#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  banner:
    charset: UTF-8
  jackson:
    time-zone: UTC
    date-format: "yyyy-MM-dd HH:mm:ss"
  cache:
    # default enable cache, you can disable by `type: none`
    type: none
    cache-names:
      - tenant
      - user
      - processDefinition
      - processTaskRelation
      - taskDefinition
    caffeine:
      spec: maximumSize=100,expireAfterWrite=300s,recordStats
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
    hikari:
      connection-test-query: select 1
      minimum-idle: 1
      auto-commit: true
      validation-timeout: 3000
      pool-name: DolphinScheduler
      maximum-pool-size: 5
      connection-timeout: 30000
      idle-timeout: 600000
      leak-detection-threshold: 0
      initialization-fail-timeout: 1
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    properties:
      org.quartz.threadPool.threadPriority: 5
      org.quartz.jobStore.isClustered: true
      org.quartz.jobStore.class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
      org.quartz.scheduler.instanceId: AUTO
      org.quartz.jobStore.tablePrefix: QRTZ_
      org.quartz.jobStore.acquireTriggersWithinLock: true
      org.quartz.scheduler.instanceName: DolphinScheduler
      org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
      org.quartz.jobStore.useProperties: false
      org.quartz.threadPool.makeThreadsDaemons: true
      org.quartz.threadPool.threadCount: 25
      org.quartz.jobStore.misfireThreshold: 60000
      org.quartz.scheduler.batchTriggerAcquisitionMaxCount: 1
      org.quartz.scheduler.makeSchedulerThreadDaemon: true
      org.quartz.jobStore.driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
      org.quartz.jobStore.clusterCheckinInterval: 5000
  redis:
    cluster:
      nodes: redis.dsg.com:7361,redis.dsg.com:7362,redis.dsg.com:7363,redis.dsg.com:7364,redis.dsg.com:7365,redis.dsg.com:7366
    password: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
    timeout: 6000
    lettuce:
      pool:
        max-idle: 10
        min-idle: 5
        max-active: 10
        max-wait: -1
  kafka:
    bootstrap-servers: kafka.dsg.com:9092
    reconnect-backoff-ms: 5000
    reconnect-backoff-max-ms: 1000
    producer:
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      enable-jaas: true
      user-jaas: admin
      pwd-jaas: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-jaas: true
      user-jaas: admin
      pwd-jaas: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)

application:
  yaml:
registry:
  type: jdbc
  term-refresh-interval: 10s
  term-expire-times: 5
  hikari-config:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
    minimum-idle: 1
    maximum-pool-size: 3
    connection-timeout: 18000
    idle-timeout: 600000

master:
  listen-port: 5678
  # master fetch command num
  fetch-command-num: 10
  # master prepare execute thread number to limit handle commands in parallel
  pre-exec-threads: 10
  # master execute thread number to limit process instances in parallel
  exec-threads: 100
  # master dispatch task number per batch, if all the tasks dispatch failed in a batch, will sleep 1s.
  dispatch-task-number: 3
  # master host selector to select a suitable worker, default value: LowerWeight. Optional values include random, round_robin, lower_weight
  host-selector: round_robin
  # master heartbeat interval
  heartbeat-interval: 10s
  # master commit task retry times
  task-commit-retry-times: 5
  # master commit task interval
  task-commit-interval: 1s
  state-wheel-interval: 5s
  # master max cpuload avg, only higher than the system cpu load average, master server can schedule. default value -1: the number of cpu cores * 2
  max-cpu-load-avg: -1
  # master reserved memory, only lower than system available memory, master server can schedule. default value 0.3, the unit is G
  reserved-memory: -1
  # failover interval, the unit is minute
  failover-interval: 10m
  # kill yarn jon when failover taskInstance, default true
  kill-yarn-job-when-task-failover: true
  registry-disconnect-strategy:
    # The disconnect strategy: stop, waiting
    strategy: waiting
    # The max waiting time to reconnect to registry if you set the strategy to waiting
    max-waiting-time: 100s
  gateway-http: http://nginx.dsg.com:8000
  joyadata-stx-home: /dsg/app/public/seatunnel
  task-monitor: false
server:
  port: 5679

management:
  server:
    port: -1
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      enabled: true
      show-details: always
  health:
    db:
      enabled: true
    defaults:
      enabled: false
  metrics:
    tags:
      application: ${spring.application.name}

metrics:
  enabled: true


jasypt:
  encryptor:
    # 加密算法
    algorithm: PBEWITHHMACSHA512ANDAES_256
    # 加密使用的盐
    password: NkVCQUQxMjBFQTI4QjY5NzVFQkYxRUNBRjEzMjc1Nzc=