/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.service.process;

import org.apache.dolphinscheduler.common.enums.AuthorizationType;
import org.apache.dolphinscheduler.common.enums.TaskGroupQueueStatus;
import org.apache.dolphinscheduler.common.graph.DAG;
import org.apache.dolphinscheduler.common.model.TaskNode;
import org.apache.dolphinscheduler.common.model.TaskNodeRelation;
import org.apache.dolphinscheduler.common.utils.CodeGenerateUtils;
import org.apache.dolphinscheduler.dao.entity.*;
import org.apache.dolphinscheduler.plugin.task.api.enums.TaskExecutionStatus;
import org.apache.dolphinscheduler.plugin.task.api.model.DateInterval;
import org.apache.dolphinscheduler.service.exceptions.CronParseException;
import org.apache.dolphinscheduler.spi.enums.ResourceType;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

public interface ProcessService {

    @Transactional
    ProcessInstance handleCommand(String host,
                                  Command command) throws CronParseException, CodeGenerateUtils.CodeGenerateException;

    void moveToErrorCommand(Command command, String message);

    int createCommand(Command command);

    int createCommandBatch(List<Command> commands);

    List<Command> findCommandPage(int pageSize, int pageNumber);

    List<Command> findCommandPageBySlot(int pageSize, int pageNumber, int masterCount, int thisMasterSlot);

    boolean verifyIsNeedCreateCommand(Command command);

    ProcessInstance findProcessInstanceDetailById(String processId);

    List<TaskDefinition> getTaskNodeListByDefinition(long defineCode);

    ProcessInstance findProcessInstanceById(String processId);

    ProcessDefinition findProcessDefineById(String processDefinitionId);

    ProcessDefinition findProcessDefinition(Long processDefinitionCode, int processDefinitionVersion);

    ProcessDefinition findProcessDefinitionByCode(Long processDefinitionCode);

    int deleteWorkProcessInstanceById(String processInstanceId);

    int deleteAllSubWorkProcessByParentId(String processInstanceId);

    void removeTaskLogFile(String processInstanceId);

    void deleteWorkTaskInstanceByProcessInstanceId(String processInstanceId);

    void recurseFindSubProcess(long parentCode, List<Long> ids);

    void createRecoveryWaitingThreadCommand(Command originCommand, ProcessInstance processInstance);

    Tenant getTenantForProcess(String tenantId, String userId);

    Environment findEnvironmentByCode(Long environmentCode);

    void setSubProcessParam(ProcessInstance subProcessInstance);

    TaskInstance submitTaskWithRetry(ProcessInstance processInstance, TaskInstance taskInstance, int commitRetryTimes,
                                     long commitInterval);

    @Transactional
    TaskInstance submitTask(ProcessInstance processInstance, TaskInstance taskInstance);

    void createSubWorkProcess(ProcessInstance parentProcessInstance, TaskInstance task);

    Map<String, String> getGlobalParamMap(String globalParams);

    Command createSubProcessCommand(ProcessInstance parentProcessInstance,
                                    ProcessInstance childInstance,
                                    ProcessInstanceMap instanceMap,
                                    TaskInstance task);

    TaskInstance submitTaskInstanceToDB(TaskInstance taskInstance, ProcessInstance processInstance);

    TaskExecutionStatus getSubmitTaskState(TaskInstance taskInstance, ProcessInstance processInstance);

    int saveCommand(Command command);

    boolean saveTaskInstance(TaskInstance taskInstance);

    boolean createTaskInstance(TaskInstance taskInstance);

    boolean updateTaskInstance(TaskInstance taskInstance);

    TaskInstance findTaskInstanceById(String taskId);

    List<TaskInstance> findTaskInstanceByIdList(List<String> idList);

    void packageTaskInstance(TaskInstance taskInstance, ProcessInstance processInstance);

    void updateTaskDefinitionResources(TaskDefinition taskDefinition);

    List<String> findTaskIdByInstanceState(String instanceId, TaskExecutionStatus state);

    List<TaskInstance> findValidTaskListByProcessId(String processInstanceId);

    //List<TaskInstance> findAllValidTaskListByProcessId(String processInstanceId);

    List<TaskInstance> findPreviousTaskListByWorkProcessId(String processInstanceId);

    int updateWorkProcessInstanceMap(ProcessInstanceMap processInstanceMap);

    int createWorkProcessInstanceMap(ProcessInstanceMap processInstanceMap);

    ProcessInstanceMap findWorkProcessMapByParent(String parentWorkProcessId, String parentTaskId);

    int deleteWorkProcessMapByParentId(String parentWorkProcessId);

    ProcessInstance findSubProcessInstance(String parentProcessId, String parentTaskId);

    ProcessInstance findParentProcessInstance(String subProcessId);

    void changeOutParam(TaskInstance taskInstance);

    Schedule querySchedule(String id);

    List<Schedule> queryReleaseSchedulerListByProcessDefinitionCode(long processDefinitionCode);

    Map<Long, String> queryWorkerGroupByProcessDefinitionCodes(List<Long> processDefinitionCodeList);

    List<DependentProcessDefinition> queryDependentProcessDefinitionByProcessDefinitionCode(long processDefinitionCode);

    List<ProcessInstance> queryNeedFailoverProcessInstances(String host);

    List<String> queryNeedFailoverProcessInstanceHost();

    @Transactional
    void processNeedFailoverProcessInstances(ProcessInstance processInstance);

    List<TaskInstance> queryNeedFailoverTaskInstances(String host);

    DataSource findDataSourceById(String id);

    ProcessInstance findProcessInstanceByTaskId(String taskId);

    List<UdfFunc> queryUdfFunListByIds(String[] ids);

    String queryTenantCodeByResName(String resName, ResourceType resourceType);

    List<Schedule> selectAllByProcessDefineCode(long[] codes);

    ProcessInstance findLastSchedulerProcessInterval(Long definitionCode, DateInterval dateInterval, int testFalg);

    ProcessInstance findLastManualProcessInterval(Long definitionCode, DateInterval dateInterval, int testFlag);

    ProcessInstance findLastRunningProcess(Long definitionCode, Date startTime, Date endTime, int testFlag);

    String queryUserQueueByProcessInstance(ProcessInstance processInstance);

    ProjectUser queryProjectWithUserByProcessInstanceId(String processInstanceId);

    String getTaskWorkerGroup(TaskInstance taskInstance);

    List<Project> getProjectListHavePerm(String userId);

    <T> List<T> listUnauthorized(String userId, T[] needChecks, AuthorizationType authorizationType);

    User getUserById(String userId);

    Resource getResourceById(String resourceId);

    List<Resource> listResourceByIds(String[] resIds);

    String formatTaskAppId(TaskInstance taskInstance);

    int switchVersion(ProcessDefinition processDefinition, ProcessDefinitionLog processDefinitionLog);

    int switchProcessTaskRelationVersion(ProcessDefinition processDefinition);

    int switchTaskDefinitionVersion(long taskCode, int taskVersion);

    String getResourceIds(TaskDefinition taskDefinition);

    String getResourceIds(TaskExternalDefinition taskDefinition);

    int saveTaskDefine(User operator, long projectCode, List<TaskDefinitionLog> taskDefinitionLogs, Boolean syncDefine, int updateVersion);

    int saveTaskExternalDefine(User operator, List<TaskExternalDefinition> taskExternalDefinitions, Boolean syncDefine);

    int saveProcessDefine(User operator, ProcessDefinition processDefinition, Boolean syncDefine,
                          Boolean isFromProcessDefine, int updateVersion);

    int saveTaskRelation(User operator, long projectCode, long processDefinitionCode, int processDefinitionVersion,
                         List<ProcessTaskRelationLog> taskRelationList, List<TaskDefinitionLog> taskDefinitionLogs,
                         Boolean syncDefine, int updateVersion);

    boolean isTaskOnline(long taskCode);

    DAG<String, TaskNode, TaskNodeRelation> genDagGraph(ProcessDefinition processDefinition);

    DagData genDagData(ProcessDefinition processDefinition);

    List<TaskDefinitionLog> genTaskDefineList(List<ProcessTaskRelation> processTaskRelations);

    List<TaskDefinitionLog> getTaskDefineLogListByRelation(List<ProcessTaskRelation> processTaskRelations);

    TaskDefinition findTaskDefinition(long taskCode, int taskDefinitionVersion);

    List<ProcessTaskRelation> findRelationByCode(long processDefinitionCode, int processDefinitionVersion);

    List<TaskNode> transformTask(List<ProcessTaskRelation> taskRelationList,
                                 List<TaskDefinitionLog> taskDefinitionLogs);

    Map<ProcessInstance, TaskInstance> notifyProcessList(String processId);

    DqExecuteResult getDqExecuteResultByTaskInstanceId(String taskInstanceId);

    int updateDqExecuteResultUserId(String taskInstanceId);

    int updateDqExecuteResultState(DqExecuteResult dqExecuteResult);

    int deleteDqExecuteResultByTaskInstanceId(String taskInstanceId);

    int deleteTaskStatisticsValueByTaskInstanceId(String taskInstanceId);

    DqRule getDqRule(String ruleId);

    List<DqRuleInputEntry> getRuleInputEntry(String ruleId);

    List<DqRuleExecuteSql> getDqExecuteSql(String ruleId);

    DqComparisonType getComparisonTypeById(String id);

    boolean acquireTaskGroup(String taskId,
                             String taskName, String groupId,
                             String processId, int priority);

    boolean robTaskGroupResource(TaskGroupQueue taskGroupQueue);

    void releaseAllTaskGroup(String processInstanceId);

    TaskInstance releaseTaskGroup(TaskInstance taskInstance);

    void changeTaskGroupQueueStatus(String taskId, TaskGroupQueueStatus status);

    TaskGroupQueue insertIntoTaskGroupQueue(String taskId,
                                            String taskName, String groupId,
                                            String processId, Integer priority, TaskGroupQueueStatus status);

    int updateTaskGroupQueueStatus(String taskId, int status);

    int updateTaskGroupQueue(TaskGroupQueue taskGroupQueue);

    TaskGroupQueue loadTaskGroupQueue(String taskId);

    void sendStartTask2Master(ProcessInstance processInstance, String taskId,
                              org.apache.dolphinscheduler.remote.command.CommandType taskType);

    ProcessInstance loadNextProcess4Serial(long code, int state, String id);

    public String findConfigYamlByName(String clusterName);

    void forceProcessInstanceSuccessByTaskInstanceId(String taskInstanceId);
}
