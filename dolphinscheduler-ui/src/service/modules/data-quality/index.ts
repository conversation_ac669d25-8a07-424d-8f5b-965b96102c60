/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { axios } from '@/service/service'
import type { RuleListReq, ResultListReq } from './types'

export function queryRuleListPaging(params: RuleListReq): any {
  return axios({
    url: '/data-quality/rule/page',
    method: 'get',
    params
  })
}

export function queryExecuteResultListPaging(params: ResultListReq): any {
  return axios({
    url: '/data-quality/result/page',
    method: 'get',
    params
  })
}

export function queryRuleList(): any {
  return axios({
    url: '/data-quality/ruleList',
    method: 'get'
  })
}

export function getRuleFormCreateJson(ruleId: number): any {
  return axios({
    url: '/data-quality/getRuleFormCreateJson',
    method: 'get',
    params: {
      ruleId
    }
  })
}

export function getDatasourceOptionsById(datasourceId: number): any {
  return axios({
    url: '/data-quality/getDatasourceOptionsById',
    method: 'get',
    params: {
      datasourceId
    }
  })
}
