/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useI18n } from 'vue-i18n'
import type { IJsonItem } from '../types'

export function useFailed(): IJsonItem[] {
  const { t } = useI18n()
  return [
    {
      type: 'input-number',
      field: 'failRetryTimes',
      name: t('project.node.number_of_failed_retries'),
      span: 12,
      props: {
        min: 0
      },
      slots: {
        suffix: () => t('project.node.times')
      }
    },
    {
      type: 'input-number',
      field: 'failRetryInterval',
      name: t('project.node.failed_retry_interval'),
      span: 12,
      props: {
        min: 0
      },
      slots: {
        suffix: () => t('project.node.minute')
      }
    }
  ]
}
