/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { reactive, ref } from 'vue'

export function useForm() {
  const state = reactive({
    formRef: ref()
  })

  const validate = async (...args: []) => {
    await state.formRef.validate(...args)
  }

  const resetValues = (initialValues: { [field: string]: any }) => {
    const modelKeys = Object.keys(state.formRef.model)
    for (const key of modelKeys) {
      if (!Object.keys(initialValues).includes(key)) {
        delete state.formRef.model[key]
      }
    }

    setValues(initialValues)
  }

  const setValues = (initialValues: { [field: string]: any }) => {
    for (const [key, value] of Object.entries(initialValues)) {
      state.formRef.model[key] = value
    }
  }

  const restoreValidation = () => {
    state.formRef.restoreValidation()
  }

  const getValues = () => {
    return state.formRef.model
  }

  return {
    state,
    validate,
    setValues,
    getValues,
    resetValues,
    restoreValidation
  }
}
