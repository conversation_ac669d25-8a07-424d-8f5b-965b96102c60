{"name": "dolphin<PERSON><PERSON>ler-ui", "version": "0.0.0", "scripts": {"dev": "vite", "build:prod": "vue-tsc --noEmit && vite build --mode production", "preview": "vite preview", "lint": "eslint src --fix --ext .ts,.tsx,.vue", "prettier": "prettier --write \"src/**/*.{vue,ts,tsx}\""}, "dependencies": {"@antv/layout": "0.1.31", "@antv/x6": "^1.34.1", "@vueuse/core": "^9.2.0", "axios": "^0.27.2", "date-fns": "^2.29.3", "date-fns-tz": "^1.3.7", "echarts": "^5.3.3", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "monaco-editor": "^0.34.0", "naive-ui": "2.30.7", "nprogress": "^0.2.0", "pinia": "^2.0.22", "pinia-plugin-persistedstate": "^2.2.0", "qs": "^6.11.0", "screenfull": "^6.0.2", "vfonts": "^0.0.3", "vue": "^3.2.39", "vue-i18n": "^9.2.2", "vue-router": "^4.1.5"}, "devDependencies": {"@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.185", "@types/node": "^18.7.18", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "@vicons/antd": "^0.12.0", "@vitejs/plugin-vue": "^3.1.0", "@vitejs/plugin-vue-jsx": "^2.0.1", "dart-sass": "^1.25.0", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.5.1", "prettier": "^2.7.1", "sass": "^1.54.9", "sass-loader": "^13.0.2", "typescript": "^4.8.3", "typescript-plugin-css-modules": "^3.4.0", "vite": "^3.1.2", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^0.40.13"}}