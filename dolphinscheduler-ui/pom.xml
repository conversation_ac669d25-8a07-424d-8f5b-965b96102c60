<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.dolphinscheduler</groupId>
        <artifactId>dolphinscheduler</artifactId>
        <version>3.2.2</version>
    </parent>

    <artifactId>dolphinscheduler-ui</artifactId>

    <name>${project.artifactId}</name>

    <properties>
        <node.version>v16.13.1</node.version>
        <pnpm.version>v6.32.6</pnpm.version>
        <sonar.sources>src</sonar.sources>
        <frontend-maven-plugin.version>1.12.1</frontend-maven-plugin.version>
    </properties>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>${frontend-maven-plugin.version}</version>
                        <configuration>
                            <pnpmInheritsProxyConfigFromMaven>false</pnpmInheritsProxyConfigFromMaven>
                        </configuration>
                        <executions>
                            <execution>
                                <id>install node and pnpm</id>
                                <goals>
                                    <goal>install-node-and-pnpm</goal>
                                </goals>
                                <configuration>
                                    <nodeVersion>${node.version}</nodeVersion>
                                    <pnpmVersion>${pnpm.version}</pnpmVersion>
                                </configuration>
                            </execution>
                            <execution>
                                <id>pnpm install</id>
                                <goals>
                                    <goal>pnpm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>install</arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>pnpm run build:prod</id>
                                <goals>
                                    <goal>pnpm</goal>
                                </goals>
                                <configuration>
                                    <arguments>run build:prod</arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>

            </build>
        </profile>
    </profiles>
</project>
