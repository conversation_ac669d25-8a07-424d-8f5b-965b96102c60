# 最终解决方案：彻底消除 "文本文件忙" 错误

## 🎯 问题定位

通过日志分析发现，"文本文件忙" 错误现在发生在 `_node.sh` 文件上：

```
/dsg/app/public/dolphinscheduler/tmp_file/exec/process/.../74962541157696_74962542632768_node.sh: 文本文件忙
```

**根本原因**：海豚调度器创建了两个文件：
1. `xxx.command` (主命令文件) - 已通过 `ShellCommandExecutor` 修复
2. `xxx_node.sh` (节点脚本文件) - 在 `ShellTask.buildCommand()` 中创建，仍使用原始方法

## 🔧 最终修复方案

### 修复的文件和方法

#### 1. AbstractCommandExecutor.java ✅
- `buildProcess()` - 通过解释器执行 + 重试机制
- `ensureFileWriteComplete()` - 文件状态验证
- 所有 `run()` 方法 - 添加文件检查

#### 2. ShellCommandExecutor.java ✅  
- `createCommandFileIfNotExists()` - 调用安全创建方法
- `createCommandFileSafely()` - 原子性文件创建

#### 3. ShellTask.java 🆕 (新修复)
- `buildCommand()` - 替换原始文件创建
- `createNodeScriptFileSafely()` - 原子性创建节点脚本

### 核心技术方案

#### 原子性文件创建流程
```java
// 1. 写入临时文件
Path tempPath = Paths.get(fileName + ".tmp." + timestamp);
Files.write(tempPath, content.getBytes(), StandardOpenOption.CREATE);

// 2. 设置权限并同步
Files.setPosixFilePermissions(tempPath, permissions);
FileChannel.force(true);

// 3. 原子性移动 (关键步骤!)
Files.move(tempPath, targetPath, StandardCopyOption.ATOMIC_MOVE);
```

#### 通过解释器执行
```java
// 旧方式：直接执行 (容易出错)
command.add(commandFile);

// 新方式：通过解释器 (安全可靠)  
command.add("sh");
command.add(commandFile);
```

## 📊 修复覆盖范围

### 文件创建路径
- ✅ **主命令文件** (`xxx.command`) - `ShellCommandExecutor.createCommandFileIfNotExists()`
- ✅ **节点脚本文件** (`xxx_node.sh`) - `ShellTask.buildCommand()`
- ✅ **取消命令文件** (`xxxcancel.command`) - `AbstractCommandExecutor.cancelSeatunnelApplication()`

### 执行路径
- ✅ **普通Shell任务** - `AbstractCommandExecutor.run(String)`
- ✅ **STX任务** - `AbstractCommandExecutor.run(String, StringRedisTemplate, String)`
- ✅ **任务取消** - `AbstractCommandExecutor.cancelSeatunnelApplication()`

### 进程启动
- ✅ **重试机制** - 处理临时文件锁定
- ✅ **解释器执行** - 避免直接执行脚本文件
- ✅ **详细日志** - 便于问题排查

## 🎉 预期效果

### 修复前的错误
```
[ERROR] Cannot run program "xxx.command": error=26, 文本文件忙
[ERROR] xxx_node.sh: 文本文件忙
```

### 修复后的正常流程
```
[INFO] 开始安全创建命令文件: xxx.command
[INFO] 命令文件创建成功: xxx.command
[INFO] 开始安全创建节点脚本文件: xxx_node.sh  
[INFO] 节点脚本文件创建成功: xxx_node.sh
[INFO] 生成的command=[sh, xxx.command]
[INFO] 进程启动成功，重试次数: 0
```

## 🔍 技术原理

### 1. 原子性操作的重要性
- **无中间状态**：文件要么完全可用，要么完全不存在
- **系统级保证**：由操作系统保证原子性
- **消除竞态条件**：避免文件正在写入时被执行

### 2. 临时文件 + 原子移动策略
```bash
# 传统方式 (有问题)
echo "content" > script.sh
chmod +x script.sh
./script.sh  # 可能出现 "文本文件忙"

# 原子性方式 (安全)
echo "content" > script.sh.tmp
chmod +x script.sh.tmp
mv script.sh.tmp script.sh  # 原子性操作
sh script.sh  # 通过解释器执行
```

### 3. 解释器执行的优势
- **避免文件锁定**：解释器读取文件，不需要执行权限检查
- **更好兼容性**：适用于各种系统环境
- **错误处理**：提供更详细的错误信息

## 🚀 部署验证

### 1. 编译部署
```bash
# 编译修改的模块
mvn clean compile -pl dolphinscheduler-task-plugin/dolphinscheduler-task-api
mvn clean compile -pl dolphinscheduler-task-plugin/dolphinscheduler-task-shell

# 重启服务
systemctl restart dolphinscheduler-worker
```

### 2. 测试验证
```bash
# 创建简单的Shell任务
echo "sleep 1; echo 'test'" 

# 并发测试
for i in {1..10}; do
  (submit_shell_task &)
done

# 观察日志
tail -f worker.log | grep -E "(文本文件忙|Text file busy|安全创建|原子性)"
```

### 3. 监控指标
- **任务启动成功率** - 应达到 99.9%+
- **文件创建耗时** - 应在合理范围内
- **错误日志频率** - "文本文件忙" 错误应为 0

## 💡 额外建议

### 1. 系统优化
```bash
# 文件系统优化
echo 'vm.dirty_ratio = 5' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 2' >> /etc/sysctl.conf
sysctl -p

# 文件描述符优化
ulimit -n 65536
```

### 2. 监控告警
- 监控 "文本文件忙" 错误频率
- 监控任务启动失败率
- 监控文件创建耗时

### 3. 日志配置
```xml
<!-- 生产环境：INFO级别 -->
<logger name="org.apache.dolphinscheduler.plugin.task" level="INFO"/>

<!-- 调试环境：DEBUG级别 -->
<logger name="org.apache.dolphinscheduler.plugin.task" level="DEBUG"/>
```

## 🎯 总结

这个最终解决方案：

1. **全面覆盖**：修复了所有文件创建路径
2. **根本解决**：使用原子性操作消除竞态条件  
3. **安全可靠**：通过解释器执行避免文件锁定
4. **向后兼容**：不影响现有功能
5. **易于维护**：代码结构清晰，日志详细

应该能够彻底解决 "文本文件忙" 问题，提供稳定可靠的任务执行环境。
