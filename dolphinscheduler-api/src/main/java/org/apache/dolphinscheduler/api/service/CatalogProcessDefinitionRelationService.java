package org.apache.dolphinscheduler.api.service;

import org.apache.dolphinscheduler.dao.entity.CatalogProcessDefinitionRelation;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: CatalogProcessDefinitionRelationService
 * @date 2023/10/8
 */
public interface CatalogProcessDefinitionRelationService {
    List<Long> getProcessDefinitionCodes(String catalogId);

    int insert(CatalogProcessDefinitionRelation catalogProcessDefinitionRelation);

    int deleteByProcessDefinitionCode(long processDefinitionCode,  long projectCode);

    int updateByProcessDefinitionCode(long processDefinitionCode, CatalogProcessDefinitionRelation catalogProcessDefinitionRelation, long projectCode);
}
