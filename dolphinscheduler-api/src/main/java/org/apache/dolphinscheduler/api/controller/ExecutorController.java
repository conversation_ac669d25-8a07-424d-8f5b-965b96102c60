/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.api.controller;

import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dolphinscheduler.api.aspect.AccessLogAnnotation;
import org.apache.dolphinscheduler.api.enums.ExecuteType;
import org.apache.dolphinscheduler.api.enums.Status;
import org.apache.dolphinscheduler.api.exceptions.ApiException;
import org.apache.dolphinscheduler.api.service.ExecutorService;
import org.apache.dolphinscheduler.api.utils.Result;
import org.apache.dolphinscheduler.common.Constants;
import org.apache.dolphinscheduler.common.enums.*;
import org.apache.dolphinscheduler.common.utils.JSONUtils;
import org.apache.dolphinscheduler.dao.entity.User;
import org.apache.dolphinscheduler.remote.dto.WorkflowExecuteDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.dolphinscheduler.api.enums.Status.*;

/**
 * executor controller
 */
@Api(tags = "EXECUTOR_TAG")
@RestController
@RequestMapping("projects/{projectCode}/executors")
public class ExecutorController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ProcessInstanceController.class);

    @Autowired
    private ExecutorService execService;

    /**
     * execute process instance
     *
     * @param loginUser                 login user
     * @param projectCode               project code
     * @param processDefinitionCode     process definition code
     * @param scheduleTime              schedule time when CommandType is COMPLEMENT_DATA  there are two ways to transfer parameters 1.date range, for example:{"complementStartDate":"2022-01-01 12:12:12","complementEndDate":"2022-01-6 12:12:12"} 2.manual input,  for example:{"complementScheduleDateList":"2022-01-01 00:00:00,2022-01-02 12:12:12,2022-01-03 12:12:12"}
     * @param failureStrategy           failure strategy
     * @param startNodeList             start nodes list
     * @param taskDependType            task depend type
     * @param execType                  execute type
     * @param warningType               warning type
     * @param warningGroupId            warning group id
     * @param runMode                   run mode
     * @param processInstancePriority   process instance priority
     * @param workerGroup               worker group
     * @param timeout                   timeout
     * @param expectedParallelismNumber the expected parallelism number when execute complement in parallel mode
     * @return start process result code
     */
    @ApiOperation(value = "startProcessInstance", notes = "RUN_PROCESS_INSTANCE_NOTES")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processDefinitionCode", value = "PROCESS_DEFINITION_CODE", required = true, dataTypeClass = long.class, example = "100"),
            @ApiImplicitParam(name = "scheduleTime", value = "SCHEDULE_TIME", required = true, dataTypeClass = String.class, example = "2022-04-06 00:00:00,2022-04-06 00:00:00"),
            @ApiImplicitParam(name = "failureStrategy", value = "FAILURE_STRATEGY", required = true, dataTypeClass = FailureStrategy.class),
            @ApiImplicitParam(name = "startNodeList", value = "START_NODE_LIST", dataTypeClass = String.class),
            @ApiImplicitParam(name = "taskDependType", value = "TASK_DEPEND_TYPE", dataTypeClass = TaskDependType.class),
            @ApiImplicitParam(name = "execType", value = "COMMAND_TYPE", dataTypeClass = CommandType.class),
            @ApiImplicitParam(name = "warningType", value = "WARNING_TYPE", required = true, dataTypeClass = WarningType.class),
            @ApiImplicitParam(name = "warningGroupId", value = "WARNING_GROUP_ID", dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "runMode", value = "RUN_MODE", dataTypeClass = RunMode.class),
            @ApiImplicitParam(name = "processInstancePriority", value = "PROCESS_INSTANCE_PRIORITY", required = true, dataTypeClass = Priority.class),
            @ApiImplicitParam(name = "workerGroup", value = "WORKER_GROUP", dataTypeClass = String.class, example = "default"),
            @ApiImplicitParam(name = "environmentCode", value = "ENVIRONMENT_CODE", dataTypeClass = long.class, example = "-1"),
            @ApiImplicitParam(name = "timeout", value = "TIMEOUT", dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "expectedParallelismNumber", value = "EXPECTED_PARALLELISM_NUMBER", dataTypeClass = int.class, example = "8"),
            @ApiImplicitParam(name = "dryRun", value = "DRY_RUN", dataTypeClass = int.class, example = "0"),
            @ApiImplicitParam(name = "complementDependentMode", value = "COMPLEMENT_DEPENDENT_MODE", dataTypeClass = ComplementDependentMode.class)
    })
    @PostMapping(value = "start-process-instance")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(START_PROCESS_INSTANCE_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result startProcessInstance(@ApiIgnore @RequestAttribute(value = Constants.SESSION_USER) User loginUser,
                                       @ApiParam(name = "projectCode", value = "PROJECT_CODE", required = true) @PathVariable long projectCode,
                                       @RequestParam(value = "processDefinitionCode") long processDefinitionCode,
                                       @RequestParam(value = "scheduleTime") String scheduleTime,
                                       @RequestParam(value = "failureStrategy") FailureStrategy failureStrategy,
                                       @RequestParam(value = "startNodeList", required = false) String startNodeList,
                                       @RequestParam(value = "taskDependType", required = false) TaskDependType taskDependType,
                                       @RequestParam(value = "execType", required = false) CommandType execType,
                                       @RequestParam(value = "warningType") WarningType warningType,
                                       @RequestParam(value = "warningGroupId", required = false, defaultValue = "0") String warningGroupId,
                                       @RequestParam(value = "runMode", required = false) RunMode runMode,
                                       @RequestParam(value = "processInstancePriority", required = false) Priority processInstancePriority,
                                       @RequestParam(value = "workerGroup", required = false, defaultValue = "default") String workerGroup,
                                       @RequestParam(value = "environmentCode", required = false, defaultValue = "-1") Long environmentCode,
                                       @RequestParam(value = "timeout", required = false) Integer timeout,
                                       @RequestParam(value = "startParams", required = false) String startParams,
                                       @RequestParam(value = "expectedParallelismNumber", required = false) Integer expectedParallelismNumber,
                                       @RequestParam(value = "dryRun", defaultValue = "0", required = false) int dryRun,
                                       @RequestParam(value = "testFlag", defaultValue = "0") int testFlag,
                                       @RequestParam(value = "complementDependentMode", required = false) ComplementDependentMode complementDependentMode,
                                       @RequestParam(value = "returnInstanceId", required = false, defaultValue = "false") Boolean returnInstanceId) {

        if (timeout == null) {
            timeout = Constants.MAX_TASK_TIMEOUT;
        }
        Map<String, String> startParamMap = null;
        if (startParams != null) {
            startParamMap = JSONUtils.toMap(startParams);
        }

        if (complementDependentMode == null) {
            complementDependentMode = ComplementDependentMode.OFF_MODE;
        }

        Map<String, Object> result = execService.execProcessInstance(loginUser, projectCode, processDefinitionCode,
                scheduleTime, execType, failureStrategy,
                startNodeList, taskDependType, warningType, warningGroupId, runMode, processInstancePriority,
                workerGroup, environmentCode, timeout, startParamMap, expectedParallelismNumber, dryRun, testFlag,
                complementDependentMode, returnInstanceId);
        return returnDataList(result);
    }

    /**
     * batch execute process instance
     * If any processDefinitionCode cannot be found, the failure information is returned and the status is set to
     * failed. The successful task will run normally and will not stop
     *
     * @param loginUser                 login user
     * @param projectCode               project code
     * @param processDefinitionCodes    process definition codes
     * @param scheduleTime              schedule time
     * @param failureStrategy           failure strategy
     * @param startNodeList             start nodes list
     * @param taskDependType            task depend type
     * @param execType                  execute type
     * @param warningType               warning type
     * @param warningGroupId            warning group id
     * @param runMode                   run mode
     * @param processInstancePriority   process instance priority
     * @param workerGroup               worker group
     * @param timeout                   timeout
     * @param expectedParallelismNumber the expected parallelism number when execute complement in parallel mode
     * @return start process result code
     */
    @ApiOperation(value = "batchStartProcessInstance", notes = "BATCH_RUN_PROCESS_INSTANCE_NOTES")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processDefinitionCodes", value = "PROCESS_DEFINITION_CODES", required = true, dataTypeClass = String.class, example = "1,2,3"),
            @ApiImplicitParam(name = "scheduleTime", value = "SCHEDULE_TIME", required = true, dataTypeClass = String.class, example = "2022-04-06 00:00:00,2022-04-06 00:00:00"),
            @ApiImplicitParam(name = "failureStrategy", value = "FAILURE_STRATEGY", required = true, dataTypeClass = FailureStrategy.class),
            @ApiImplicitParam(name = "startNodeList", value = "START_NODE_LIST", dataTypeClass = String.class),
            @ApiImplicitParam(name = "taskDependType", value = "TASK_DEPEND_TYPE", dataTypeClass = TaskDependType.class),
            @ApiImplicitParam(name = "execType", value = "COMMAND_TYPE", dataTypeClass = CommandType.class),
            @ApiImplicitParam(name = "warningType", value = "WARNING_TYPE", required = true, dataTypeClass = WarningType.class),
            @ApiImplicitParam(name = "warningGroupId", value = "WARNING_GROUP_ID", required = true, dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "runMode", value = "RUN_MODE", dataTypeClass = RunMode.class),
            @ApiImplicitParam(name = "processInstancePriority", value = "PROCESS_INSTANCE_PRIORITY", required = true, dataTypeClass = Priority.class),
            @ApiImplicitParam(name = "workerGroup", value = "WORKER_GROUP", dataTypeClass = String.class, example = "default"),
            @ApiImplicitParam(name = "environmentCode", value = "ENVIRONMENT_CODE", dataTypeClass = long.class, example = "-1"),
            @ApiImplicitParam(name = "timeout", value = "TIMEOUT", dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "expectedParallelismNumber", value = "EXPECTED_PARALLELISM_NUMBER", dataTypeClass = int.class, example = "8"),
            @ApiImplicitParam(name = "dryRun", value = "DRY_RUN", dataTypeClass = int.class, example = "0"),
            @ApiImplicitParam(name = "testFlag", value = "TEST_FLAG", dataTypeClass = int.class, example = "0"),
            @ApiImplicitParam(name = "complementDependentMode", value = "COMPLEMENT_DEPENDENT_MODE", dataTypeClass = ComplementDependentMode.class)
    })
    @PostMapping(value = "batch-start-process-instance")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(START_PROCESS_INSTANCE_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result batchStartProcessInstance(@ApiIgnore @RequestAttribute(value = Constants.SESSION_USER) User loginUser,
                                            @ApiParam(name = "projectCode", value = "PROJECT_CODE", required = true) @PathVariable long projectCode,
                                            @RequestParam(value = "processDefinitionCodes") String processDefinitionCodes,
                                            @RequestParam(value = "scheduleTime") String scheduleTime,
                                            @RequestParam(value = "failureStrategy") FailureStrategy failureStrategy,
                                            @RequestParam(value = "startNodeList", required = false) String startNodeList,
                                            @RequestParam(value = "taskDependType", required = false) TaskDependType taskDependType,
                                            @RequestParam(value = "execType", required = false) CommandType execType,
                                            @RequestParam(value = "warningType") WarningType warningType,
                                            @RequestParam(value = "warningGroupId", required = false) String warningGroupId,
                                            @RequestParam(value = "runMode", required = false) RunMode runMode,
                                            @RequestParam(value = "processInstancePriority", required = false) Priority processInstancePriority,
                                            @RequestParam(value = "workerGroup", required = false, defaultValue = "default") String workerGroup,
                                            @RequestParam(value = "environmentCode", required = false, defaultValue = "-1") Long environmentCode,
                                            @RequestParam(value = "timeout", required = false) Integer timeout,
                                            @RequestParam(value = "startParams", required = false) String startParams,
                                            @RequestParam(value = "expectedParallelismNumber", required = false) Integer expectedParallelismNumber,
                                            @RequestParam(value = "dryRun", defaultValue = "0", required = false) int dryRun,
                                            @RequestParam(value = "testFlag", defaultValue = "0") int testFlag,
                                            @RequestParam(value = "complementDependentMode", required = false) ComplementDependentMode complementDependentMode) {

        if (timeout == null) {
            timeout = Constants.MAX_TASK_TIMEOUT;
        }

        Map<String, String> startParamMap = null;
        if (startParams != null) {
            startParamMap = JSONUtils.toMap(startParams);
        }

        if (complementDependentMode == null) {
            complementDependentMode = ComplementDependentMode.OFF_MODE;
        }

        Map<String, Object> result = new HashMap<>();
        List<String> processDefinitionCodeArray = Arrays.asList(processDefinitionCodes.split(Constants.COMMA));
        List<String> startFailedProcessDefinitionCodeList = new ArrayList<>();

        processDefinitionCodeArray = processDefinitionCodeArray.stream().distinct().collect(Collectors.toList());

        for (String strProcessDefinitionCode : processDefinitionCodeArray) {
            long processDefinitionCode = Long.parseLong(strProcessDefinitionCode);
            result = execService.execProcessInstance(loginUser, projectCode, processDefinitionCode, scheduleTime,
                    execType, failureStrategy,
                    startNodeList, taskDependType, warningType, warningGroupId, runMode, processInstancePriority,
                    workerGroup, environmentCode, timeout, startParamMap, expectedParallelismNumber, dryRun, testFlag,
                    complementDependentMode, false);

            if (!Status.SUCCESS.equals(result.get(Constants.STATUS))) {
                startFailedProcessDefinitionCodeList.add(String.valueOf(processDefinitionCode));
            }
        }

        if (!startFailedProcessDefinitionCodeList.isEmpty()) {
            putMsg(result, Status.BATCH_START_PROCESS_INSTANCE_ERROR,
                    String.join(Constants.COMMA, startFailedProcessDefinitionCodeList));
        }

        return returnDataList(result);
    }

    /**
     * do action to process instance: pause, stop, repeat, recover from pause, recover from stop
     *
     * @param loginUser         login user
     * @param projectCode       project code
     * @param processInstanceId process instance id
     * @param executeType       execute type
     * @return execute result code
     */
    @ApiOperation(value = "execute", notes = "EXECUTE_ACTION_TO_PROCESS_INSTANCE_NOTES")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "PROCESS_INSTANCE_ID", required = true, dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "executeType", value = "EXECUTE_TYPE", required = true, dataTypeClass = ExecuteType.class)
    })
    @PostMapping(value = "/execute")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(EXECUTE_PROCESS_INSTANCE_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result execute(@ApiIgnore @RequestAttribute(value = Constants.SESSION_USER) User loginUser,
                          @ApiParam(name = "projectCode", value = "PROJECT_CODE", required = true) @PathVariable long projectCode,
                          @RequestParam("processInstanceId") String processInstanceId,
                          @RequestParam("executeType") ExecuteType executeType,
                          @RequestParam(value = "startParams", required = false) String startParams) {
        //20250325 新增参数startParams。默认是不支持startParams,这里如果有传递，会覆盖从工作流查询出来的参数。
        Map<String, Object> result = execService.execute(loginUser, projectCode, processInstanceId, executeType,startParams);
        return returnDataList(result);
    }

    /**
     * batch execute and do action to process instance
     *
     * @param loginUser          login user
     * @param projectCode        project code
     * @param processInstanceIds process instance ids, delimiter by "," if more than one id
     * @param executeType        execute type
     * @return execute result code
     */
    @ApiOperation(value = "batchExecute", notes = "BATCH_EXECUTE_ACTION_TO_PROCESS_INSTANCE_NOTES")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectCode", value = "PROJECT_CODE", required = true, dataTypeClass = int.class),
            @ApiImplicitParam(name = "processInstanceIds", value = "PROCESS_INSTANCE_IDS", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "executeType", value = "EXECUTE_TYPE", required = true, dataTypeClass = ExecuteType.class)
    })
    @PostMapping(value = "/batch-execute")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(BATCH_EXECUTE_PROCESS_INSTANCE_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result batchExecute(@RequestAttribute(value = Constants.SESSION_USER) User loginUser,
                               @PathVariable long projectCode,
                               @RequestParam("processInstanceIds") String processInstanceIds,
                               @RequestParam("executeType") ExecuteType executeType,
                               @RequestParam(value = "startParams", required = false) String startParams) {
        Map<String, Object> result = new HashMap<>();
        List<String> executeFailedIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(processInstanceIds)) {
            String[] processInstanceIdArray = processInstanceIds.split(Constants.COMMA);

            for (String strProcessInstanceId : processInstanceIdArray) {
                String processInstanceId = strProcessInstanceId;
                try {
                    Map<String, Object> singleResult =
                            execService.execute(loginUser, projectCode, processInstanceId, executeType,startParams);
                    if (!Status.SUCCESS.equals(singleResult.get(Constants.STATUS))) {
                        executeFailedIdList.add((String) singleResult.get(Constants.MSG));
                        logger.error((String) singleResult.get(Constants.MSG));
                    }
                } catch (Exception e) {
                    executeFailedIdList
                            .add(MessageFormat.format(Status.PROCESS_INSTANCE_ERROR.getMsg(), strProcessInstanceId));
                }
            }
        }
        if (!executeFailedIdList.isEmpty()) {
            putMsg(result, Status.BATCH_EXECUTE_PROCESS_INSTANCE_ERROR, String.join("\n", executeFailedIdList));
        } else {
            putMsg(result, Status.SUCCESS);
        }
        return returnDataList(result);
    }

    /**
     * check process definition and all the son process definitions is online.
     *
     * @param processDefinitionCode process definition code
     * @return check result code
     */
    @ApiOperation(value = "startCheckProcessDefinition", notes = "START_CHECK_PROCESS_DEFINITION_NOTES")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processDefinitionCode", value = "PROCESS_DEFINITION_CODE", required = true, dataTypeClass = long.class, example = "100")
    })
    @PostMapping(value = "/start-check")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(CHECK_PROCESS_DEFINITION_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result startCheckProcessDefinition(@RequestParam(value = "processDefinitionCode") long processDefinitionCode) {
        Map<String, Object> result = execService.startCheckByProcessDefinedCode(processDefinitionCode);
        return returnDataList(result);
    }

    /**
     * query execute data of processInstance from master
     */
    @ApiOperation(value = "queryExecutingWorkflow", notes = "QUERY_WORKFLOW_EXECUTE_DATA")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "PROCESS_INSTANCE_ID", required = true, dataTypeClass = int.class, example = "100")
    })
    @GetMapping(value = "/query-executing-workflow")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(QUERY_EXECUTING_WORKFLOW_ERROR)
    @AccessLogAnnotation
    public Result queryExecutingWorkflow(@RequestParam("id") String processInstanceId) {
        WorkflowExecuteDto workflowExecuteDto =
                execService.queryExecutingWorkflowByProcessInstanceId(processInstanceId);
        return Result.success(workflowExecuteDto);
    }

    /**
     * execute task instance
     *
     * @param loginUser      login user
     * @param projectCode    project code
     * @param code           taskDefinitionCode
     * @param version        taskDefinitionVersion
     * @param warningGroupId warning group id
     * @param workerGroup    worker group
     * @return start task result code
     */
    @ApiOperation(value = "startTaskInstance", notes = "RUN_TASK_INSTANCE_NOTES")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "version", value = "VERSION", dataTypeClass = int.class, example = "1"),
            @ApiImplicitParam(name = "failureStrategy", value = "FAILURE_STRATEGY", required = true, dataTypeClass = FailureStrategy.class),
            @ApiImplicitParam(name = "execType", value = "COMMAND_TYPE", dataTypeClass = CommandType.class),
            @ApiImplicitParam(name = "warningType", value = "WARNING_TYPE", required = true, dataTypeClass = WarningType.class),
            @ApiImplicitParam(name = "warningGroupId", value = "WARNING_GROUP_ID", dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "workerGroup", value = "WORKER_GROUP", dataTypeClass = String.class, example = "default"),
            @ApiImplicitParam(name = "environmentCode", value = "ENVIRONMENT_CODE", dataTypeClass = long.class, example = "-1"),
            @ApiImplicitParam(name = "timeout", value = "TIMEOUT", dataTypeClass = int.class, example = "100"),
            @ApiImplicitParam(name = "dryRun", value = "DRY_RUN", dataTypeClass = int.class, example = "0"),
    })
    @PostMapping(value = "/task-instance/{code}/start")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(START_PROCESS_INSTANCE_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result startStreamTaskInstance(@ApiIgnore @RequestAttribute(value = Constants.SESSION_USER) User loginUser,
                                          @ApiParam(name = "projectCode", value = "PROJECT_CODE", required = true) @PathVariable long projectCode,
                                          @ApiParam(name = "code", value = "TASK_CODE", required = true) @PathVariable long code,
                                          @RequestParam(value = "version", required = true) int version,
                                          @RequestParam(value = "warningGroupId", required = false, defaultValue = "0") String warningGroupId,
                                          @RequestParam(value = "workerGroup", required = false, defaultValue = "default") String workerGroup,
                                          @RequestParam(value = "environmentCode", required = false, defaultValue = "-1") Long environmentCode,
                                          @RequestParam(value = "startParams", required = false) String startParams,
                                          @RequestParam(value = "dryRun", defaultValue = "0", required = false) int dryRun) {

        Map<String, String> startParamMap = null;
        if (startParams != null) {
            startParamMap = JSONUtils.toMap(startParams);
        }

        Map<String, Object> result = execService.execStreamTaskInstance(loginUser, projectCode, code, version,
                warningGroupId, workerGroup, environmentCode, startParamMap, dryRun);
        return returnDataList(result);
    }


    /**
     * 调度中心定时调用
     * kill工作流实例
     *
     * @return
     */
    @GetMapping(value = "/execute/kill")
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result cleanProcessDefinition(@ApiParam(name = "projectCode", value = "PROJECT_CODE", required = true) @PathVariable long projectCode,
                                         @RequestParam("processInstanceId") String processInstanceId,
                                         @RequestParam("executeType") ExecuteType executeType) {
        Map<String, Object> result = execService.executeKill(projectCode, processInstanceId, executeType);
        return returnDataList(result);
    }

    /**
     * 批量运行工作流（不同启动参数多次调用同一工作流）
     */
    @ApiOperation(value = "startProcessInstanceBatch", notes = "BATCH_RUN_PROCESS_INSTANCE_NOTES")
    @PostMapping(value = "start-process-instance-batch")
    @ResponseStatus(HttpStatus.OK)
    @ApiException(START_PROCESS_INSTANCE_ERROR)
    @AccessLogAnnotation(ignoreRequestArgs = "loginUser")
    public Result startProcessInstanceBatch(
            @ApiIgnore @RequestAttribute(value = Constants.SESSION_USER) User loginUser,
            @ApiParam(name = "projectCode", value = "PROJECT_CODE", required = true) @PathVariable long projectCode,
            @RequestParam(value = "processDefinitionCode") long processDefinitionCode,
            @RequestParam(value = "scheduleTime") String scheduleTime,
            @RequestParam(value = "failureStrategy") FailureStrategy failureStrategy,
            @RequestParam(value = "startNodeList", required = false) String startNodeList,
            @RequestParam(value = "taskDependType", required = false) TaskDependType taskDependType,
            @RequestParam(value = "execType", required = false) CommandType execType,
            @RequestParam(value = "warningType") WarningType warningType,
            @RequestParam(value = "warningGroupId", required = false, defaultValue = "0") String warningGroupId,
            @RequestParam(value = "runMode", required = false) RunMode runMode,
            @RequestParam(value = "processInstancePriority", required = false) Priority processInstancePriority,
            @RequestParam(value = "workerGroup", required = false, defaultValue = "default") String workerGroup,
            @RequestParam(value = "environmentCode", required = false, defaultValue = "-1") Long environmentCode,
            @RequestParam(value = "timeout", required = false) Integer timeout,
            @RequestParam(value = "expectedParallelismNumber", required = false) Integer expectedParallelismNumber,
            @RequestParam(value = "dryRun", defaultValue = "0", required = false) int dryRun,
            @RequestParam(value = "testFlag", defaultValue = "0") int testFlag,
            @RequestParam(value = "complementDependentMode", required = false) ComplementDependentMode complementDependentMode,
            @RequestParam(value = "returnInstanceId", required = false, defaultValue = "false") Boolean returnInstanceId,
            @RequestBody List<String> startParamsList
    ) {
        if (timeout == null) {
            timeout = Constants.MAX_TASK_TIMEOUT;
        }
        if (complementDependentMode == null) {
            complementDependentMode = ComplementDependentMode.OFF_MODE;
        }

        if (CollectionUtils.isEmpty(startParamsList)) {
            return error(Status.REQUEST_PARAMS_NOT_VALID_ERROR.getCode(),"启动参数为空");
        }

        List<Map<String, String>> parsedStartParamsList = new ArrayList<>();
        for (String startParams : startParamsList) {
            Map<String, String> startParamMap = null;
            if (startParams != null) {
                startParamMap = JSONUtils.toMap(startParams);
            }
            parsedStartParamsList.add(startParamMap);
        }

        Map<String, Object> result = execService.execProcessInstanceBatch(
                loginUser, projectCode, processDefinitionCode,
                scheduleTime, execType, failureStrategy,
                startNodeList, taskDependType, warningType, warningGroupId, runMode, processInstancePriority,
                workerGroup, environmentCode, timeout, parsedStartParamsList, expectedParallelismNumber, dryRun, testFlag,
                complementDependentMode, returnInstanceId);

        // 直接处理结果，不使用 returnDataList
        if (result.get(Constants.MSG).equals(Status.SUCCESS.getMsg())) {
            int count = (Integer) result.get("totalCount");
            return success("成功，批量启动工作流实例"+count+"个", count);
        } else {
            String msg = (String) result.get(Constants.MSG);
            return error(Status.START_PROCESS_INSTANCE_ERROR.getCode(), msg);
        }

    }
}
