package org.apache.dolphinscheduler.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.dolphinscheduler.api.service.CatalogProcessDefinitionRelationService;
import org.apache.dolphinscheduler.common.utils.CodeGenerateUtils;
import org.apache.dolphinscheduler.dao.entity.CatalogProcessDefinitionRelation;
import org.apache.dolphinscheduler.dao.mapper.CatalogProcessDefinitionRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: CatalogProcessDefinitionRelationImpl
 * @date 2023/10/8
 */
@Service
public class CatalogProcessDefinitionRelationImpl extends BaseServiceImpl implements CatalogProcessDefinitionRelationService {
    @Autowired
    private CatalogProcessDefinitionRelationMapper catalogProcessDefinitionRelationMapper;

    @Override
    public List<Long> getProcessDefinitionCodes(String catalogId) {
        List<Long> result = new ArrayList<>();
        QueryWrapper<CatalogProcessDefinitionRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("catalog_id", catalogId);
        List<CatalogProcessDefinitionRelation> catalogProcessDefinitionRelations = catalogProcessDefinitionRelationMapper.selectList(queryWrapper);
        Optional.ofNullable(catalogProcessDefinitionRelations).ifPresent(catalogProcessDefinitionRelationList -> {
            result.addAll(catalogProcessDefinitionRelationList.stream().map(CatalogProcessDefinitionRelation::getProcessDefinitionCode).collect(Collectors.toSet()));
        });
        return result;
    }

    @Override
    public int insert(CatalogProcessDefinitionRelation catalogProcessDefinitionRelation) {
        return catalogProcessDefinitionRelationMapper.insert(catalogProcessDefinitionRelation);
    }

    @Override
    public int deleteByProcessDefinitionCode(long processDefinitionCode, long projectCode) {
        QueryWrapper<CatalogProcessDefinitionRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("process_definition_code", processDefinitionCode);
        queryWrapper.eq("project_code", projectCode);
        return catalogProcessDefinitionRelationMapper.delete(queryWrapper);
    }

    @Override
    public int updateByProcessDefinitionCode(long processDefinitionCode, CatalogProcessDefinitionRelation catalogProcessDefinitionRelation, long projectCode) {
        QueryWrapper<CatalogProcessDefinitionRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("process_definition_code", processDefinitionCode);
        queryWrapper.eq("project_code", projectCode);
        CatalogProcessDefinitionRelation catalogProcessDefinitionRelation1 = catalogProcessDefinitionRelationMapper.selectOne(queryWrapper);
        if (null == catalogProcessDefinitionRelation1) {
            catalogProcessDefinitionRelation.setId(String.valueOf(CodeGenerateUtils.getInstance().genCode()));
            catalogProcessDefinitionRelation.setCreateTime(catalogProcessDefinitionRelation.getUpdateTime());
            return this.insert(catalogProcessDefinitionRelation);
        }
        return catalogProcessDefinitionRelationMapper.update(catalogProcessDefinitionRelation, queryWrapper);
    }
}
