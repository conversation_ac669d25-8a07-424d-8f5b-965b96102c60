/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.plugin.task.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang3.SystemUtils;
import org.apache.dolphinscheduler.plugin.task.api.model.TaskResponse;
import org.apache.dolphinscheduler.plugin.task.api.utils.*;
import org.apache.dolphinscheduler.spi.utils.JSONUtils;
import org.apache.dolphinscheduler.spi.utils.PropertyUtils;
import org.apache.dolphinscheduler.spi.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.MDC;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.dolphinscheduler.plugin.task.api.TaskConstants.EXIT_CODE_FAILURE;
import static org.apache.dolphinscheduler.plugin.task.api.TaskConstants.EXIT_CODE_KILL;

/**
 * abstract command executor
 */
public abstract class AbstractCommandExecutor {

    /**
     * rules for extracting Var Pool
     */
    protected static final Pattern SETVALUE_REGEX = Pattern.compile(TaskConstants.SETVALUE_REGEX);

    protected StringBuilder varPool = new StringBuilder();
    /**
     * process
     */
    private Process process;

    /**
     * log handler
     */
    protected Consumer<LinkedBlockingQueue<String>> logHandler;

    /**
     * logger
     */
    protected Logger logger;

    /**
     * log list
     */
    protected LinkedBlockingQueue<String> logBuffer;

    protected boolean logOutputIsSuccess = false;

    /*
     * SHELL result string
     */
    protected String taskResultString;

    /**
     * taskRequest
     */
    protected TaskExecutionContext taskRequest;

    public AbstractCommandExecutor(Consumer<LinkedBlockingQueue<String>> logHandler,
                                   TaskExecutionContext taskRequest,
                                   Logger logger) {
        this.logHandler = logHandler;
        this.taskRequest = taskRequest;
        this.logger = logger;
        this.logBuffer = new LinkedBlockingQueue<>();
    }

    public AbstractCommandExecutor(LinkedBlockingQueue<String> logBuffer) {
        this.logBuffer = logBuffer;
    }

    /**
     * build process
     *
     * @param commandFile command file
     * @throws IOException IO Exception
     */
    private void buildProcess(String commandFile) throws IOException {
        // setting up user to run commands
        List<String> command = new LinkedList<>();

        // init process builder
        ProcessBuilder processBuilder = new ProcessBuilder();
        // setting up a working directory
        processBuilder.directory(new File(taskRequest.getExecutePath()));
        // merge error information to standard output stream
        processBuilder.redirectErrorStream(true);

        // if sudo.enable=true,setting up user to run commands
        if (OSUtils.isSudoEnable()) {
            logger.info("判断操作系统={}", SystemUtils.IS_OS_LINUX);
            logger.info("获取系统配置{}",
                    PropertyUtils.getBoolean(AbstractCommandExecutorConstants.TASK_RESOURCE_LIMIT_STATE));
            if (SystemUtils.IS_OS_LINUX
                    && PropertyUtils.getBoolean(AbstractCommandExecutorConstants.TASK_RESOURCE_LIMIT_STATE)) {
                generateCgroupCommand(command);
                command.add(commandInterpreter());
            } else if (SystemUtils.IS_OS_WINDOWS) {
            } else {
                command.add("sudo");
                command.add("-u");
                command.add(taskRequest.getTenantCode());
                command.add(commandInterpreter());
            }
        }

        command.addAll(Collections.emptyList());
        command.add(commandFile);

        // setting commands
        processBuilder.command(command);
        logger.info("生成的command={}", command);
        process = processBuilder.start();

        printCommand(command);
    }

    /**
     * generate systemd command.
     * eg: sudo systemd-run -q --scope -p CPUQuota=100% -p MemoryMax=200M --uid=root
     *
     * @param command command
     */
    private void generateCgroupCommand(List<String> command) {
        Integer cpuQuota = taskRequest.getCpuQuota();
        Integer memoryMax = taskRequest.getMemoryMax();

        command.add("sudo");
        command.add("systemd-run");
        command.add("-q");
        command.add("--scope");

        if (cpuQuota == -1) {
            command.add("-p");
            command.add("CPUQuota=100%");
        } else {
            command.add("-p");
            command.add(String.format("CPUQuota=%s%%", taskRequest.getCpuQuota()));
        }

        if (memoryMax == -1) {
            /*command.add("-p");
            command.add(String.format("MemoryLimit=%s", "infinity"));*/
        } else {
            command.add("-p");
            command.add(String.format("MemoryLimit=%sM", taskRequest.getMemoryMax()));
        }

        command.add(String.format("--uid=%s", taskRequest.getTenantCode()));
    }

    public void cancelSeatunnelApplication(List<String> commands) throws IOException {

        String commandFilePath = buildCancelCommandFilePath();

        // create command file if not exists
        // lihj如果执行文件不存在，则创建文件
        String execCommand = String.join(" ", commands);
        createCommandFileIfNotExists(execCommand, commandFilePath);

        // build process
        // lihj构建执行命令
        buildProcess(commandFilePath);

        // init process builder
        ProcessBuilder processBuilder = new ProcessBuilder();
        // setting up a working directory
        processBuilder.directory(new File(taskRequest.getExecutePath()));
        // merge error information to standard output stream
        processBuilder.redirectErrorStream(true);

        logger.info("得到的命令是 {}", commands);
        String cancelProcessId = getProcessId(process);
        logger.info("取消任务的进程ID: {}", cancelProcessId);

    }

    public TaskResponse run(String execCommand, StringRedisTemplate globalRedisTemplate, String stxHome) throws IOException, InterruptedException {
        TaskResponse result = new TaskResponse();
        Map<String, String> map = new HashMap<>();
        String taskInstanceId = taskRequest.getTaskInstanceId();
        map.put("stxTaskInstanceId", taskInstanceId);
        map.put("stxTaskCode", String.valueOf(taskRequest.getTaskCode()));
        map.put("stxTaskName", taskRequest.getTaskName());
/*        taskRequest.getProcessDefineCode();//工作流定义code
        taskRequest.getProcessInstanceId();//对应工作流实例id*/
        if (null == TaskExecutionContextCacheManager.getByTaskInstanceId(taskInstanceId)) {
            result.setExitStatusCode(EXIT_CODE_KILL);
            result.setVarPool(JSONUtils.toJsonString(map));
            return result;
        }

        if (StringUtils.isEmpty(execCommand)) {
            TaskExecutionContextCacheManager.removeByTaskInstanceId(taskInstanceId);
            result.setVarPool(JSONUtils.toJsonString(map));
            return result;
        }

        String commandFilePath = buildCommandFilePath();

        // create command file if not exists
        // lihj如果执行文件不存在，则创建文件
        createCommandFileIfNotExists(execCommand, commandFilePath);

        // 确保文件写入完成，避免 text file busy 问题
        ensureFileWriteComplete(commandFilePath);

        // build process
        // lihj构建执行命令，并启动
        buildProcess(commandFilePath);

        // parse process output
        //处理任务的输入、输出参数
        if ("STX".equalsIgnoreCase(taskRequest.getTaskType())) {
            try {
                String param = taskRequest.getTaskParams();
                Map<String, Object> stringStringMap1 = JSONUtils.toMap(param, String.class, Object.class);
                String rawScript = stringStringMap1.get("rawScript") + "";
                // 解析HOCON配置
                String filePath = ConfigUtils.getFilePath(rawScript);
                map.put("stxFilePath", filePath);
                String stxRealPath = ConfigParser.getSinkPath(rawScript);
                map.put("stxRealPath", stxRealPath);
            } catch (Exception e) {
                logger.error("当前stx参数是{}", taskRequest.getTaskParams());
                logger.error("处理stx参数出现异常，异常信息是{}", e.getMessage());
            }
            parseProcessOutputStx(process, map);
        } else {
            parseProcessOutput(process);
        }

        // lihj获取pid
        String processId = getProcessId(process);

        result.setProcessId(processId);

        // cache processId
        taskRequest.setProcessId(processId);
        boolean updateTaskExecutionContextStatus =
                TaskExecutionContextCacheManager.updateTaskExecutionContext(taskRequest);
        if (Boolean.FALSE.equals(updateTaskExecutionContextStatus)) {
            ProcessUtils.kill(taskRequest);
            result.setExitStatusCode(EXIT_CODE_KILL);
            result.setVarPool(JSONUtils.toJsonString(map));
            return result;
        }
        // print process id
        logger.info("任务对应的pid为 {} 开始存储pid", processId);//对应的pid
        syncProcessIdToRedis(processId, taskRequest, globalRedisTemplate);
        logger.info("存储pid完毕");
        logger.info("超时时间设置的为{},任务开始时间是 {}", taskRequest.getTaskTimeout(), taskRequest.getStartTime().getTime());
        // if timeout occurs, exit directly
        long remainTime = getRemainTime();
        String appIds = String.join(TaskConstants.COMMA, getApplicationIds());
        //appIds 如果是-1 则代码任务失败
        if ("STX".equalsIgnoreCase(taskRequest.getTaskType())) {
            int count = 0;
            while (count < 5 * 60 * 10) { // lihj循环等待进程结束10分钟
                if (StringUtils.isEmpty(appIds)) {
                    appIds = String.join(TaskConstants.COMMA, getApplicationIds());
                } else {
                    break;
                }
                count++;
                try {
                    Thread.sleep(200);
                    logger.debug("等待从hazelcast中获取任务的jobId");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            logger.info("将信息发送至redis,appIds={},任务实例id={}", appIds, taskRequest.getTaskInstanceId());
            logger.info("从healcast中拿到的任务id是 {}", appIds);
            if (StringUtils.isNotBlank(appIds) && !"-1".equals(appIds)) {
                syncAppIdToRedis(appIds, taskRequest, globalRedisTemplate);
            } else {
                logger.error("未从healcast中拿到的任务id={}，请检查任务提交时间慢的原因", appIds);
            }
        }
        // waiting for the run to finish

        boolean status = process.waitFor(remainTime, TimeUnit.SECONDS);
        //取消redis监控信息
        cleanProcessIdToRedis(taskRequest.getTaskInstanceId(), globalRedisTemplate);
        // if SHELL task exit
        if (status) {
            // SHELL task state
            result.setExitStatusCode(process.exitValue());
        } else {
            logger.error("process has failure, the task timeout configuration value is:{}, {},ready to kill ...",
                    taskRequest.getTaskTimeout(),taskRequest.getProcessId());
            if ("STX".equalsIgnoreCase(taskRequest.getTaskType())) {
                ProcessUtils.cancelSeatunnelApplication(stxHome, appIds);
            } else {
                ProcessUtils.kill(taskRequest);
            }
            result.setExitStatusCode(EXIT_CODE_FAILURE);
        }

        logger.info(
                "process has exited, execute path:{}, processId:{} ,exitStatusCode:{} ,processWaitForStatus:{} ,processExitValue:{}",
                taskRequest.getExecutePath(), processId, result.getExitStatusCode(), status, process.exitValue());
        /*if("STX".equalsIgnoreCase(taskRequest.getTaskType())){
            logger.info("开始处理参数");
            Thread.sleep(5000);//等待日志输出完毕
            StxResultUtils.parseJobStatistic(taskRequest.getLogPath(), map);
            result.setVarPool(JSONUtils.toJsonString(map));
        }*/
        result.setVarPool(JSONUtils.toJsonString(map));
        logger.info("处理参数完毕 map={},var={}", map, result.getVarPool());
        return result;

    }

    private void syncProcessIdToRedis(String realPid, TaskExecutionContext taskRequest, StringRedisTemplate globalRedisTemplate) {
        try {
            String key = "dedp:ds:task:monitor";
            String hkey = taskRequest.getTaskInstanceId();
            globalRedisTemplate.opsForHash().put(key, hkey, realPid);
        } catch (Exception e) {
            logger.error("存储信息出现异常，请检查，不影响任务执行。", e.getMessage());
        }
    }

    private void cleanProcessIdToRedis(String taskInstanceId, StringRedisTemplate globalRedisTemplate) {
        try {
            String key = "dedp:ds:task:monitor";
            globalRedisTemplate.opsForHash().delete(key, taskInstanceId);
            logger.info("清理taskInstanceId缓存数据完成", taskInstanceId);
        } catch (Exception e) {
            logger.error("存储信息出现异常，请检查，不影响任务执行。", e.getMessage());
        }
    }

    private void syncAppIdToRedis(String appIds, TaskExecutionContext taskRequest, StringRedisTemplate globalRedisTemplate) throws JsonProcessingException {
        String key = "DEDP:DS:TASK:SCHEDULER";
        logger.info("将信息发送至redis,key={},value={}", key, appIds);
       /* Map<String, String> map = new HashMap<>();
        map.put("appid", appIds);
        map.put("sdate", String.valueOf(System.currentTimeMillis()));*/
        String hkey = taskRequest.getTenantCode() + ":" + taskRequest.getTaskInstanceId();
        //host = "**************:1234" 只取ip
        String host = taskRequest.getHost();
        if (StringUtils.isNotBlank(host)) {
            // 找到 ":" 的位置
            int index = host.indexOf(":");
            // 如果找到了 ":"，则截取 ":" 之前的部分
            if (index != -1) {
                host = host.substring(0, index);
            } else {
                logger.info("host未找到':'");
            }
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> map = new HashMap<>();
        map.put("sdate", String.valueOf(System.currentTimeMillis()));
        map.put("appid", appIds);
        map.put("host", host);
        map.put("sourceCountMap", objectMapper.writeValueAsString(taskRequest.getSourceCountMap()));
//        String str = "{\"sdate\":\"" + System.currentTimeMillis() + "\",\"appid\":\"" + appIds + "\",\"host\":\"" + host + "\"}";
        globalRedisTemplate.opsForHash().put(key, hkey, objectMapper.writeValueAsString(map));
    }

    public Set<String> getApplicationIds() throws IOException {
        Set<String> appIds = new HashSet<>();
        File file = new File(taskRequest.getLogPath());
        if (!file.exists()) {
            return appIds;
        }

        /*
         * analysis log? get submitted yarn application id
         */
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(taskRequest.getLogPath()), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (line.contains("SeaTunnel job executed failed")) {
                    return Collections.singleton("-1");
                }
                String appId = findAppId(line);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(appId)) {
                    appIds.add(appId);
                    return appIds;
                }
            }
        }
        return appIds;
    }

    protected static final Pattern SEATUNNEL_APPLICATION_REGEX = Pattern.compile(TaskConstants.SEATUNNEL_APPLICATION_REGEX);

    protected String findAppId(String line) {
        Matcher matcher = SEATUNNEL_APPLICATION_REGEX.matcher(line);
        if (matcher.find()) {
            String jobId = matcher.group(1);
            return jobId;
        }
        return null;
    }

    public TaskResponse run(String execCommand) throws IOException, InterruptedException {
        TaskResponse result = new TaskResponse();
        String taskInstanceId = taskRequest.getTaskInstanceId();
        if (null == TaskExecutionContextCacheManager.getByTaskInstanceId(taskInstanceId)) {
            result.setExitStatusCode(EXIT_CODE_KILL);
            return result;
        }
        if (StringUtils.isEmpty(execCommand)) {
            TaskExecutionContextCacheManager.removeByTaskInstanceId(taskInstanceId);
            return result;
        }

        String commandFilePath = buildCommandFilePath();

        // create command file if not exists
        // lihj如果执行文件不存在，则创建文件
        createCommandFileIfNotExists(execCommand, commandFilePath);

        // build process
        // lihj构建执行命令
        buildProcess(commandFilePath);

        // parse process output
        //处理任务的输入、输出参数
        parseProcessOutput(process);

        // lihj获取pid
        String processId = getProcessId(process);

        result.setProcessId(processId);

        // cache processId
        taskRequest.setProcessId(processId);
        boolean updateTaskExecutionContextStatus =
                TaskExecutionContextCacheManager.updateTaskExecutionContext(taskRequest);
        if (Boolean.FALSE.equals(updateTaskExecutionContextStatus)) {
            ProcessUtils.kill(taskRequest);
            result.setExitStatusCode(EXIT_CODE_KILL);
            return result;
        }
        // print process id
        logger.info("process start, process id is: {}", processId);
        logger.info("任务实例id={}", taskRequest.getTaskInstanceId());

        // if timeout occurs, exit directly
        long remainTime = getRemainTime();
        // waiting for the run to finish
        boolean status = process.waitFor(remainTime, TimeUnit.SECONDS);

        // if SHELL task exit
        if (status) {

            // SHELL task state
            result.setExitStatusCode(process.exitValue());

        } else {
            logger.error("process has failure, the task timeout configuration value is:{}, ready to kill ...",
                    taskRequest.getTaskTimeout());
            ProcessUtils.kill(taskRequest);
            result.setExitStatusCode(EXIT_CODE_FAILURE);
        }

        logger.info(
                "process has exited, execute path:{}, processId:{} ,exitStatusCode:{} ,processWaitForStatus:{} ,processExitValue:{}",
                taskRequest.getExecutePath(), processId, result.getExitStatusCode(), status, process.exitValue());
        return result;

    }

    public String getVarPool() {
        return varPool.toString();
    }


    /**
     * cancel application
     *
     * @throws Exception exception
     */
    public void cancelApplication() throws Exception {
        if (process == null) {
            return;
        }

        // clear log
        clear();

        String processId = getProcessId(process);

        logger.info("cancel process: {}", processId);

        // kill , waiting for completion
        boolean killed = softKill(processId);

        if (!killed) {
            // hard kill
            hardKill(processId);

            // destory
            process.destroy();

            process = null;
        }
    }

    /**
     * soft kill
     *
     * @param processId process id
     * @return process is alive
     */
    private boolean softKill(String processId) {

        if (!processId.equals("0") && process.isAlive()) {
            try {
                // sudo -u user command to run command
                String cmd = String.format("kill %s", processId);
                cmd = OSUtils.getSudoCmd(taskRequest.getTenantCode(), cmd);
                logger.info("soft kill task:{}, process id:{}, cmd:{}", taskRequest.getTaskAppId(), processId, cmd);

                Runtime.getRuntime().exec(cmd);
            } catch (IOException e) {
                logger.info("kill attempt failed", e);
            }
        }

        return process.isAlive();
    }

    /**
     * hard kill
     *
     * @param processId process id
     */
    private void hardKill(String processId) {
        if (!"0".equals(processId) && process.isAlive()) {
            try {
                String cmd = String.format("kill -9 %s", processId);
                cmd = OSUtils.getSudoCmd(taskRequest.getTenantCode(), cmd);
                logger.info("hard kill task:{}, process id:{}, cmd:{}", taskRequest.getTaskAppId(), processId, cmd);

                Runtime.getRuntime().exec(cmd);
            } catch (IOException e) {
                logger.error("kill attempt failed ", e);
            }
        }
    }

    private void printCommand(List<String> commands) {
        logger.info("task run command: {}", String.join(" ", commands));
        if (!commands.get(commands.size() - 1).contains("cancel")) {
            logger.info("任务提交成功");
        }
    }

    /**
     * clear
     */
    private void clear() {

        LinkedBlockingQueue<String> markerLog = new LinkedBlockingQueue<>(1);
        markerLog.add(ch.qos.logback.classic.ClassicConstants.FINALIZE_SESSION_MARKER.toString());

        if (!logBuffer.isEmpty()) {
            // log handle
            logHandler.accept(logBuffer);
            logBuffer.clear();
        }
        logHandler.accept(markerLog);
    }

    /**
     * get the standard output of the process
     * lihj获取流程标准输出
     *
     * @param process process
     */
    private Map<String, String> parseProcessOutputStx(Process process, Map<String, String> map) {
        String threadLoggerInfoName = taskRequest.getTaskLogName();
        ExecutorService getOutputLogService = newDaemonSingleThreadExecutor(threadLoggerInfoName);
        getOutputLogService.submit(() -> {
            try (BufferedReader inReader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = inReader.readLine()) != null) {
                    StxResultUtils.parseLogContent(line, map);
                    MDC.put("taskInstanceId", String.valueOf(taskRequest.getTaskInstanceId()));
                    logger.info(line);
                    if (line.startsWith("${setValue(") || line.startsWith("#{setValue(")) {
                        varPool.append(findVarPool(line));
                        varPool.append("$VarPool$");
                    } else {
                        logBuffer.add(line);
                        taskResultString = line;
                    }
                }
                logOutputIsSuccess = true;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                logOutputIsSuccess = true;
            }
        });

        getOutputLogService.shutdown();

        ExecutorService parseProcessOutputExecutorService = newDaemonSingleThreadExecutor(threadLoggerInfoName);
        parseProcessOutputExecutorService.submit(() -> {
            try {
                long lastFlushTime = System.currentTimeMillis();
                while (logBuffer.size() > 0 || !logOutputIsSuccess) {
                    if (logBuffer.size() > 0) {
                        lastFlushTime = flush(lastFlushTime);
                    } else {
                        Thread.sleep(TaskConstants.DEFAULT_LOG_FLUSH_INTERVAL);
                    }
                }
            } catch (Exception e) {
                Thread.currentThread().interrupt();
                logger.error(e.getMessage(), e);
            } finally {
                clear();
            }
        });
        parseProcessOutputExecutorService.shutdown();
        return map;
    }

    private void parseProcessOutput(Process process) {
        String threadLoggerInfoName = taskRequest.getTaskLogName();
        ExecutorService getOutputLogService = newDaemonSingleThreadExecutor(threadLoggerInfoName);
        getOutputLogService.submit(() -> {
            try (BufferedReader inReader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = inReader.readLine()) != null) {
                    MDC.put("taskInstanceId", String.valueOf(taskRequest.getTaskInstanceId()));
                    logger.info(line);
                    if (line.startsWith("${setValue(") || line.startsWith("#{setValue(")) {
                        varPool.append(findVarPool(line));
                        varPool.append("$VarPool$");
                    } else {
                        logBuffer.add(line);
                        taskResultString = line;
                    }
                }
                logOutputIsSuccess = true;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                logOutputIsSuccess = true;
            }
        });

        getOutputLogService.shutdown();

        ExecutorService parseProcessOutputExecutorService = newDaemonSingleThreadExecutor(threadLoggerInfoName);
        parseProcessOutputExecutorService.submit(() -> {
            try {
                long lastFlushTime = System.currentTimeMillis();
                while (logBuffer.size() > 0 || !logOutputIsSuccess) {
                    if (logBuffer.size() > 0) {
                        lastFlushTime = flush(lastFlushTime);
                    } else {
                        Thread.sleep(TaskConstants.DEFAULT_LOG_FLUSH_INTERVAL);
                    }
                }
            } catch (Exception e) {
                Thread.currentThread().interrupt();
                logger.error(e.getMessage(), e);
            } finally {
                clear();
            }
        });
        parseProcessOutputExecutorService.shutdown();
    }

    /**
     * find var pool
     *
     * @param line
     * @return
     */
    private String findVarPool(String line) {
        Matcher matcher = SETVALUE_REGEX.matcher(line);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }


    /**
     * get remain time（s）
     *
     * @return remain time
     */
    private long getRemainTime() {
        long usedTime = (System.currentTimeMillis() - taskRequest.getStartTime().getTime()) / 1000;
        long remainTime = taskRequest.getTaskTimeout() - usedTime;
        logger.info("usedTime ={},remainTime={},当前时间是{},任务开始时间是{}，任务超时时间是{}", usedTime, remainTime, System.currentTimeMillis(), taskRequest.getStartTime().getTime(), taskRequest.getTaskTimeout());
        /*if (remainTime < 0) {
            throw new RuntimeException("task execution time out");
        }*/
        if (remainTime < 0) {//如果设置值太小，则10秒后kill掉
            return 10;//throw new RuntimeException("task execution time out");
        }
        return remainTime;
    }

    /**
     * get process id
     *
     * @param process process
     * @return process id
     */
    private String getProcessId(Process process) {
        int processId = 0;

        try {
            Field f = process.getClass().getDeclaredField(TaskConstants.PID);
            f.setAccessible(true);
            processId = f.getInt(process);
        } catch (Throwable e) {
            // logger.error(e.getMessage(), e);
            logger.info("windows下获取pid");
            try {
                Field f = process.getClass().getDeclaredField(TaskConstants.HANDLE);
                f.setAccessible(true);
                processId = (int) f.getLong(process);
            } catch (Throwable e1) {
                logger.error(e.getMessage(), e);
            }
        }

        return String.valueOf(processId);
    }

    /**
     * 获取真正执行任务的进程ID（通常是Java进程）
     *
     * @param process 主进程
     * @return 真正执行任务的进程ID
     */
   /* private String getRealTaskProcessId(Process process) {
        String mainProcessId = getProcessId(process);
        logger.info("主进程PID: {}", mainProcessId);

        // 对于SeaTunnel任务，尝试找到真正的Java进程
        if ("STX".equalsIgnoreCase(taskRequest.getTaskType())) {
            try {
                int realPid = ProcessUtils.findJavaPid(mainProcessId);
                if (realPid != -1) {
                    logger.info("找到真正执行任务的Java进程PID: {}", realPid);
                    return String.valueOf(realPid);
                }
            } catch (Exception e) {
                logger.warn("查找Java子进程失败，使用主进程PID: {}", mainProcessId, e);
            }
        }

        return mainProcessId;
    }*/

    /**
     * 确保文件写入完成，避免 text file busy 问题
     *
     * @param filePath 文件路径
     */
    private void ensureFileWriteComplete(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            return;
        }

        try {
            // 等待文件系统同步
            Thread.sleep(50);

            // 尝试以只读方式打开文件，确保写入完成
            int retryCount = 0;
            int maxRetries = 10;

            while (retryCount < maxRetries) {
                try (FileInputStream fis = new FileInputStream(file)) {
                    // 如果能成功打开，说明文件写入完成
                    break;
                } catch (IOException e) {
                    if (e.getMessage().contains("Text file busy") ||
                        e.getMessage().contains("Resource temporarily unavailable")) {
                        retryCount++;
                        logger.warn("11111111111111 {},2222222{}/{}", filePath, retryCount, maxRetries);
                        Thread.sleep(100);
                    } else {
                        throw e;
                    }
                }
            }

            if (retryCount >= maxRetries) {
                logger.warn("等待文件 {} 写入完成超时，继续执行", filePath);
            }

        } catch (Exception e) {
            logger.warn("检查文件写入状态时出错: {}", e.getMessage());
        }
    }

    /**
     * when log buffer siz or flush time reach condition , then flush
     *
     * @param lastFlushTime last flush time
     * @return last flush time
     */
    private long flush(long lastFlushTime) {
        long now = System.currentTimeMillis();

        /*
         * when log buffer siz or flush time reach condition , then flush
         */
        if (logBuffer.size() >= TaskConstants.DEFAULT_LOG_ROWS_NUM
                || now - lastFlushTime > TaskConstants.DEFAULT_LOG_FLUSH_INTERVAL) {
            lastFlushTime = now;
            logHandler.accept(logBuffer);

            logBuffer.clear();
        }
        return lastFlushTime;
    }

    protected abstract String buildCommandFilePath();

    protected abstract String buildCancelCommandFilePath();

    protected abstract void createCommandFileIfNotExists(String execCommand, String commandFile) throws IOException;

    ExecutorService newDaemonSingleThreadExecutor(String threadName) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setDaemon(true)
                .setNameFormat(threadName)
                .build();
        return Executors.newSingleThreadExecutor(threadFactory);
    }

    protected abstract String commandInterpreter();

}
