/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.plugin.task.joyadata.st;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.joyadata.engine.common.beans.constants.Constants;
import com.joyadata.engine.common.beans.dto.EngineEntryPointDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDTO;
import groovy.lang.Tuple2;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dolphinscheduler.plugin.task.api.*;
import org.apache.dolphinscheduler.plugin.task.api.model.Property;
import org.apache.dolphinscheduler.plugin.task.api.model.TaskResponse;
import org.apache.dolphinscheduler.plugin.task.api.parameters.AbstractParameters;
import org.apache.dolphinscheduler.plugin.task.api.parser.ParamUtils;
import org.apache.dolphinscheduler.plugin.task.api.parser.ParameterUtils;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.dto.SavePointDTO;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.utils.EngineInitUtils;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.utils.TdSqlProxyUtils;
import org.apache.dolphinscheduler.spi.utils.JSONUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.sql.DataSource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.dolphinscheduler.plugin.task.api.TaskConstants.EXIT_CODE_FAILURE;
import static org.apache.dolphinscheduler.plugin.task.joyadata.st.JoyadataConstants.CONFIG_OPTIONS;

/**
 * seatunnel task
 */
public class JoyadataSeatunnelTask extends AbstractTaskExecutor {

    /**
     * seatunnel parameters
     */
    private JoyadataSeatunnelParameters seatunnelParameters;

    /**
     * shell command executor
     */
    private ShellCommandExecutor shellCommandExecutor;

    /**
     * taskExecutionContext
     */
    protected final TaskExecutionContext taskExecutionContext;

    protected static final Pattern SEATUNNEL_APPLICATION_REGEX = Pattern.compile(TaskConstants.SEATUNNEL_APPLICATION_REGEX);

    private StringRedisTemplate globalRedisTemplate;
    private DataSource dataSource;
    private String stxHome;
    private String dataSourcePluginPath;

    /**
     * constructor
     *
     * @param taskExecutionContext taskExecutionContext
     */
    public JoyadataSeatunnelTask(TaskExecutionContext taskExecutionContext) {
        super(taskExecutionContext);

        this.taskExecutionContext = taskExecutionContext;
        this.shellCommandExecutor = new ShellCommandExecutor(this::logHandle,
                taskExecutionContext,
                logger);
    }

    @Override
    public void init() {
        logger.info("SeaTunnel task params {}", taskExecutionContext.getTaskParams());
        if (!seatunnelParameters.checkParameters()) {
            throw new RuntimeException("SeaTunnel task params is not valid");
        }
    }

    @Override
    public void initCustomParam(StringRedisTemplate redisTemplate, String engineHome, String stxHome, String gatewayHttp, DataSource dataSource, RestTemplate restTemplate, String datasourcexHome) {
        globalRedisTemplate = redisTemplate;
        this.dataSourcePluginPath = datasourcexHome;
        DatasourceUtils.initDataSourcePluginPath(dataSourcePluginPath);
        dataSource = dataSource;
        this.stxHome = stxHome;
        JoyadataSeatunnelParameters seatunnelParameter = JSONUtils.parseObject(taskRequest.getTaskParams(), JoyadataSeatunnelParameters.class);
        String rawScript = seatunnelParameter.getRawScript();
        if (StringUtils.isNotBlank(rawScript)) {
            Map<String, Property> paramsMap = taskExecutionContext.getPrepareParamsMap();
            //先处理建表中的参数
            rawScript = convertParams(rawScript);
            EngineEntryPointDTO rawScriptDTO = JSONObject.parseObject(rawScript, EngineEntryPointDTO.class);
            //处理tdsql是否需要替换
            EngineEntryPointDTO newRawScriptDTO = TdSqlProxyUtils.checkUseProxy(rawScriptDTO, globalRedisTemplate, gatewayHttp, taskRequest, logger, paramsMap);
            logger.info("开始检查组装创建目标表。");
            try {
                EngineInitUtils.createSinkTable(newRawScriptDTO, redisTemplate, taskRequest.getTenantCode(), logger, gatewayHttp);
            } catch (RuntimeException e) {
                //2024-09-05 如果其中一个sink设置了建表，报错就抛出异常。
                List<EngineSinkDTO> sink = newRawScriptDTO.getSink();
                if (!CollectionUtils.isEmpty(sink)) {
                    boolean createAlterTable = getCreateAlterTable(sink);
                    if (createAlterTable) {
                        throw e;
                    }
                }
            }
            logger.info("目标表检查创建完毕,开始组装配置文件。");
            //填充ftl
            String content = convertParams(EngineInitUtils.initEngineModel(newRawScriptDTO, redisTemplate, taskRequest, logger, gatewayHttp, engineHome, paramsMap, dataSource));
            //checkCreateFile(engineModel); 20241118 移除空文件生成，底层会自动生成。
            seatunnelParameter.setRawScript(content);
            seatunnelParameters.setRawScript(content);
            taskRequest.setTaskParams(JSONObject.toJSONString(seatunnelParameter));
        }
    }


    private boolean getCreateAlterTable(List<EngineSinkDTO> sink) {
        boolean createAlterTable = false;
        for (EngineSinkDTO engineSinkDTO : sink) {
            if (createAlterTable) {
                break;
            }
            switch (engineSinkDTO.getType()) {
                case JDBC:
                    if (null != engineSinkDTO.getJdbcSink().getCreateAlterTable()) {
                        createAlterTable = engineSinkDTO.getJdbcSink().getCreateAlterTable();
                    }
                    break;
                case DORIS:
                    if (null != engineSinkDTO.getDorisSink().getCreateAlterTable()) {
                        createAlterTable = engineSinkDTO.getDorisSink().getCreateAlterTable();
                    }
                    break;
                case CLICKHOUSE:
                    if (null != engineSinkDTO.getClickHouseSink().getCreateAlterTable()) {
                        createAlterTable = engineSinkDTO.getClickHouseSink().getCreateAlterTable();
                    }
                    break;
                case HIVE:
                    if (null != engineSinkDTO.getHiveSink().getCreateAlterTable()) {
                        createAlterTable = engineSinkDTO.getHiveSink().getCreateAlterTable();
                    }
                    break;
                case KUDU:
                    if (null != engineSinkDTO.getKuduSink().getCreateAlterTable()) {
                        createAlterTable = engineSinkDTO.getKuduSink().getCreateAlterTable();
                    }
                    break;
            }
        }
        return createAlterTable;
    }

    private String convertParams(String content) {
        //处理参数替换
        Map<String, Property> paramsMap = taskExecutionContext.getPrepareParamsMap();
        //处理文件参数
        content = ParameterUtils.convertParameterPlaceholdersForSpecial(content, taskExecutionContext.getFileParamsPath());
        //开始 20240902处理全局参数覆盖任务参数
        String globalParams = taskExecutionContext.getGlobalParams();
        ParameterUtils.convertGlobalParams(paramsMap, globalParams);
        content = ParameterUtils.convertParameterPlaceholders(content, ParamUtils.convert(paramsMap));

        return content;
    }

    @Override
    public void doSavePoint(StringRedisTemplate redisTemplate, DataSource dataSource) {
        String processDefineCode = String.valueOf(taskRequest.getProcessDefineCode());
        String taskCode = String.valueOf(taskRequest.getTaskCode());
        String needSavePoint = redisTemplate.opsForValue().get(Constants.ENGINE_NEED_SAVEPOINT + processDefineCode + ":" + taskCode);

        if (StringUtils.isEmpty(needSavePoint)) {
            logger.info("No savepoint needed for processDefineCode={}, taskCode={}", processDefineCode, taskCode);
            return;
        }

        List<SavePointDTO> savePoints = JSONObject.parseArray(needSavePoint, SavePointDTO.class);
        if (CollectionUtils.isEmpty(savePoints)) {
            logger.info("Empty savepoint list for processDefineCode={}, taskCode={}", processDefineCode, taskCode);
            return;
        }

        // 批量处理保存位点
        savePoints.forEach(savePoint -> {
            try {
                savePointToRedis(redisTemplate, processDefineCode, taskCode, savePoint);
                savePointToDatabase(dataSource, processDefineCode, taskCode, savePoint);
            } catch (Exception e) {
                logger.error("Failed to save point for table={}, error={}", savePoint.getTableName(), e.getMessage(), e);
            }
        });

        // 清理Redis中的临时位点信息
        redisTemplate.delete(Constants.ENGINE_NEED_SAVEPOINT + processDefineCode + ":" + taskCode);
    }

    /**
     * 保存位点到Redis
     */
    private void savePointToRedis(StringRedisTemplate redisTemplate, String processDefineCode, String taskCode, SavePointDTO savePoint) {
        String redisKey = Constants.ENGINE_SAVEPOINT + processDefineCode + ":" + taskCode + ":" + savePoint.getTableName();
        redisTemplate.opsForValue().set(redisKey, savePoint.getMax());
        logger.info("Saved point to Redis: processDefineCode={}, taskCode={}, table={}, max={}",
                processDefineCode, taskCode, savePoint.getTableName(), savePoint.getMax());
    }

    /**
     * 保存位点到数据库
     */
    private void savePointToDatabase(DataSource dataSource, String processDefineCode, String taskCode, SavePointDTO savePoint) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false);
            try {
                // 更新记录表
                updateSavePointRecord(connection, processDefineCode, taskCode, savePoint);
                // 插入或更新位点表
                upsertSavePoint(connection, processDefineCode, taskCode, savePoint);

                connection.commit();
                logger.info("Successfully saved point to database: processDefineCode={}, taskCode={}, table={}",
                        processDefineCode, taskCode, savePoint.getTableName());
            } catch (SQLException e) {
                connection.rollback();
                throw e;
            }
        }
    }

    /**
     * 更新位点记录表
     */
    private void updateSavePointRecord(Connection connection, String processDefineCode, String taskCode, SavePointDTO savePoint) throws SQLException {
        String updateSql = "update t_ds_stx_savepoint_record set min_save_point=?, max_save_point=?, type=1, update_time=? " +
                "where proccess_define_code=? and task_code=? and table_name=? and type=0";

        try (PreparedStatement statement = connection.prepareStatement(updateSql)) {
            statement.setString(1, savePoint.getMin());
            statement.setString(2, savePoint.getMax());
            statement.setTimestamp(3, new java.sql.Timestamp(System.currentTimeMillis()));
            statement.setString(4, processDefineCode);
            statement.setString(5, taskCode);
            statement.setString(6, savePoint.getTableName());
            statement.executeUpdate();
        }
    }

    /**
     * 插入或更新位点表
     */
    private void upsertSavePoint(Connection connection, String processDefineCode, String taskCode, SavePointDTO savePoint) throws SQLException {
        String upsertSql = "INSERT INTO `t_ds_stx_savepoint` (`proccess_define_code`,`task_code`,`table_name`,`min_save_point`,`max_save_point`,`type`," +
                "`create_time`,`update_time`) VALUES (?, ?, ?, ?, ?, ?, ?, ?) " +
                "ON DUPLICATE KEY UPDATE `min_save_point` = VALUES(`min_save_point`),`max_save_point` = VALUES(`max_save_point`), " +
                "`type` = VALUES(`type`),`update_time` = VALUES(`update_time`)";

        try (PreparedStatement statement = connection.prepareStatement(upsertSql)) {
            statement.setString(1, processDefineCode);
            statement.setString(2, taskCode);
            statement.setString(3, savePoint.getTableName());
            statement.setString(4, savePoint.getMin());
            statement.setString(5, savePoint.getMax());
            statement.setInt(6, 0);
            statement.setTimestamp(7, new java.sql.Timestamp(System.currentTimeMillis()));
            statement.setTimestamp(8, new java.sql.Timestamp(System.currentTimeMillis()));
            statement.executeUpdate();
        }
    }

    @Override
    public void handle() throws TaskException {
        try {
            logger.info("开始生成执行命令...");
            // construct process
            String command = buildCommand();
            logger.info("生成的command是 {},path={}", command, taskRequest.getLogPath());
            if (command.contains("seatunnel.sh") && !command.contains("cancel")) {
                logger.info("提交stx任务:任务提交成功");
                //执行前置sql
                Map<String, List<Tuple2<String, List<String>>>> preSqlMap = taskExecutionContext.getPreSqlMap();
                if (null != preSqlMap && !preSqlMap.isEmpty()) {
                    preSqlMap.keySet().forEach(datasourceId -> {
                        List<Tuple2<String, List<String>>> preSqlTupleList = preSqlMap.get(datasourceId);
                        preSqlTupleList.forEach(preSqlTuple -> {
                            String configName = preSqlTuple.getV1();
                            List<String> preSqlList = preSqlTuple.getV2();
                            preSqlList.forEach(sql -> {
                                try {
                                    DatasourceUtils.execSQL(datasourceId, sql, null, taskExecutionContext.getTenantCode());
                                } catch (SQLException e) {
                                    logger.error("前置sql:" + configName + "组件执行前置sql失败，sql语句是{},错误信息是{}", sql, e.getMessage());
                                    throw new RuntimeException(e);
                                }
                            });
                            logger.info("前置sql:" + configName + "组件前置sql执行成功");
                        });
                    });
                } else {
                    logger.info("前置sql:无前置sql");
                }
            }
            TaskResponse commandExecuteResult = shellCommandExecutor.run(command, globalRedisTemplate, stxHome);
            logger.info("任务执行完毕返回参数是 {}", commandExecuteResult.getVarPool());
            setExitStatusCode(commandExecuteResult.getExitStatusCode());
            setProcessId(commandExecuteResult.getProcessId());
            //处理输出参数
            //解析日志
            //seatunnelParameters.dealOutParam(shellCommandExecutor.getVarPool());
            seatunnelParameters.dealOutParamStx(shellCommandExecutor.getVarPool(), commandExecuteResult.getVarPool());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("The current SeaTunnel task has been interrupted", e);
            setExitStatusCode(EXIT_CODE_FAILURE);
            throw new TaskException("The current SeaTunnel task has been interrupted", e);
        } catch (IllegalThreadStateException e) {
            logger.error("stx任务结束:KILL");
            logger.error("SeaTunnel task error", e);
            setExitStatusCode(EXIT_CODE_FAILURE);
            throw new TaskException("Execute Seatunnel task failed", e);
        } catch (Exception e) {
            logger.error("SeaTunnel task error", e);
            setExitStatusCode(EXIT_CODE_FAILURE);
            throw new TaskException("Execute Seatunnel task failed", e);
        }
    }


    @Override
    public Set<String> getApplicationIds() throws IOException {
        Set<String> appIds = new HashSet<>();
        File file = new File(taskRequest.getLogPath());
        if (!file.exists()) {
            return appIds;
        }

        /*
         * analysis log? get submitted yarn application id
         */
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(taskRequest.getLogPath()), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                String appId = findAppId(line);
                if (StringUtils.isNotEmpty(appId)) {
                    appIds.add(appId);
                }
            }
        }
        return appIds;
    }

    protected String findAppId(String line) {
        Matcher matcher = SEATUNNEL_APPLICATION_REGEX.matcher(line);
        if (matcher.find()) {
            String jobId = matcher.group(1);
            return jobId;
        }
        return null;
    }


    @Override
    public void cancelApplication(boolean cancelApplication) throws Exception {
        logger.info("得到的appids = {}", getApplicationIds());
        // cancel process  pid
        //shellCommandExecutor.cancelApplication();
        List<String> cancelCommand = buildCancelCommand();
        logger.info("运行关闭命令栏是: {}", cancelCommand);
        shellCommandExecutor.cancelSeatunnelApplication(cancelCommand);
    }

    private List<String> buildCancelCommand() throws IOException {
        List<String> args = new ArrayList<>();
        args.add(seatunnelParameters.getEngine().getCommand());
        args.add("-can");
        args.add(String.join(TaskConstants.COMMA, getApplicationIds()));
        return args;
    }

    private String buildCommand() throws Exception {

        List<String> args = new ArrayList<>();
        args.add(seatunnelParameters.getEngine().getCommand());
        args.addAll(buildOptions());

        String command = String.join(" ", args);
        logger.info("SeaTunnel task command: {}", command);

        return command;
    }

    protected List<String> buildOptions() throws Exception {
        List<String> args = new ArrayList<>();
        if (BooleanUtils.isTrue(seatunnelParameters.getUseCustom())) {
            args.add(CONFIG_OPTIONS);
            args.add(buildCustomConfigCommand());
        } else {
            seatunnelParameters.getResourceList().forEach(resourceInfo -> {
                args.add(CONFIG_OPTIONS);
                // TODO Currently resourceName is `/xxx.sh`, it has more `/` and needs to be optimized
                args.add(resourceInfo.getResourceName().substring(1));
            });
        }
        return args;
    }

    protected String buildCustomConfigCommand() throws Exception {
        String config = buildCustomConfigContent();
        String filePath = buildConfigFilePath();
        createConfigFileIfNotExists(config, filePath);

        return filePath;
    }

    private String buildCustomConfigContent() {
        String raw = seatunnelParameters.getRawScript();
        raw = raw.replaceAll("password=\".*?\"", "password=");
        logger.info("raw custom config content : {}", raw);
        String script = seatunnelParameters.getRawScript().replaceAll("\\r\\n", "\n");
        script = parseScript(script);
        return script;
    }

    private String buildConfigFilePath() {
        return String.format("%s/stx_%s.conf", taskExecutionContext.getExecutePath(), taskExecutionContext.getTaskAppId());
    }

    private void createConfigFileIfNotExists(String script, String scriptFile) throws IOException {
        logger.info("tenantCode :{}, task dir:{}", taskExecutionContext.getTenantCode(), taskExecutionContext.getExecutePath());

        if (!Files.exists(Paths.get(scriptFile))) {
            logger.info("generate script file:{}", scriptFile);

            // write data to file
            FileUtils.writeStringToFile(new File(scriptFile), script, StandardCharsets.UTF_8);
        }
    }

    @Override
    public AbstractParameters getParameters() {
        return seatunnelParameters;
    }

    private String parseScript(String script) {
        // combining local and global parameters
        Map<String, Property> paramsMap = taskExecutionContext.getPrepareParamsMap();
        //开始 20240902处理全局参数覆盖任务参数
        String globalParams = taskExecutionContext.getGlobalParams();
        ParameterUtils.convertGlobalParams(paramsMap, globalParams);
        //结束
        return ParameterUtils.convertParameterPlaceholders(script, ParamUtils.convert(paramsMap));
    }

    public void setSeatunnelParameters(JoyadataSeatunnelParameters seatunnelParameters) {
        this.seatunnelParameters = seatunnelParameters;
    }
}
