package org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.dto.ExecSQLResult;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.joyadata.engine.common.beans.constants.Constants;
import com.joyadata.engine.common.beans.dto.DatabaseConnectionDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.dto.engine.EngineModel;
import com.joyadata.engine.common.beans.dto.engine.EngineTransformModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineHttpBaseSinkModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineMaxComputeBaseSinkModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkAdbGpdistModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkArgoHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkClickhouseModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkConsoleHoleModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkDWSPGModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkDataHubModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkDorisModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkElasticsearchModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkFTPModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkHbaseModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkHiveModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkInspurOSSFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkJDBCModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkKafkaModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkKuduModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkLocalFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkLocalFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkMongoDBModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkOssAliFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkOssAliFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkOssHuaweiFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkS3File2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkS3FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkSFTP2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkSFTPModel;
import com.joyadata.engine.common.beans.dto.sink.EngineHttpBaseSinkDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineMaxComputeSinkDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkAdbGpdistDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkArgoHdfsFileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkClickHouseDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDWSPGDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDataHubDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDorisDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkElasticsearchDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkFTPDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkHbaseDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkHdfsFileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkHiveDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkInspurOSSFile2FileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkJDBCDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkKafkaDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkKuduDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkLocalFile2FileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkLocalFileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkMongoDBDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkOssAliFile2FileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkOssAliFileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkOssHuaweiFilDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkS3File2FileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkS3FileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkSFTP2FileDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkSFTPDTO;
import com.joyadata.engine.common.beans.dto.source.EngineMaxComputeSourceDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceDTO;
import com.joyadata.engine.common.beans.enums.DatabaseTypeEnum;
import com.joyadata.engine.common.beans.enums.EngineSinkTypeEnum;
import com.joyadata.engine.common.beans.enums.JoyadataJoinType;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSinkUtils
 * @date 2024/2/29
 */
@Slf4j
public class EngineSinkUtils {

    public static void initSinks(List<EngineSinkDTO> sink,
                                 EngineModel engineModel,
                                 StringRedisTemplate redisTemplate,
                                 String tenantCode,
                                 List<EngineTransformModel> transforms,
                                 List<EngineSourceDTO> source,
                                 Logger logger,
                                 Map<String, List<Tuple2<String, List<String>>>> preSqlMap,
                                 Map<String, List<Tuple2<String, List<String>>>> postSqlMap) {
        sink.forEach(t -> {
            Optional<EngineSourceDTO> engineSource = EngineInitUtils.getEngineSource(t.getSourceTableName(), transforms, source);
            if (engineSource.isPresent()) {
                try {
                    //如果是jdbc类型，修正正确的type，EngineSinkDTO
                    if (EngineSinkTypeEnum.JDBC == t.getType()) {
                        // @TODO: 如果使用Doris的Jdbc写入，注释掉下面代码
                        fixEngineSinkDTO(t, redisTemplate, tenantCode, logger);
                    }
                    switch (t.getType()) {
                        case JDBC:
                            engineModel.getJdbcSinks().add(initJdbcSinks(t, redisTemplate, tenantCode, logger, preSqlMap, postSqlMap));
                            break;
                        case CLICKHOUSE:
                            engineModel.getClickhouseSinks().add(initClickHouseSink(t, redisTemplate, tenantCode, preSqlMap, postSqlMap));
                            break;
                        case KAFKA:
                            engineModel.getKafkaSinks().add(initKafkaSink(t, redisTemplate, tenantCode));
                            break;
                        case HIVE:
                        case INCEPTOR:
                            engineModel.getHiveSinks().add(initHiveSink(t, redisTemplate, tenantCode));
                            break;
                        case HDFSFILE:
                            engineModel.getHdfsFileSinks().add(initHdfsFileSink(t, redisTemplate, tenantCode));
                            break;
                        case MONGODB:
                            engineModel.getMongodbSinks().add(initMongoDBSink(t, redisTemplate, tenantCode));
                            break;
                        case KUDU:
                            engineModel.getKuduSinks().add(initKuduSink(t, redisTemplate, tenantCode));
                            break;
                        case LOCALFILE:
                            initLocalFileSink(engineModel, t, redisTemplate, tenantCode);
                            break;
                        case SFTPFILE:
                            //engineModel.getSftpSinks().add(initSftpSink(t, redisTemplate, tenantCode));
                            initSftpSink(engineModel, t, redisTemplate, tenantCode);
                            break;
                        case FTPFILE:
                            //engineModel.getFtpSinks().add(initFtpSink(t, redisTemplate, tenantCode));
                            initFtpSink(engineModel, t, redisTemplate, tenantCode);
                            break;
                        case OSS_ALI:
                            engineModel.getOssAliSinks().add(initOssAliSink(t, redisTemplate, tenantCode));
                            break;
                        case S3FILE:
                            engineModel.getS3FileSinks().add(initS3FileSink(t, redisTemplate, tenantCode));
                            break;
                        case DORIS:
                            Optional<EngineTransformModel> any = transforms.stream().filter(x -> x.getResultTableName().equals(t.getSourceTableName())).findAny();
                            if (any.isPresent()) {
                                EngineTransformModel model = any.get();
                                if (null != model.getFieldMapper()) {
                                    List<String> queryMetadata = getNewMetadata(t.getDorisSink(), tenantCode, redisTemplate);
                                    log.info("获取表{}的元数据是 {}", t.getDorisSink().getTable(), queryMetadata);
                                    if (null != queryMetadata) {
                                        List<String> lowerMetaData = queryMetadata.stream().map(s -> s.replaceAll("`", "")).map(String::toLowerCase).collect(Collectors.toList());
                                        LinkedHashMap<String, String> oldFieldMapper = model.getFieldMapper();
                                        int index = transforms.indexOf(model);
                                        LinkedHashMap<String, String> newFieldMapper = updateFieldMapper(oldFieldMapper, lowerMetaData, queryMetadata);
                                        model.setFieldMapper(newFieldMapper);
                                        transforms.set(index, model);
                                    }
                                }

                            }
                            engineModel.getDorisSinks().add(initDorisSink(t, redisTemplate, tenantCode));
                            break;
                        case OSS_HUAWEI:
                            engineModel.getOssHuaweiSinks().add(initOssHuaweiSink(t, redisTemplate, tenantCode));
                            break;
                        case CONSOLE_HOLE:
                            engineModel.getConsoleHoleSinks().add(initConsoleHoleSink(t));
                            break;
                        case DATA_HUB:
                            engineModel.getDataHubSinks().add(initDataHubSink(t, redisTemplate, tenantCode));
                            break;
                        case ARGO_HDFS_FILE:
                            engineModel.getArgoHdfsFileSinks().add(initArgoHdfsFileSink(t, redisTemplate, tenantCode));
                            break;
                        case ADB_GPDIST:
                            engineModel.getAdbGpdistSinks().add(initAdbGpdistSink(t, redisTemplate, tenantCode));
                            break;
                        case ELASTICSEARCH:
                            engineModel.getElasticsearchSinks().add(initElasticsearchSink(t, redisTemplate, tenantCode));
                            break;
                        case HBASE:
                            engineModel.getHbaseSinks().add(initHbaseSink(t, redisTemplate, tenantCode));
                            break;
                        case LOCALFILE2FILE:
                            engineModel.getLocalFile2FileSinks().add(initLocalFile2FileSink(t, engineSource.get()));
                            break;
                        case OSS_ALI2FILE:
                            engineModel.getOssAliFile2FileSinks().add(initOssAliFile2FileSink(t, redisTemplate, tenantCode, engineSource.get(), logger));
                            break;
                        case S3FILE2FILE:
                            engineModel.getS3File2FileSinks().add(initS3File2FileSink(t, redisTemplate, tenantCode, engineSource.get(), logger));
                            break;
                        case SFTPFILE2FILE:
                            engineModel.getSftpFile2FileSinks().add(initSftpFile2FileSink(t, redisTemplate, tenantCode, engineSource.get(), logger));
                            break;
                        case INSPUROSSFILE2FILE:
                            engineModel.getInspurOSSFile2FileSinks().add(initInspurOSSFile2FileSink(t, redisTemplate, tenantCode, engineSource.get(), logger));
                            break;
                        case DWS_PG:
                            engineModel.getDwspgSinks().add(initDwspgSink(t, redisTemplate, tenantCode));
                            break;
                        case HTTP:
                            engineModel.getHttpBaseSinks().add(initHttpBaseSink(t, redisTemplate, tenantCode));
                            break;
                        case MAXCOMPUTE:
                            engineModel.getMaxComputeSinks().add(initMaxComputeSink(t, redisTemplate, tenantCode, logger));
                            break;
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                throw new RuntimeException("没有对应源端！" + t.getSourceTableName() + " type " + t.getType());
            }
        });

    }

    private static EngineMaxComputeBaseSinkModel initMaxComputeSink(EngineSinkDTO t, StringRedisTemplate redisTemplate, String tenantCode, Logger logger) {
        EngineMaxComputeBaseSinkModel maxComputeBaseSinkModel = new EngineMaxComputeBaseSinkModel();
        EngineMaxComputeSinkDTO dto = t.getMaxComputeSink();
        String datasourceInfoId = dto.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "目标端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        //页面传的有以页面为准
        String projectName = dto.getProjectName();
        if (StringUtils.isNotBlank(projectName)) {
            databaseConnectionDTO.setProject(projectName);
        }
        maxComputeBaseSinkModel.setAccessId(databaseConnectionDTO.getAccessId());
        maxComputeBaseSinkModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        maxComputeBaseSinkModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        maxComputeBaseSinkModel.setProject(databaseConnectionDTO.getProject());
        maxComputeBaseSinkModel.setTableName(dto.getTableName());
        maxComputeBaseSinkModel.setPartitionSpec(dto.getPartitionValue());
        maxComputeBaseSinkModel.setSourceTableName(t.getSourceTableName());
        return maxComputeBaseSinkModel;
    }

    private static EngineHttpBaseSinkModel initHttpBaseSink(EngineSinkDTO t, StringRedisTemplate redisTemplate, String tenantCode) {

        EngineHttpBaseSinkDTO httpBaseSink = t.getHttpBaseSink();

        String sourceTableName = t.getSourceTableName();

        EngineHttpBaseSinkModel httpBaseSinkModel = new EngineHttpBaseSinkModel();

        httpBaseSinkModel.setSourceTableName(sourceTableName);
        httpBaseSinkModel.setUrl(httpBaseSink.getUrl());
        httpBaseSinkModel.setMethod(httpBaseSink.getMethod());
        httpBaseSinkModel.setParams(httpBaseSink.getParams());
        httpBaseSinkModel.setHeaders(httpBaseSink.getHeaders());

        List<LinkedHashMap<String, String>> sinkOutputColumns = httpBaseSink.getSinkOutputColumns();
        List<LinkedHashMap<String, String>> linkedHashMaps = new ArrayList<>();
        for (LinkedHashMap<String, String> map : sinkOutputColumns) {
            LinkedHashMap<String, String> linkedHashMap = new LinkedHashMap<>();
            String srcColumnName = map.get("srcColumnName") == null ? "" : map.get("srcColumnName");
            linkedHashMap.put("srcColumnName", srcColumnName);

            String srcColumnType = map.get("srcColumnType") == null ? "" : map.get("srcColumnType");
            if (StringUtils.isNotEmpty(srcColumnType)) {
                linkedHashMap.put("srcColumnType", srcColumnType);
            }

            String destFieldName = map.get("columnName") == null ? "" : map.get("columnName");
            linkedHashMap.put("destFieldName", destFieldName);

            String destFieldType = map.get("columnType") == null ? "" : map.get("columnType");
            linkedHashMap.put("destFieldType", destFieldType);

            String destLength = map.get("columnLength") == null ? "" : map.get("columnLength");
            linkedHashMap.put("destLength", destLength);

            String defaultValue = map.get("defaultValue") == null ? "" : map.get("defaultValue");
            linkedHashMap.put("defaultValue", defaultValue);

            linkedHashMaps.add(linkedHashMap);
        }
        httpBaseSinkModel.setSinkOutputColumns(linkedHashMaps);

        //httpBaseSinkModel.setSourceTableName();
        httpBaseSinkModel.setBodySendType(httpBaseSink.getBodySendType());
        httpBaseSinkModel.setConnectTimeoutMs(httpBaseSink.getConnectTimeoutMs());

        httpBaseSinkModel.setDateFormat(httpBaseSink.getDateFormat());
        httpBaseSinkModel.setDatetimeFormat(httpBaseSink.getDatetimeFormat());
        httpBaseSinkModel.setTimeFormat(httpBaseSink.getTimeFormat());


        return httpBaseSinkModel;
    }

    private static EngineSinkDWSPGModel initDwspgSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkDWSPGDTO dwspgSink = sink.getDwspgSink();
        String datasourceInfoId = dwspgSink.getDatasourceInfoId();

        //获取数据源信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSinkDWSPGModel engineSinkDWSPGModel = new EngineSinkDWSPGModel();
        String path = dwspgSink.getPath();
        String dwsTable = dwspgSink.getTableName();
        engineSinkDWSPGModel.setPath(path);
        if (path.endsWith("/")) {
            engineSinkDWSPGModel.setTmpPath(path + "tmp");
            engineSinkDWSPGModel.setFinalName(path + dwsTable + "_" + System.currentTimeMillis() + ".csv");
        } else {
            engineSinkDWSPGModel.setTmpPath(path + "/tmp");
            engineSinkDWSPGModel.setFinalName(path + "/" + dwsTable + "_" + System.currentTimeMillis() + ".csv");
        }
        engineSinkDWSPGModel.setDwsUrl(databaseConnectionDTO.getUrl());
        engineSinkDWSPGModel.setDwsDriver(databaseConnectionDTO.getDriverClassName());
        engineSinkDWSPGModel.setDwsUser(databaseConnectionDTO.getUserName());
        engineSinkDWSPGModel.setDwsPassword(databaseConnectionDTO.getPassword());
        engineSinkDWSPGModel.setDwsTable(dwsTable);
        engineSinkDWSPGModel.setDwsSchema(dwspgSink.getSchemaName());
        engineSinkDWSPGModel.setDwsGdsAddress(dwspgSink.getGdsAddress());
        List<MetadataColumnDTO> fieldList = dwspgSink.getFieldList();
        if (null != fieldList && fieldList.size() > 0) {
            List<String> fields = fieldList.stream().map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
            engineSinkDWSPGModel.setDwsFields(JSON.toJSONString(fields));
        }
        return engineSinkDWSPGModel;
    }

    private static List<String> getNewMetadata(EngineSinkDorisDTO dorisDTO, String tenantCode, StringRedisTemplate redisTemplate) throws Exception {
        String datasourceInfoId = dorisDTO.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        //String sql ="show create table "+dorisDTO.getTable();
        String sql = "SELECT t1.TABLE_SCHEMA AS DB_NAME ,t1.TABLE_NAME AS TABLE_NAME \n" +
                "      ,t1.TABLE_COMMENT AS TABLE_COMMENT_NAME\n" +
                "      ,t2.COLUMN_NAME AS COLUMN_NAME\n" +
                "      ,t2.COLUMN_TYPE AS COLUMN_TYPE_NAME \n" +
                "      ,t2.COLUMN_COMMENT AS COLUMN_COMMENT_NAME\n" +
                "      ,t2.ORDINAL_POSITION AS COLUMN_IDX\n" +
                "FROM information_schema.tables t1\n" +
                "JOIN information_schema.columns t2\n" +
                "     ON t1.TABLE_SCHEMA = t2.TABLE_SCHEMA AND t1.TABLE_NAME = t2.TABLE_NAME\n" +
                "WHERE lower(t1.table_schema) = '" + databaseConnectionDTO.getSchemaName().toLowerCase() + "'\n" +
                "  AND lower(t1.TABLE_NAME) = '" + dorisDTO.getTable().toLowerCase() + "'\n" +
                "ORDER BY DB_NAME, TABLE_NAME, COLUMN_IDX";
        log.info("执行的查询语句是{}", sql);
        try {
            ExecSQLResult execSQLResult = DatasourceUtils.execSQL(dorisDTO.getDatasourceInfoId(), sql, true, tenantCode);
            log.info("获取元数据sql结果:{}", execSQLResult);
            List<String> columnNames = new ArrayList<>();
            List<JSONObject> resultSet = execSQLResult.getResultSet();
            if (null != resultSet && resultSet.size() == 1) {
                resultSet.forEach(t -> columnNames.add(t.getString("COLUMN_NAME")));
                log.info("获取到的元数据是{}", columnNames);
            }
            return columnNames;
        } catch (SQLException e) {
            log.error("获取元数据失败 sql:{}，原因:{}", sql, e.getMessage());
            return null;
        }
    }

    private static LinkedHashMap<String, String> updateFieldMapper(LinkedHashMap<String, String> oldFieldMapper, List<String> lowerMetaData, List<String> queryMetadata) {
        LinkedHashMap<String, String> newFieldMapper = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : oldFieldMapper.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String lowerCaseValue = value.toLowerCase();
            if (lowerMetaData.contains(lowerCaseValue)) {
                int index = lowerMetaData.indexOf(lowerCaseValue);
                newFieldMapper.put(key, queryMetadata.get(index));
            } else {
                newFieldMapper.put(key, value);
            }
        }
        return newFieldMapper;
    }

    private static EngineSinkInspurOSSFile2FileModel initInspurOSSFile2FileSink(EngineSinkDTO engineSinkDTO,
                                                                                StringRedisTemplate redisTemplate,
                                                                                String tenantCode,
                                                                                EngineSourceDTO engineSourceDTO,
                                                                                Logger logger) {
        Boolean isFile = false;
        switch (engineSourceDTO.getType()) {
            case LOCALFILE2FILE:
                isFile = engineSourceDTO.getLocalFile2FileSource().getIsFile();
                break;
            case OSS_ALI2FILE:
                isFile = engineSourceDTO.getOssAliFile2FileSource().getIsFile();
                break;
            case S3FILE2FILE:
                isFile = engineSourceDTO.getS3File2FileSource().getIsFile();
                break;
            case SFTPFILE2FILE:
                isFile = engineSourceDTO.getSftp2FileSource().getIsFile();
                break;
            case INSPUROSSFILE2FILE:
                isFile = engineSourceDTO.getInspurOSSFile2FileSource().getIsFile();
                break;
        }

        EngineSinkInspurOSSFile2FileDTO inspurOSSFile2FileSink = engineSinkDTO.getInspurOSSFile2FileSink();
        String datasourceInfoId = inspurOSSFile2FileSink.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "目标端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkInspurOSSFile2FileModel inspurOSSFile2FileModel = new EngineSinkInspurOSSFile2FileModel();
        String pathString = inspurOSSFile2FileSink.getPath();
        inspurOSSFile2FileModel.setLoadType(inspurOSSFile2FileSink.getLoadType());
        inspurOSSFile2FileModel.setPath(pathString);
        if (pathString.endsWith("/")) {
            inspurOSSFile2FileModel.setTmpPath(pathString + "tmp");
        } else {
            inspurOSSFile2FileModel.setTmpPath(pathString + "/tmp");
        }
        inspurOSSFile2FileModel.setBucket("s3a://" + databaseConnectionDTO.getBucket());
        inspurOSSFile2FileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        inspurOSSFile2FileModel.setSecretKey(databaseConnectionDTO.getAccessSecret());
        inspurOSSFile2FileModel.setFsS3aEndpoint(databaseConnectionDTO.getEndpoint());
        inspurOSSFile2FileModel.setFsS3aAwsCredentialsProvider(inspurOSSFile2FileSink.getFsS3aAwsCredentialsProvider());
        inspurOSSFile2FileModel.setValidateFile(inspurOSSFile2FileSink.getValidateFile());
        inspurOSSFile2FileModel.setValidateContent(inspurOSSFile2FileSink.getValidateContent());
        inspurOSSFile2FileModel.setIsFile(String.valueOf(isFile));
        inspurOSSFile2FileModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        if (isFile) {
            Path path = Paths.get(pathString);
            String fileName = path.getFileName().toString();
            inspurOSSFile2FileModel.setFileName(fileName);
            String subPathString = pathString.substring(0, pathString.lastIndexOf(fileName) - 1);
            inspurOSSFile2FileModel.setPath(StringUtils.defaultIfBlank(subPathString, "/"));
            inspurOSSFile2FileModel.setTmpPath(subPathString + "/tmp");
        }

        return inspurOSSFile2FileModel;
    }

    private static EngineSinkSFTP2FileModel initSftpFile2FileSink(EngineSinkDTO engineSinkDTO,
                                                                  StringRedisTemplate redisTemplate,
                                                                  String tenantCode,
                                                                  EngineSourceDTO engineSourceDTO,
                                                                  Logger logger) {
        Boolean isFile = false;
        switch (engineSourceDTO.getType()) {
            case LOCALFILE2FILE:
                isFile = engineSourceDTO.getLocalFile2FileSource().getIsFile();
                break;
            case OSS_ALI2FILE:
                isFile = engineSourceDTO.getOssAliFile2FileSource().getIsFile();
                break;
            case S3FILE2FILE:
                isFile = engineSourceDTO.getS3File2FileSource().getIsFile();
                break;
            case SFTPFILE2FILE:
                isFile = engineSourceDTO.getSftp2FileSource().getIsFile();
                break;
            case INSPUROSSFILE2FILE:
                isFile = engineSourceDTO.getInspurOSSFile2FileSource().getIsFile();
                break;
        }

        EngineSinkSFTP2FileDTO sftp2FileSink = engineSinkDTO.getSftp2FileSink();
        String datasourceInfoId = sftp2FileSink.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "目标端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkSFTP2FileModel sftp2FileModel = new EngineSinkSFTP2FileModel();
        String pathString = sftp2FileSink.getPath();
        sftp2FileModel.setPath(pathString);
        if (pathString.endsWith("/")) {
            sftp2FileModel.setTmpPath(pathString + "tmp");
        } else {
            sftp2FileModel.setTmpPath(pathString + "/tmp");
        }
        sftp2FileModel.setHost(databaseConnectionDTO.getHost());
        sftp2FileModel.setPort(databaseConnectionDTO.getPort());
        sftp2FileModel.setUser(databaseConnectionDTO.getUserName());
        sftp2FileModel.setPassword(databaseConnectionDTO.getPassword());
        sftp2FileModel.setValidateFile(sftp2FileSink.getValidateFile());
        sftp2FileModel.setValidateContent(sftp2FileSink.getValidateContent());
        sftp2FileModel.setIsFile(String.valueOf(isFile));
        sftp2FileModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        if (isFile) {
            Path path = Paths.get(pathString);
            String fileName = path.getFileName().toString();
            sftp2FileModel.setFileName(fileName);
            String subPathString = pathString.substring(0, pathString.lastIndexOf(fileName) - 1);
            sftp2FileModel.setPath(StringUtils.defaultIfBlank(subPathString, "/"));
            sftp2FileModel.setTmpPath(subPathString + "/tmp");
        }

        return sftp2FileModel;
    }

    private static EngineSinkS3File2FileModel initS3File2FileSink(EngineSinkDTO engineSinkDTO,
                                                                  StringRedisTemplate redisTemplate,
                                                                  String tenantCode,
                                                                  EngineSourceDTO engineSourceDTO,
                                                                  Logger logger) {
        Boolean isFile = false;
        switch (engineSourceDTO.getType()) {
            case LOCALFILE2FILE:
                isFile = engineSourceDTO.getLocalFile2FileSource().getIsFile();
                break;
            case OSS_ALI2FILE:
                isFile = engineSourceDTO.getOssAliFile2FileSource().getIsFile();
                break;
            case S3FILE2FILE:
                isFile = engineSourceDTO.getS3File2FileSource().getIsFile();
                break;
            case SFTPFILE2FILE:
                isFile = engineSourceDTO.getSftp2FileSource().getIsFile();
                break;
            case INSPUROSSFILE2FILE:
                isFile = engineSourceDTO.getInspurOSSFile2FileSource().getIsFile();
                break;
        }

        EngineSinkS3File2FileDTO s3File2FileSink = engineSinkDTO.getS3File2FileSink();

        String datasourceInfoId = s3File2FileSink.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "目标端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkS3File2FileModel s3File2FileModel = new EngineSinkS3File2FileModel();
        String pathString = s3File2FileSink.getPath();
        s3File2FileModel.setPath(pathString);
        if (pathString.endsWith("/")) {
            s3File2FileModel.setTmpPath(pathString + "tmp");
        } else {
            s3File2FileModel.setTmpPath(pathString + "/tmp");
        }
        s3File2FileModel.setBucket("s3a://" + databaseConnectionDTO.getBucket());
        s3File2FileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        s3File2FileModel.setSecretKey(databaseConnectionDTO.getAccessSecret());
        s3File2FileModel.setFsS3aEndpoint(databaseConnectionDTO.getEndpoint());
        s3File2FileModel.setValidateFile(s3File2FileSink.getValidateFile());
        s3File2FileModel.setValidateContent(s3File2FileSink.getValidateContent());
        s3File2FileModel.setIsFile(String.valueOf(isFile));
        s3File2FileModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        if (isFile) {
            Path path = Paths.get(pathString);
            String fileName = path.getFileName().toString();
            s3File2FileModel.setFileName(fileName);
            String subPathString = pathString.substring(0, pathString.lastIndexOf(fileName) - 1);
            s3File2FileModel.setPath(StringUtils.defaultIfBlank(subPathString, "/"));
            s3File2FileModel.setTmpPath(subPathString + "/tmp");
        }

        return s3File2FileModel;
    }

    private static EngineSinkOssAliFile2FileModel initOssAliFile2FileSink(EngineSinkDTO engineSinkDTO,
                                                                          StringRedisTemplate redisTemplate,
                                                                          String tenantCode,
                                                                          EngineSourceDTO engineSourceDTO,
                                                                          Logger logger) {

        Boolean isFile = false;
        switch (engineSourceDTO.getType()) {
            case LOCALFILE2FILE:
                isFile = engineSourceDTO.getLocalFile2FileSource().getIsFile();
                break;
            case OSS_ALI2FILE:
                isFile = engineSourceDTO.getOssAliFile2FileSource().getIsFile();
                break;
            case S3FILE2FILE:
                isFile = engineSourceDTO.getS3File2FileSource().getIsFile();
                break;
            case SFTPFILE2FILE:
                isFile = engineSourceDTO.getSftp2FileSource().getIsFile();
                break;
            case INSPUROSSFILE2FILE:
                isFile = engineSourceDTO.getInspurOSSFile2FileSource().getIsFile();
                break;
        }

        EngineSinkOssAliFile2FileDTO ossAliFile2FileSink = engineSinkDTO.getOssAliFile2FileSink();

        String datasourceInfoId = ossAliFile2FileSink.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "目标端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkOssAliFile2FileModel ossAliFile2FileModel = new EngineSinkOssAliFile2FileModel();
        String pathString = ossAliFile2FileSink.getPath();
        ossAliFile2FileModel.setPath(pathString);
        if (pathString.endsWith("/")) {
            ossAliFile2FileModel.setTmpPath(pathString + "tmp");
        } else {
            ossAliFile2FileModel.setTmpPath(pathString + "/tmp");
        }
        ossAliFile2FileModel.setBucket("oss://" + databaseConnectionDTO.getBucket());
        ossAliFile2FileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        ossAliFile2FileModel.setAccessSecret(databaseConnectionDTO.getAccessSecret());
        ossAliFile2FileModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        ossAliFile2FileModel.setValidateFile(ossAliFile2FileSink.getValidateFile());
        ossAliFile2FileModel.setValidateContent(ossAliFile2FileSink.getValidateContent());
        ossAliFile2FileModel.setIsFile(String.valueOf(isFile));
        ossAliFile2FileModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        if (isFile) {
            Path path = Paths.get(pathString);
            String fileName = path.getFileName().toString();
            ossAliFile2FileModel.setFileName(fileName);
            String subPathString = pathString.substring(0, pathString.lastIndexOf(fileName) - 1);
            ossAliFile2FileModel.setPath(StringUtils.defaultIfBlank(subPathString, "/"));
            ossAliFile2FileModel.setTmpPath(subPathString + "/tmp");
        }

        return ossAliFile2FileModel;
    }

    private static EngineSinkLocalFile2FileModel initLocalFile2FileSink(EngineSinkDTO engineSinkDTO, EngineSourceDTO engineSourceDTO) {
        Boolean isFile = false;
        switch (engineSourceDTO.getType()) {
            case LOCALFILE2FILE:
                isFile = engineSourceDTO.getLocalFile2FileSource().getIsFile();
                break;
            case OSS_ALI2FILE:
                isFile = engineSourceDTO.getOssAliFile2FileSource().getIsFile();
                break;
            case S3FILE2FILE:
                isFile = engineSourceDTO.getS3File2FileSource().getIsFile();
                break;
            case SFTPFILE2FILE:
                isFile = engineSourceDTO.getSftp2FileSource().getIsFile();
                break;
            case INSPUROSSFILE2FILE:
                isFile = engineSourceDTO.getInspurOSSFile2FileSource().getIsFile();
                break;
        }

        EngineSinkLocalFile2FileDTO localFile2FileSink = engineSinkDTO.getLocalFile2FileSink();

        EngineSinkLocalFile2FileModel localFile2FileModel = new EngineSinkLocalFile2FileModel();
        String pathString = localFile2FileSink.getPath();

        localFile2FileModel.setPath(pathString);
        localFile2FileModel.setIsFile(String.valueOf(isFile));
        if (isFile) {
            Path path = Paths.get(pathString);
            String fileName = path.getFileName().toString();
            localFile2FileModel.setFileName(fileName);
            String subPathString = pathString.substring(0, pathString.lastIndexOf(fileName) - 1);
            localFile2FileModel.setPath(StringUtils.defaultIfBlank(subPathString, "/"));
        }
        localFile2FileModel.setValidateFile(localFile2FileSink.getValidateFile());
        localFile2FileModel.setValidateContent(localFile2FileSink.getValidateContent());
        localFile2FileModel.setSourceTableName(engineSinkDTO.getSourceTableName());

        return localFile2FileModel;
    }

    /// /如果是jdbc类型，修正正确的type，EngineSinkDTO
    private static void fixEngineSinkDTO(EngineSinkDTO engineSinkDTO, StringRedisTemplate redisTemplate, String tenantCode, Logger logger) {
        EngineSinkJDBCDTO jdbcSink = engineSinkDTO.getJdbcSink();
        String datasourceInfoId = jdbcSink.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "目标端");
        //数据源转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        switch (DatabaseTypeEnum.getDatabaseTypeEnum(datasourceDTO.getDataType().toLowerCase())) {
            case DORIS:
                EngineSinkDorisDTO dorisSink = new EngineSinkDorisDTO();
                BeanUtils.copyProperties(jdbcSink, dorisSink);
                jdbcToDoris(jdbcSink, dorisSink);
                engineSinkDTO.setType(EngineSinkTypeEnum.DORIS);
                engineSinkDTO.setDorisSink(dorisSink);
                engineSinkDTO.setJdbcSink(null);
                break;
        }

    }

    private static void jdbcToDoris(EngineSinkJDBCDTO jdbcSink, EngineSinkDorisDTO dorisSink) {
        dorisSink.setTable(jdbcSink.getTableName());
        dorisSink.setSinkEnable2pc("false");
        dorisSink.setDataSaveMode("APPEND_DATA");
        Map<String, String> dorisConfig = new HashMap<>();
        dorisConfig.put("format", "json");
        dorisConfig.put("read_json_by_line", "true");
        dorisSink.setDorisConfig(dorisConfig);
    }

    private static EngineSinkHbaseModel initHbaseSink(EngineSinkDTO engineSinkDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkHbaseDTO hbaseSink = engineSinkDTO.getHbaseSink();
        String datasourceInfoId = hbaseSink.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        Map<String, Object> hbaseJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        EngineSinkHbaseModel engineSinkHbaseModel = new EngineSinkHbaseModel();
        engineSinkHbaseModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        engineSinkHbaseModel.setZookeeperQuorum(hbaseJson.get("hbase_quorum") + "");
        engineSinkHbaseModel.setTableName(hbaseSink.getTableName());
        List<String> rowkeyColumn = hbaseSink.getRowkeyColumn();
        if (CollectionUtils.isNotEmpty(rowkeyColumn)) {
            engineSinkHbaseModel.setRowkeyColumn(JSONObject.toJSONString(rowkeyColumn));
            engineSinkHbaseModel.setRowkeyDelimiter(",");
        }
        List<String> familyName = hbaseSink.getFamilyName();
        //拼接familyName
        if (CollectionUtils.isNotEmpty(familyName)) {
            List<String> finalFamilyName = new ArrayList<>();
            for (String str : familyName) {
                StringBuilder stringBuilder = new StringBuilder();
                if (str.contains(":")) {
                    String[] split = str.split(":");
                    stringBuilder.append("\"").append(split[1]).append("\"").append("=\"").append(split[0]).append("\"");
                    finalFamilyName.add(stringBuilder.toString());
                }
            }
            engineSinkHbaseModel.setFamilyName(finalFamilyName);
        }
        //下载hbase数据源的文件并给Model设置文件路径
        try {
            String filePath = HbaseFileUtils.setFile(hbaseJson, hbaseSink.getDatasourceInfoId());
            if (null != hbaseJson.get("openKerberos") && "open".equals(hbaseJson.get("openKerberos").toString())) {
                engineSinkHbaseModel.setUser(hbaseJson.get("authUser") + "");
                engineSinkHbaseModel.setServerPrincipal(hbaseJson.get("serverPrincipal") + "");
            }
            engineSinkHbaseModel.setFilePath(filePath + "/");
        } catch (Exception e) {
            log.error("下载hbase数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("下载hbase数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
        }
        return engineSinkHbaseModel;
    }

    private static EngineSinkElasticsearchModel initElasticsearchSink(EngineSinkDTO engineSinkDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkElasticsearchDTO elasticsearchSink = engineSinkDTO.getElasticsearchSink();
        String datasourceInfoId = elasticsearchSink.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.info("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        //组装对象
        EngineSinkElasticsearchModel sinkElasticsearchModel = new EngineSinkElasticsearchModel();
        sinkElasticsearchModel.setDatasourceInfoId(datasourceInfoId);
        sinkElasticsearchModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        sinkElasticsearchModel.setHosts(databaseConnectionDTO.getUrl());
        if (StringUtils.isBlank(elasticsearchSink.getIndex())) {
            throw new RuntimeException("index不可为空！");
        }
        sinkElasticsearchModel.setIndex(elasticsearchSink.getIndex());
        sinkElasticsearchModel.setIndexType(elasticsearchSink.getIndexType());
        sinkElasticsearchModel.setUsername(databaseConnectionDTO.getUserName());
        sinkElasticsearchModel.setPassword(databaseConnectionDTO.getPassword());
        sinkElasticsearchModel.setMaxRetryCount(elasticsearchSink.getMaxRetryCount());
        sinkElasticsearchModel.setMaxBatchSize(elasticsearchSink.getMaxBatchSize());
        sinkElasticsearchModel.setTlsVerifyCertificate(elasticsearchSink.getTlsVerifyCertificate());
        sinkElasticsearchModel.setTlsVerifyHostname(elasticsearchSink.getTlsVerifyHostname());
        if (StringUtils.isBlank(elasticsearchSink.getSchemaSaveMode())) {
            throw new RuntimeException("schemaSaveMode不可为空！");
        }
        sinkElasticsearchModel.setSchemaSaveMode(elasticsearchSink.getSchemaSaveMode());
        if (StringUtils.isBlank(elasticsearchSink.getDataSaveMode())) {
            throw new RuntimeException("dataSaveMode不可为空！");
        }
        sinkElasticsearchModel.setDataSaveMode(elasticsearchSink.getDataSaveMode());


        return sinkElasticsearchModel;
    }

    private static EngineSinkOssHuaweiFileModel initOssHuaweiSink(EngineSinkDTO engineSinkDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkOssHuaweiFilDTO ossHuaweiFileSink = engineSinkDTO.getOssHuaweiFileSink();
        String datasourceInfoId = ossHuaweiFileSink.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSinkOssHuaweiFileModel sinkOssHuaweiFileModel = new EngineSinkOssHuaweiFileModel();
        sinkOssHuaweiFileModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        sinkOssHuaweiFileModel.setPath(ossHuaweiFileSink.getPath());
        sinkOssHuaweiFileModel.setTmpPath(ossHuaweiFileSink.getTmpPath());
        sinkOssHuaweiFileModel.setBucket("obs://" + databaseConnectionDTO.getBucket());
        sinkOssHuaweiFileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        sinkOssHuaweiFileModel.setAccessSecret(databaseConnectionDTO.getAccessSecret());
        sinkOssHuaweiFileModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        sinkOssHuaweiFileModel.setCustomFilename(ossHuaweiFileSink.getCustomFilename());
        if (StringUtils.isNotBlank(ossHuaweiFileSink.getFileNameExpression())) {
            sinkOssHuaweiFileModel.setFileNameExpression(ossHuaweiFileSink.getFileNameExpression() + "_${transactionId}");
        }
        sinkOssHuaweiFileModel.setFilenameTimeFormat(ossHuaweiFileSink.getFilenameTimeFormat());
        sinkOssHuaweiFileModel.setFileFormatType(ossHuaweiFileSink.getFileFormatType());
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != ossHuaweiFileSink.getFieldDelimiter() && !ossHuaweiFileSink.getFieldDelimiter().equals("\\001")) {
            sinkOssHuaweiFileModel.setFieldDelimiter(ossHuaweiFileSink.getFieldDelimiter());
        }
        sinkOssHuaweiFileModel.setRowDelimiter(ossHuaweiFileSink.getRowDelimiter());
        sinkOssHuaweiFileModel.setHavePartition(ossHuaweiFileSink.getHavePartition());
        if (null != ossHuaweiFileSink.getPartitionBy() && ossHuaweiFileSink.getPartitionBy().size() > 0) {
            sinkOssHuaweiFileModel.setPartitionBy(JSONObject.toJSONString(ossHuaweiFileSink.getPartitionBy()));
        }
        sinkOssHuaweiFileModel.setPartitionDirExpression(ossHuaweiFileSink.getPartitionDirExpression());
        sinkOssHuaweiFileModel.setIsPartitionFieldWriteInFile(ossHuaweiFileSink.getIsPartitionFieldWriteInFile());
        if (null != ossHuaweiFileSink.getSinkColumns() && ossHuaweiFileSink.getSinkColumns().size() > 0) {
            sinkOssHuaweiFileModel.setSinkColumns(JSONObject.toJSONString(ossHuaweiFileSink.getSinkColumns()));
        }
        sinkOssHuaweiFileModel.setIsEnableTransaction(ossHuaweiFileSink.getIsEnableTransaction());
        sinkOssHuaweiFileModel.setBatchSize(ossHuaweiFileSink.getBatchSize());
        sinkOssHuaweiFileModel.setMaxRowsInMemory(ossHuaweiFileSink.getMaxRowsInMemory());
        sinkOssHuaweiFileModel.setCompressCodec(ossHuaweiFileSink.getCompressCodec());
        sinkOssHuaweiFileModel.setSheetName(ossHuaweiFileSink.getSheetName());

        //校验内容
        String validateFile = ossHuaweiFileSink.getValidateFile();
        if (StringUtils.isNoneBlank(validateFile)) {
            sinkOssHuaweiFileModel.setValidateFile(validateFile);
        }
        String validateContent = ossHuaweiFileSink.getValidateContent();
        if (StringUtils.isNoneBlank(validateContent)) {
            sinkOssHuaweiFileModel.setValidateContent(validateContent);
        }
        sinkOssHuaweiFileModel.setEmptyDataStrategy(ossHuaweiFileSink.getEmptyDataStrategy());
        sinkOssHuaweiFileModel.setValidates(Boolean.toString(ossHuaweiFileSink.isValidates()));
        sinkOssHuaweiFileModel.setDateFormat(ossHuaweiFileSink.getDateFormat());
        sinkOssHuaweiFileModel.setDatetimeFormat(ossHuaweiFileSink.getDatetimeFormat());
        sinkOssHuaweiFileModel.setTimeFormat(ossHuaweiFileSink.getTimeFormat());
        sinkOssHuaweiFileModel.setEndpoint(ossHuaweiFileSink.getEndpoint());
        sinkOssHuaweiFileModel.setTdsqlPartitionName(engineSinkDTO.getTdsqlPartitionName());
        return sinkOssHuaweiFileModel;
    }

    private static EngineSinkS3FileModel initS3FileSink(EngineSinkDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkS3FileDTO s3FileSink = engineSourceDTO.getS3FileSink();
        String datasourceInfoId = s3FileSink.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        Map<String, Object> datasourceMap = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        String secretKey = datasourceMap.get("secretKey") + "";
        String endPoint = datasourceMap.get("endPoint") + "";
        String accessKey = datasourceMap.get("accessKey") + "";
        String Bucket = datasourceMap.get("Bucket") + "";
        EngineSinkS3FileModel sinkS3FileModel = new EngineSinkS3FileModel();
        sinkS3FileModel.setSourceTableName(engineSourceDTO.getSourceTableName());
        sinkS3FileModel.setCleanTargetFolder(s3FileSink.isCleanTargetFolder());
        sinkS3FileModel.setPath(s3FileSink.getPath());
        sinkS3FileModel.setTmpPath(s3FileSink.getTmpPath());
        //数据加载方式默认S3
        sinkS3FileModel.setLoadType(StringUtils.defaultString(s3FileSink.getLoadType(), "S3"));
        if ("S3".equalsIgnoreCase(sinkS3FileModel.getLoadType())) {
            sinkS3FileModel.setBucket("s3a://" + Bucket);
        } else if ("ICFSDOS".equalsIgnoreCase(sinkS3FileModel.getLoadType())) {
            sinkS3FileModel.setBucket("icfsdos://" + Bucket);
        }
        sinkS3FileModel.setAccessKey(accessKey);
        sinkS3FileModel.setAccessSecret(secretKey);
        sinkS3FileModel.setEndpoint(endPoint);
        sinkS3FileModel.setFsS3aAwsCredentialsProvider(s3FileSink.getFsS3aAwsCredentialsProvider());
        sinkS3FileModel.setCustomFilename(s3FileSink.getCustomFilename());
        if (StringUtils.isNotBlank(s3FileSink.getFileNameExpression())) {
            sinkS3FileModel.setFileNameExpression(s3FileSink.getFileNameExpression() + "_${transactionId}");
        }
        sinkS3FileModel.setFilenameTimeFormat(s3FileSink.getFilenameTimeFormat());
        sinkS3FileModel.setFileFormatType(s3FileSink.getFileFormatType());
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != s3FileSink.getFieldDelimiter() && !s3FileSink.getFieldDelimiter().equals("\\001")) {
            sinkS3FileModel.setFieldDelimiter(s3FileSink.getFieldDelimiter());
        }
        sinkS3FileModel.setRowDelimiter(s3FileSink.getRowDelimiter());
        sinkS3FileModel.setHavePartition(s3FileSink.getHavePartition());
        if (null != s3FileSink.getPartitionBy() && s3FileSink.getPartitionBy().size() > 0) {
            sinkS3FileModel.setPartitionBy(JSONObject.toJSONString(s3FileSink.getPartitionBy()));
        }
        sinkS3FileModel.setPartitionDirExpression(s3FileSink.getPartitionDirExpression());
        sinkS3FileModel.setIsPartitionFieldWriteInFile(s3FileSink.getIsPartitionFieldWriteInFile());
        if (null != s3FileSink.getSinkColumns() && s3FileSink.getSinkColumns().size() > 0) {
            sinkS3FileModel.setSinkColumns(JSONObject.toJSONString(s3FileSink.getSinkColumns()));
        }
        sinkS3FileModel.setIsEnableTransaction(s3FileSink.getIsEnableTransaction());
        sinkS3FileModel.setBatchSize(s3FileSink.getBatchSize());
        sinkS3FileModel.setMaxRowsInMemory(s3FileSink.getMaxRowsInMemory());
        sinkS3FileModel.setCompressCodec(s3FileSink.getCompressCodec());
        sinkS3FileModel.setSheetName(s3FileSink.getSheetName());
        sinkS3FileModel.setSchemaSaveMode(s3FileSink.getSchemaSaveMode());
        sinkS3FileModel.setDataSaveMode(s3FileSink.getDataSaveMode());
        //校验内容
        String validateFile = s3FileSink.getValidateFile();
        if (StringUtils.isNoneBlank(validateFile)) {
            sinkS3FileModel.setValidateFile(validateFile);
        }
        String validateContent = s3FileSink.getValidateContent();
        if (StringUtils.isNoneBlank(validateContent)) {
            sinkS3FileModel.setValidateContent(validateContent);
        }
        sinkS3FileModel.setEmptyDataStrategy(s3FileSink.getEmptyDataStrategy());
        sinkS3FileModel.setValidates(Boolean.toString(s3FileSink.isValidates()));
        sinkS3FileModel.setDateFormat(s3FileSink.getDateFormat());
        sinkS3FileModel.setDatetimeFormat(s3FileSink.getDatetimeFormat());
        sinkS3FileModel.setTimeFormat(s3FileSink.getTimeFormat());
        sinkS3FileModel.setEncoding(s3FileSink.getEncoding());
        sinkS3FileModel.setTdsqlPartitionName(engineSourceDTO.getTdsqlPartitionName());
        return sinkS3FileModel;
    }

    private static EngineSinkOssAliFileModel initOssAliSink(EngineSinkDTO engineSinkDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkOssAliFileDTO ossAliFileSink = engineSinkDTO.getOssAliFileSink();
        String datasourceInfoId = ossAliFileSink.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkOssAliFileModel sinkOssAliFileModel = new EngineSinkOssAliFileModel();
        sinkOssAliFileModel.setSourceTableName(engineSinkDTO.getSourceTableName());
        sinkOssAliFileModel.setCleanTargetFolder(ossAliFileSink.isCleanTargetFolder());
        sinkOssAliFileModel.setPath(ossAliFileSink.getPath());
        sinkOssAliFileModel.setTmpPath(ossAliFileSink.getTmpPath());
        sinkOssAliFileModel.setBucket("oss://" + databaseConnectionDTO.getBucket());
        sinkOssAliFileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        sinkOssAliFileModel.setAccessSecret(databaseConnectionDTO.getAccessSecret());
        sinkOssAliFileModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        sinkOssAliFileModel.setCustomFilename(ossAliFileSink.getCustomFilename());
        if (StringUtils.isNotBlank(ossAliFileSink.getFileNameExpression())) {
            sinkOssAliFileModel.setFileNameExpression(ossAliFileSink.getFileNameExpression() + "_${transactionId}");
        }
        sinkOssAliFileModel.setFilenameTimeFormat(ossAliFileSink.getFilenameTimeFormat());
        sinkOssAliFileModel.setFileFormatType(ossAliFileSink.getFileFormatType());
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != ossAliFileSink.getFieldDelimiter() && !ossAliFileSink.getFieldDelimiter().equals("\\001")) {
            sinkOssAliFileModel.setFieldDelimiter(ossAliFileSink.getFieldDelimiter());
        }
        sinkOssAliFileModel.setRowDelimiter(ossAliFileSink.getRowDelimiter());
        sinkOssAliFileModel.setHavePartition(ossAliFileSink.getHavePartition());
        if (null != ossAliFileSink.getPartitionBy() && ossAliFileSink.getPartitionBy().size() > 0) {
            sinkOssAliFileModel.setPartitionBy(JSONObject.toJSONString(ossAliFileSink.getPartitionBy()));
        }
        sinkOssAliFileModel.setPartitionDirExpression(ossAliFileSink.getPartitionDirExpression());
        sinkOssAliFileModel.setIsPartitionFieldWriteInFile(ossAliFileSink.getIsPartitionFieldWriteInFile());
        if (null != ossAliFileSink.getSinkColumns() && ossAliFileSink.getSinkColumns().size() > 0) {
            sinkOssAliFileModel.setSinkColumns(JSONObject.toJSONString(ossAliFileSink.getSinkColumns()));
        }
        sinkOssAliFileModel.setIsEnableTransaction(ossAliFileSink.getIsEnableTransaction());
        sinkOssAliFileModel.setBatchSize(ossAliFileSink.getBatchSize());
        sinkOssAliFileModel.setMaxRowsInMemory(ossAliFileSink.getMaxRowsInMemory());
        sinkOssAliFileModel.setCompressCodec(ossAliFileSink.getCompressCodec());
        sinkOssAliFileModel.setSheetName(ossAliFileSink.getSheetName());
        sinkOssAliFileModel.setTdsqlPartitionName(engineSinkDTO.getTdsqlPartitionName());

        //校验内容
        String validateFile = ossAliFileSink.getValidateFile();
        if (StringUtils.isNoneBlank(validateFile)) {
            sinkOssAliFileModel.setValidateFile(validateFile);
        }
        String validateContent = ossAliFileSink.getValidateContent();
        if (StringUtils.isNoneBlank(validateContent)) {
            sinkOssAliFileModel.setValidateContent(validateContent);
        }
        sinkOssAliFileModel.setEmptyDataStrategy(ossAliFileSink.getEmptyDataStrategy());
        sinkOssAliFileModel.setValidates(Boolean.toString(ossAliFileSink.isValidates()));
        sinkOssAliFileModel.setDateFormat(ossAliFileSink.getDateFormat());
        sinkOssAliFileModel.setDatetimeFormat(ossAliFileSink.getDatetimeFormat());
        sinkOssAliFileModel.setTimeFormat(ossAliFileSink.getTimeFormat());
        sinkOssAliFileModel.setEncoding(ossAliFileSink.getEncoding());
        return sinkOssAliFileModel;
    }

    private static void initFtpSink(EngineModel engineModel, EngineSinkDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkFTPDTO ftpFileSink = engineSourceDTO.getFtpFileSink();
        String datasourceInfoId = ftpFileSink.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkFTPModel sinkFTPModel = new EngineSinkFTPModel();
        sinkFTPModel.setSourceTableName(engineSourceDTO.getSourceTableName());
        sinkFTPModel.setHost(databaseConnectionDTO.getHost());
        sinkFTPModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        sinkFTPModel.setPort(databaseConnectionDTO.getPort());
        sinkFTPModel.setUser(databaseConnectionDTO.getUserName());
        sinkFTPModel.setPath(ftpFileSink.getPath());
        sinkFTPModel.setTmpPath(ftpFileSink.getTmpPath());
        sinkFTPModel.setConnectionMode(ftpFileSink.getConnectionMode());
        sinkFTPModel.setCustomFilename(ftpFileSink.getCustomFilename());
        if (StringUtils.isNotBlank(ftpFileSink.getFileNameExpression())) {
            sinkFTPModel.setFileNameExpression(ftpFileSink.getFileNameExpression() + "_${transactionId}");
        }
        sinkFTPModel.setFilenameTimeFormat(ftpFileSink.getFilenameTimeFormat());
        sinkFTPModel.setFileFormatType(ftpFileSink.getFileFormatType());
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != ftpFileSink.getFieldDelimiter() && !ftpFileSink.getFieldDelimiter().equals("\\001")) {
            sinkFTPModel.setFieldDelimiter(ftpFileSink.getFieldDelimiter());
        }
        sinkFTPModel.setRowDelimiter(ftpFileSink.getRowDelimiter());
        sinkFTPModel.setHavePartition(ftpFileSink.getHavePartition());
        if (null != ftpFileSink.getPartitionBy() && ftpFileSink.getPartitionBy().size() > 0) {
            sinkFTPModel.setPartitionBy(JSONObject.toJSONString(ftpFileSink.getPartitionBy()));
        }
        sinkFTPModel.setPartitionDirExpression(ftpFileSink.getPartitionDirExpression());
        sinkFTPModel.setIsPartitionFieldWriteInFile(ftpFileSink.getIsPartitionFieldWriteInFile());
        if (null != ftpFileSink.getSinkColumns() && ftpFileSink.getSinkColumns().size() > 0) {
            sinkFTPModel.setSinkColumns(JSONObject.toJSONString(ftpFileSink.getSinkColumns()));
        }
        sinkFTPModel.setEnableTransaction(String.valueOf(ftpFileSink.isEnableTransaction()));
        sinkFTPModel.setBatchSize(ftpFileSink.getBatchSize());
        sinkFTPModel.setMaxRowsInMemory(ftpFileSink.getMaxRowsInMemory());
        sinkFTPModel.setCompressCodec(ftpFileSink.getCompressCodec());
        sinkFTPModel.setSheetName(ftpFileSink.getSheetName());
        sinkFTPModel.setEmptyDataStrategy(ftpFileSink.getEmptyDataStrategy());

        sinkFTPModel.setDateFormat(ftpFileSink.getDateFormat());
        sinkFTPModel.setDatetimeFormat(ftpFileSink.getDatetimeFormat());
        sinkFTPModel.setTimeFormat(ftpFileSink.getTimeFormat());
        sinkFTPModel.setEncoding(ftpFileSink.getEncoding());
        //校验内容
        String validateFile = ftpFileSink.getValidateFile();
        if (StringUtils.isNoneBlank(validateFile)) {
            sinkFTPModel.setValidateFile(validateFile);
        }
        String validateContent = ftpFileSink.getValidateContent();
        if (StringUtils.isNoneBlank(validateContent)) {
            sinkFTPModel.setValidateContent(validateContent);
        }

        if (StringUtils.isNotBlank(ftpFileSink.getFileName())) {
            sinkFTPModel.setFinalName(joinFileName(ftpFileSink.getPath(), ftpFileSink.getFileName()));
            engineModel.getMergeFtpSinks().add(sinkFTPModel);
        } else {
            engineModel.getFtpSinks().add(sinkFTPModel);
        }

    }

    private static void initSftpSink(EngineModel engineModel, EngineSinkDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkSFTPDTO sftpFileSink = engineSourceDTO.getSftpFileSink();
        String datasourceInfoId = sftpFileSink.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkSFTPModel sinkSFTPModel = new EngineSinkSFTPModel();
        sinkSFTPModel.setSourceTableName(engineSourceDTO.getSourceTableName());
        sinkSFTPModel.setCleanTargetFolder(sftpFileSink.isCleanTargetFolder());
        sinkSFTPModel.setHost(databaseConnectionDTO.getHost());
        sinkSFTPModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        sinkSFTPModel.setPort(databaseConnectionDTO.getPort());
        sinkSFTPModel.setUser(databaseConnectionDTO.getUserName());
        sinkSFTPModel.setPath(sftpFileSink.getPath());
        sinkSFTPModel.setTmpPath(sftpFileSink.getTmpPath());
        sinkSFTPModel.setCustomFilename(sftpFileSink.getCustomFilename());
        if (StringUtils.isNotBlank(sftpFileSink.getFileNameExpression())) {
            sinkSFTPModel.setFileNameExpression(sftpFileSink.getFileNameExpression() + "_${transactionId}");
        }
        sinkSFTPModel.setFilenameTimeFormat(sftpFileSink.getFilenameTimeFormat());
        sinkSFTPModel.setFileFormatType(sftpFileSink.getFileFormatType());
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != sftpFileSink.getFieldDelimiter() && !sftpFileSink.getFieldDelimiter().equals("\\001")) {
            sinkSFTPModel.setFieldDelimiter(sftpFileSink.getFieldDelimiter());
        }
        sinkSFTPModel.setRowDelimiter(sftpFileSink.getRowDelimiter());
        sinkSFTPModel.setHavePartition(sftpFileSink.getHavePartition());
        if (null != sftpFileSink.getPartitionBy() && sftpFileSink.getPartitionBy().size() > 0) {
            sinkSFTPModel.setPartitionBy(JSONObject.toJSONString(sftpFileSink.getPartitionBy()));
        }
        sinkSFTPModel.setPartitionDirExpression(sftpFileSink.getPartitionDirExpression());
        sinkSFTPModel.setIsPartitionFieldWriteInFile(sftpFileSink.getIsPartitionFieldWriteInFile());
        if (null != sftpFileSink.getSinkColumns() && sftpFileSink.getSinkColumns().size() > 0) {
            sinkSFTPModel.setSinkColumns(JSONObject.toJSONString(sftpFileSink.getSinkColumns()));
        }
        sinkSFTPModel.setEnableTransaction(String.valueOf(sftpFileSink.isEnableTransaction()));
        sinkSFTPModel.setBatchSize(sftpFileSink.getBatchSize());
        sinkSFTPModel.setMaxRowsInMemory(sftpFileSink.getMaxRowsInMemory());
        sinkSFTPModel.setCompressCodec(sftpFileSink.getCompressCodec());
        sinkSFTPModel.setSheetName(sftpFileSink.getSheetName());
        sinkSFTPModel.setTdsqlPartitionName(engineSourceDTO.getTdsqlPartitionName());

        //校验内容
        String validateFile = sftpFileSink.getValidateFile();
        if (StringUtils.isNoneBlank(validateFile)) {
            sinkSFTPModel.setValidateFile(validateFile);
        }
        String validateContent = sftpFileSink.getValidateContent();
        if (StringUtils.isNoneBlank(validateContent)) {
            sinkSFTPModel.setValidateContent(validateContent);
        }
        sinkSFTPModel.setEmptyDataStrategy(sftpFileSink.getEmptyDataStrategy());
        sinkSFTPModel.setValidates(Boolean.toString(sftpFileSink.isValidates()));
        sinkSFTPModel.setDateFormat(sftpFileSink.getDateFormat());
        sinkSFTPModel.setDatetimeFormat(sftpFileSink.getDatetimeFormat());
        sinkSFTPModel.setTimeFormat(sftpFileSink.getTimeFormat());
        sinkSFTPModel.setEncoding(sftpFileSink.getEncoding());
        if (StringUtils.isNotBlank(sftpFileSink.getFileName())) {
            sinkSFTPModel.setFinalName(joinFileName(sftpFileSink.getPath(), sftpFileSink.getFileName()));
            engineModel.getMergeSftpSinks().add(sinkSFTPModel);
        } else {
            engineModel.getSftpSinks().add(sinkSFTPModel);
        }
    }

    private static void initLocalFileSink(EngineModel engineModel, EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkLocalFileDTO localFileSink = sink.getLocalFileSink();
        EngineSinkLocalFileModel engineSinkLocalFileModel = new EngineSinkLocalFileModel();
        String path = localFileSink.getPath();
        String tmpPath = localFileSink.getTmpPath();
        engineSinkLocalFileModel.setPath(path);
        engineSinkLocalFileModel.setCleanTargetFolder(localFileSink.isCleanTargetFolder());
        if (StringUtils.isNotBlank(tmpPath)) {
            engineSinkLocalFileModel.setTmpPath(localFileSink.getTmpPath());
        } else {
            engineSinkLocalFileModel.setTmpPath(path + "/tmp");
        }
        engineSinkLocalFileModel.setCustomFilename(localFileSink.getCustomFilename());
        if (StringUtils.isNotBlank(localFileSink.getFileNameExpression())) {
            engineSinkLocalFileModel.setFileNameExpression(localFileSink.getFileNameExpression() + "_${transactionId}");
        }
        engineSinkLocalFileModel.setFilenameTimeFormat(localFileSink.getFilenameTimeFormat());
        engineSinkLocalFileModel.setFileFormatType(localFileSink.getFileFormatType());
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != localFileSink.getFieldDelimiter() && !localFileSink.getFieldDelimiter().equals("\\001")) {
            engineSinkLocalFileModel.setFieldDelimiter(localFileSink.getFieldDelimiter());
        }
        engineSinkLocalFileModel.setRowDelimiter(localFileSink.getRowDelimiter());
        engineSinkLocalFileModel.setHavePartition(localFileSink.getHavePartition());
        if (null != localFileSink.getPartitionBy() && localFileSink.getPartitionBy().size() > 0) {
            engineSinkLocalFileModel.setPartitionBy(JSONObject.toJSONString(localFileSink.getPartitionBy()));
        }
        engineSinkLocalFileModel.setPartitionDirExpression(localFileSink.getPartitionDirExpression());
        engineSinkLocalFileModel.setIsPartitionFieldWriteInFile(localFileSink.getIsPartitionFieldWriteInFile());
        List<String> sinkColumns = localFileSink.getSinkColumns();
        if (null != sinkColumns && sinkColumns.size() > 0) {
            engineSinkLocalFileModel.setSinkColumns(JSONObject.toJSONString(sinkColumns));
        }
        engineSinkLocalFileModel.setBatchSize(localFileSink.getBatchSize());
        engineSinkLocalFileModel.setCompressCodec(localFileSink.getCompressCodec());
        engineSinkLocalFileModel.setMaxRowsInMemory(localFileSink.getMaxRowsInMemory());
        engineSinkLocalFileModel.setSheetName(localFileSink.getSheetName());
        engineSinkLocalFileModel.setEnableHeaderWrite(localFileSink.getEnableHeaderWrite());
        engineSinkLocalFileModel.setSourceTableName(sink.getSourceTableName());

        //校验内容
        String validateFile = localFileSink.getValidateFile();
        if (StringUtils.isNoneBlank(validateFile)) {
            engineSinkLocalFileModel.setValidateFile(validateFile);
        }
        String validateContent = localFileSink.getValidateContent();
        if (StringUtils.isNoneBlank(validateContent)) {
            engineSinkLocalFileModel.setValidateContent(validateContent);
        }
        engineSinkLocalFileModel.setValidates(Boolean.toString(localFileSink.isValidates()));

        engineSinkLocalFileModel.setEmptyDataStrategy(localFileSink.getEmptyDataStrategy());
        engineSinkLocalFileModel.setDateFormat(localFileSink.getDateFormat());
        engineSinkLocalFileModel.setDatetimeFormat(localFileSink.getDatetimeFormat());
        engineSinkLocalFileModel.setTimeFormat(localFileSink.getTimeFormat());
        engineSinkLocalFileModel.setFixedFieldLengthStrategy(localFileSink.getFixedFieldLengthStrategy());
        engineSinkLocalFileModel.setEncoding(localFileSink.getEncoding());
        engineSinkLocalFileModel.setNullToValue(localFileSink.getNullToValue());
        engineSinkLocalFileModel.setTdsqlPartitionName(sink.getTdsqlPartitionName());

        if (StringUtils.isNotBlank(localFileSink.getFileName())) {
            engineSinkLocalFileModel.setFinalName(joinFileName(localFileSink.getPath(), localFileSink.getFileName()));
            engineSinkLocalFileModel.setOverwriteFile(localFileSink.isOverwriteFile());
            engineModel.getMergeLocalFileSinks().add(engineSinkLocalFileModel);
        } else {
            engineModel.getLocalFileSinks().add(engineSinkLocalFileModel);
        }

    }

    private static String joinFileName(String path, String fileName) {
        String finalName = "";
        //路径是以斜杠结尾，并且文件名称是以斜杠开头，就去掉一个
        if (path.endsWith(File.separator) && fileName.startsWith(File.separator)) {
            finalName = path.substring(0, path.length() - 1) + fileName;
        } else if (path.endsWith(File.separator) && !fileName.startsWith(File.separator)) { //路径是以斜杠结尾，并且文件名称没有斜杠，直接拼
            finalName = path + fileName;
        } else if (!path.endsWith(File.separator) && fileName.startsWith(File.separator)) {//路径不是以斜杠结尾，文件名称是以斜杠开头，直接拼
            finalName = path + fileName;
        } else {//路径不是斜杠结尾，名称也没斜杠
            finalName = path + File.separator + fileName;
        }
        //支持自定义后缀，如果有.就直接返回
        if (fileName.contains(".")) {
            return finalName;
        } else {
            return finalName + ".txt";
        }
    }

    //组装EngineSinkJdbcModel
    private static EngineSinkJDBCModel initJdbcSinks(EngineSinkDTO sink,
                                                     StringRedisTemplate redisTemplate,
                                                     String tenantCode,
                                                     Logger logger,
                                                     Map<String, List<Tuple2<String, List<String>>>> preSqlMap,
                                                     Map<String, List<Tuple2<String, List<String>>>> postSqlMap) {
        String configName = sink.getConfigName();

        EngineSinkJDBCDTO jdbcSink = sink.getJdbcSink();
        String datasourceInfoId = jdbcSink.getDatasourceInfoId();

        //获取数据源信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        //页面有传dbName以页面传的为准
        if (StringUtils.isNotBlank(jdbcSink.getDbName())) {
            datasourceDTO.setDbName(jdbcSink.getDbName());
        }
        //页面有传schemaName以页面传的为准
        if (StringUtils.isNotBlank(jdbcSink.getSchemaName())) {
            datasourceDTO.setSchema(jdbcSink.getSchemaName());
        }
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkJDBCModel engineSinkJDBCModel = new EngineSinkJDBCModel();
        engineSinkJDBCModel.setUrl(databaseConnectionDTO.getUrl());
        //数据源类型
        String dataType = datasourceDTO.getDataType();
        DatabaseTypeEnum databaseTypeEnum = DatabaseTypeEnum.getDatabaseTypeEnum(dataType.toLowerCase());
        engineSinkJDBCModel.setDriver(databaseConnectionDTO.getDriverClassName());
        engineSinkJDBCModel.setUser(databaseConnectionDTO.getUserName());
        engineSinkJDBCModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        engineSinkJDBCModel.setDatabase(datasourceDTO.getDbName());
        //oceanBase需要区分是mysql内核或oracle内核
        if (DatabaseTypeEnum.OCEANBASE_FOR_ORACLE.equals(databaseTypeEnum)) {
            engineSinkJDBCModel.setCompatibleMode("oracle");
        }
        if (DatabaseTypeEnum.ORACLE.equals(databaseTypeEnum)
                || DatabaseTypeEnum.TDSQL_FOR_ORACLE.equals(databaseTypeEnum)
                || DatabaseTypeEnum.OCEANBASE_FOR_ORACLE.equals(databaseTypeEnum)) {
            //获取服务名
//            String jdbcUrl = databaseConnectionDTO.getUrl();
//            String serviceName;
//            if (-1 == jdbcUrl.lastIndexOf("/")) {
//                serviceName = jdbcUrl.substring(jdbcUrl.lastIndexOf(":") + 1);
//            } else {
//                serviceName = jdbcUrl.substring(jdbcUrl.lastIndexOf("/") + 1);
//            }
//            engineSinkJDBCModel.setDatabase(serviceName.toUpperCase());
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
            //如果是oracle，写入目标端字段全转为大写
            //engineSinkJDBCModel.setFieldIde("UPPERCASE");
        } else if (DatabaseTypeEnum.DB2.equals(databaseTypeEnum)) {
            //db2的database取模式名
            engineSinkJDBCModel.setDatabase(databaseConnectionDTO.getSchemaName());
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
            //engineSinkJDBCModel.setFieldIde("UPPERCASE");
        } else if (DatabaseTypeEnum.POSTGRESQL.equals(databaseTypeEnum)
                || DatabaseTypeEnum.GAUSSDB.equals(databaseTypeEnum)
                || DatabaseTypeEnum.KINGBASE8.equals(databaseTypeEnum)
                || DatabaseTypeEnum.DWS_PG.equals(databaseTypeEnum)
                || DatabaseTypeEnum.TBASE.equals(databaseTypeEnum)
                || DatabaseTypeEnum.TDSQL_FOR_PG.equals(databaseTypeEnum)
                || DatabaseTypeEnum.GAUSS_DB200.equals(databaseTypeEnum)
                || DatabaseTypeEnum.GREENPLUM_POSTGRESQL.equals(databaseTypeEnum)
                || DatabaseTypeEnum.AnalyticDB_PostgreSQL.equals(databaseTypeEnum)) {
            //2024-06-27 pg类型数据库如果修改库名，jdbcurl后的库名也要修改
            String jdbcUrl = databaseConnectionDTO.getUrl();
            engineSinkJDBCModel.setUrl(jdbcUrl.substring(0, jdbcUrl.lastIndexOf("/") + 1) + datasourceDTO.getDbName());
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
//            engineSinkJDBCModel.setFieldIde("LOWERCASE");
        } else if (DatabaseTypeEnum.GREENPLUM.equals(databaseTypeEnum)) {//使用gpload方式，则替换驱动和jdbc前缀
            if ("true".equals(jdbcSink.getUseCopyStatement())) {//gpload
                engineSinkJDBCModel.setUrl(JdbcUrlUtils.convertToPostgresUrl(databaseConnectionDTO.getUrl()));
                engineSinkJDBCModel.setDriver(DatabaseTypeEnum.POSTGRESQL.getDriver());
            }
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
//            engineSinkJDBCModel.setTable(jdbcSink.getTableName());
        } else if (DatabaseTypeEnum.SQLSERVER.equals(databaseTypeEnum) || DatabaseTypeEnum.SYBASE.equals(databaseTypeEnum) || (DatabaseTypeEnum.GREENPLUM.equals(databaseTypeEnum) && !"false".equals(jdbcSink.getUseCopyStatement()))) {
            //2024-07-03 sqlserver类型数据库如果修改库名，jdbcurl后的库名也要修改
            //**************************************************************** Set=UTF-8; 不是以/拼接的 需要单独处理
            String jdbcUrl = databaseConnectionDTO.getUrl();
            String regex = "(DatabaseName=)[^;]*";
            String replacement = "$1" + datasourceDTO.getDbName();
            engineSinkJDBCModel.setUrl(jdbcUrl.replaceAll(regex, replacement));
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
            //engineSinkJDBCModel.setFieldIde("LOWERCASE");
        } else if (DatabaseTypeEnum.DMDB.equals(databaseTypeEnum)) {
            //DM类型数据库url截取
            String jdbcUrl = databaseConnectionDTO.getUrl();
            engineSinkJDBCModel.setUrl(jdbcUrl.substring(0, jdbcUrl.lastIndexOf("/")));
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
            //engineSinkJDBCModel.setFieldIde("UPPERCASE");
            engineSinkJDBCModel.setDatabase(databaseConnectionDTO.getSchemaName());
        } else if (DatabaseTypeEnum.MYSQL.equals(databaseTypeEnum)) {
            engineSinkJDBCModel.setTable(jdbcSink.getTableName());
            //2024-05-11 大家保险现场测试环境 有数据源databaseName写大写，导致seatunnel检查找不到表
            engineSinkJDBCModel.setDatabase(datasourceDTO.getDbName());
        } else if (DatabaseTypeEnum.SAP_HANA.equals(databaseTypeEnum)) {
            engineSinkJDBCModel.setTable(jdbcSink.getTableName());
            //engineSinkJDBCModel.setFieldIde("UPPERCASE");
        } else if (DatabaseTypeEnum.DORIS.equals(databaseTypeEnum)) {
            engineSinkJDBCModel.setTable(jdbcSink.getTableName());
            //engineSinkJDBCModel.setFieldIde("LOWERCASE");
        } else if (DatabaseTypeEnum.HIVE.equals(databaseTypeEnum)) {
            //jdbcurl后拼参数stringtype=varchar
            String hiveurl = databaseConnectionDTO.getUrl();
            if (StringUtils.isNotBlank(hiveurl)) {
                String separator = hiveurl.contains("?") ? "&" : "?";
                engineSinkJDBCModel.setUrl(hiveurl + separator + "stringtype=varchar");
            }
            engineSinkJDBCModel.setTable(jdbcSink.getTableName());
            //engineSinkJDBCModel.setFieldIde("LOWERCASE");
            //下载hive数据源的文件并给hiveModel设置文件路径
            Map<String, Object> hvieJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
            try {
                Map<String, String> hiveFilePath = HiveFileUtils.setFile(hvieJson, jdbcSink.getDatasourceInfoId());
                if (StringUtils.isNotBlank(hiveFilePath.get("useKerberos"))) {
                    engineSinkJDBCModel.setUseKerberos("true");
                    engineSinkJDBCModel.setKerberosPrincipal(hiveFilePath.get("kerberosPrincipal"));
                    engineSinkJDBCModel.setKrb5Path(hiveFilePath.get("krb5Path"));
                    engineSinkJDBCModel.setKerberosKeytabPath(hiveFilePath.get("kerberosKeytabPath"));
                }
                if (StringUtils.isNotBlank(hiveFilePath.get("hdfsSitePath"))) {
                    engineSinkJDBCModel.setHdfsSitePath(hiveFilePath.get("hdfsSitePath"));
                }
                if (StringUtils.isNotBlank(hiveFilePath.get("hiveSitePath"))) {
                    engineSinkJDBCModel.setHiveSitePath(hiveFilePath.get("hiveSitePath"));
                }
            } catch (Exception e) {
                log.error("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
            }
        } else if (DatabaseTypeEnum.INFORMIX.equals(databaseTypeEnum)) {
            String jdbcUrl = databaseConnectionDTO.getUrl();
            engineSinkJDBCModel.setUrl(jdbcUrl.substring(0, jdbcUrl.lastIndexOf("/") + 1) + datasourceDTO.getDbName() + jdbcUrl.substring(jdbcUrl.lastIndexOf(":")));
//            engineSinkJDBCModel.setDatabase(databaseConnectionDTO.getSchemaName());
            engineSinkJDBCModel.setTable(databaseConnectionDTO.getSchemaName() + "." + jdbcSink.getTableName());
        } else {
            engineSinkJDBCModel.setTable(jdbcSink.getTableName());
        }
        engineSinkJDBCModel.setSourceTableName(sink.getSourceTableName());
        List<MetadataColumnDTO> fieldList = jdbcSink.getFieldList();
        if ("true".equals(jdbcSink.getMerge()) || "true".equals(jdbcSink.getEnableUpsert()) || null != jdbcSink.getConflictStrategyRow()) {
            List<String> pks = new ArrayList<>();
            if (null != fieldList && fieldList.size() > 0) {
                pks = jdbcSink.getFieldList().stream().filter(t -> "1".equals(t.getColumnPrimaryKey())).map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
            }
            //优先取传过来的，如果没有传，则取元数据的。
            if (CollectionUtils.isNotEmpty(jdbcSink.getPrimaryKeys())) {
                engineSinkJDBCModel.setPrimaryKeys(JSON.toJSONString(jdbcSink.getPrimaryKeys()));
            } else if (CollectionUtils.isNotEmpty(pks)) {
                engineSinkJDBCModel.setPrimaryKeys(JSON.toJSONString(pks));
            }
            if (null != jdbcSink.getConflictStrategyRow()) {
                engineSinkJDBCModel.setConflictStrategyRow(jdbcSink.getConflictStrategyRow().toString());
            }
        }
        engineSinkJDBCModel.setSupportUpsertByQueryPrimaryKeyExist(jdbcSink.getMerge());
        if (null != jdbcSink.getMaxRetries() && jdbcSink.getMaxRetries() > 0) {
            engineSinkJDBCModel.setMaxRetries(String.valueOf(jdbcSink.getMaxRetries()));
        }
        if (null != jdbcSink.getBatchSize() && jdbcSink.getBatchSize() > 0) {
            engineSinkJDBCModel.setBatchSize(String.valueOf(jdbcSink.getBatchSize()));
        }
        engineSinkJDBCModel.setUseCopyStatement(jdbcSink.getUseCopyStatement());
        engineSinkJDBCModel.setPkStrategy(jdbcSink.getPkStrategy());
        engineSinkJDBCModel.setEnableUpsert(jdbcSink.getEnableUpsert());
        if (StringUtils.isNotBlank(jdbcSink.getAutoCommit())) {
            engineSinkJDBCModel.setAutoCommit(jdbcSink.getAutoCommit());
        }
        if (null != jdbcSink.getJoyadataJoinType() && JoyadataJoinType.NONE != jdbcSink.getJoyadataJoinType()) {
            engineSinkJDBCModel.setJoyadataJoinType(jdbcSink.getJoyadataJoinType().toString());
        }
        if (CollectionUtils.isNotEmpty(jdbcSink.getJoyadataJoinOns())) {
            engineSinkJDBCModel.setJoyadataJoinOns(JSON.toJSONString(jdbcSink.getPrimaryKeys()));
        }
        if (StringUtils.isNotBlank(jdbcSink.getInsertErrorStrategy())) {
            engineSinkJDBCModel.setInsertErrorStrategy(jdbcSink.getInsertErrorStrategy());
        }
        //空值处理，和source保持一致
        engineSinkJDBCModel.setEmptyDataStrategy(jdbcSink.getEmptyDataStrategy());
        //打印日志插入语句
        if (null != fieldList && fieldList.size() > 0) {
            List<String> columnNameList = fieldList.stream().map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
            StringBuilder columns = new StringBuilder();
            StringBuilder values = new StringBuilder();

            for (int i = 0; i < columnNameList.size(); i++) {
                columns.append(columnNameList.get(i));
                values.append("?");
                if (i < columnNameList.size() - 1) {
                    columns.append(", ");
                    values.append(", ");
                }
            }
            String tableName = jdbcSink.getTableName();
            if (!tableName.contains(".")) {
                tableName = databaseConnectionDTO.getSchemaName() + "." + tableName;
            }
            String insertSql = "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + values + ")";
            JSONObject insertSqlJson = new JSONObject();
            insertSqlJson.put("configName", configName);
            insertSqlJson.put("sql", insertSql);
            logger.info("[插入sql]:" + insertSqlJson.toJSONString());
            //放入分区字段
            List<String> partitionColumns = fieldList.stream().filter(t -> null != t.getPartitionColumn() && t.getPartitionColumn().equals("1")).map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
            engineSinkJDBCModel.setPartitionKeys(JSON.toJSONString(partitionColumns));
        }
        // 当ErrorStrategy或者pkStrategy为continue时，autoCommit必须是false
        if (StringUtils.equalsIgnoreCase(jdbcSink.getInsertErrorStrategy(),"continue") || StringUtils.equalsIgnoreCase(jdbcSink.getPkStrategy(),"continue")) {
            engineSinkJDBCModel.setAutoCommit("false");
        }
        //记录前后置sql
        EngineUtils.addPrePostSqlMap(datasourceInfoId, jdbcSink.getPreSql(), jdbcSink.getPostSql(), preSqlMap, postSqlMap, configName);
        return engineSinkJDBCModel;
    }

    private static EngineSinkConsoleHoleModel initConsoleHoleSink(EngineSinkDTO sink) {
        EngineSinkConsoleHoleModel hole = new EngineSinkConsoleHoleModel();
        hole.setSourceTableName(sink.getSourceTableName());
        return hole;
    }

    /**
     * datahub sink 配置
     *
     * @param sink
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSinkDataHubModel initDataHubSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkDataHubDTO dataHubSink = sink.getDataHubSink();
        String datasourceInfoId = dataHubSink.getDatasourceInfoId();


        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkDataHubModel engineSinkDataHubModel = new EngineSinkDataHubModel();
        engineSinkDataHubModel.setSourceTableName(sink.getSourceTableName());
        //获取连接信息
        engineSinkDataHubModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        engineSinkDataHubModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        engineSinkDataHubModel.setAccessId(databaseConnectionDTO.getAccessId());
        engineSinkDataHubModel.setProject(databaseConnectionDTO.getProject());
        engineSinkDataHubModel.setTopic(databaseConnectionDTO.getTopic());
        Long timeout = dataHubSink.getTimeout();
        if (null == timeout) {
            engineSinkDataHubModel.setTimeout(3000L);
        } else {
            engineSinkDataHubModel.setTimeout(timeout);
        }
        Long retryTimes = dataHubSink.getRetryTimes();
        if (null == retryTimes) {
            engineSinkDataHubModel.setRetryTimes(3L);
        } else {
            engineSinkDataHubModel.setRetryTimes(retryTimes);
        }
        if (StringUtils.isNotBlank(dataHubSink.getTopic())) {

            engineSinkDataHubModel.setTopic(dataHubSink.getTopic());
        }

        return engineSinkDataHubModel;

    }

    /**
     * ArgoHdfsFile sink 配置
     *
     * @param sink
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSinkArgoHdfsFileModel initArgoHdfsFileSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkArgoHdfsFileDTO argoHdfsFileSink = sink.getArgoHdfsFileSink();
        String datasourceInfoId = argoHdfsFileSink.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSinkArgoHdfsFileModel engineSinkArgoHdfsFileModel = new EngineSinkArgoHdfsFileModel();
        engineSinkArgoHdfsFileModel.setSourceTableName(sink.getSourceTableName());
        //获取连接信息
        String defaultFS = argoHdfsFileSink.getDefaultFS();
        if (StringUtils.isBlank(defaultFS)) {
            engineSinkArgoHdfsFileModel.setDefaultFS(databaseConnectionDTO.getDefaultFS());
        } else {
            engineSinkArgoHdfsFileModel.setDefaultFS(defaultFS);
        }
        engineSinkArgoHdfsFileModel.setPath(argoHdfsFileSink.getPath());
        engineSinkArgoHdfsFileModel.setFileFormatType(argoHdfsFileSink.getFileFormatType());
        engineSinkArgoHdfsFileModel.setFieldDelimiter(argoHdfsFileSink.getFieldDelimiter());
        engineSinkArgoHdfsFileModel.setRowDelimiter(argoHdfsFileSink.getRowDelimiter());
        engineSinkArgoHdfsFileModel.setArgoUrl(databaseConnectionDTO.getUrl());
        engineSinkArgoHdfsFileModel.setArgoUser(databaseConnectionDTO.getUserName());
        engineSinkArgoHdfsFileModel.setArgoPassword(databaseConnectionDTO.getPassword());
        if (StringUtils.isNotBlank(argoHdfsFileSink.getArgoSchema())) {
            engineSinkArgoHdfsFileModel.setArgoSchema(argoHdfsFileSink.getArgoSchema());
        } else {
            engineSinkArgoHdfsFileModel.setArgoSchema(databaseConnectionDTO.getSchemaName());
        }
        engineSinkArgoHdfsFileModel.setArgoTable(argoHdfsFileSink.getArgoTable());
        engineSinkArgoHdfsFileModel.setArgoTmpTableName(argoHdfsFileSink.getArgoTmpTableName());
        engineSinkArgoHdfsFileModel.setSourceTableName(sink.getSourceTableName());
        if (StringUtils.isNotBlank(argoHdfsFileSink.getFileNameExpression())) {
            engineSinkArgoHdfsFileModel.setFileNameExpression(argoHdfsFileSink.getFileNameExpression() + "_${transactionId}");
        }
        engineSinkArgoHdfsFileModel.setKrb5Path(argoHdfsFileSink.getKrb5Path());
        engineSinkArgoHdfsFileModel.setKerberosKeytabPath(argoHdfsFileSink.getKerberosKeytabPath());
        engineSinkArgoHdfsFileModel.setKerberosPrincipal(argoHdfsFileSink.getKerberosPrincipal());
        engineSinkArgoHdfsFileModel.setHdfsSitePath(argoHdfsFileSink.getHdfsSitePath());
        engineSinkArgoHdfsFileModel.setEmptyDataStrategy(argoHdfsFileSink.getEmptyDataStrategy());
        engineSinkArgoHdfsFileModel.setArgoTmpSchema(argoHdfsFileSink.getArgoTmpSchema());
        return engineSinkArgoHdfsFileModel;

    }

    private static EngineSinkAdbGpdistModel initAdbGpdistSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkAdbGpdistDTO adbGpdistSink = sink.getAdbGpdistSink();
        String datasourceInfoId = adbGpdistSink.getDatasourceInfoId();


        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkAdbGpdistModel model = new EngineSinkAdbGpdistModel();
        model.setPath(adbGpdistSink.getPath());
        model.setAdbUrl(databaseConnectionDTO.getUrl());
        model.setAdbDriver(DatabaseTypeEnum.ADB_GPDIST.getDriver());
        model.setAdbUser(databaseConnectionDTO.getUserName());
        model.setAdbPassword(databaseConnectionDTO.getPassword());
        model.setAdbDatabase(adbGpdistSink.getAdbDatabase());
        model.setSchemaName(adbGpdistSink.getSchemaName());
        model.setAdbTable(adbGpdistSink.getAdbTable());
        model.setAdbGpfdistDddress(adbGpdistSink.getAdbGpfdistDddress());
        model.setAdbTmpFilePath(adbGpdistSink.getAdbTmpFilePath());
        model.setSourceTableName(sink.getSourceTableName());
        model.setFileFormatType(adbGpdistSink.getFileFormatType());
        model.setFieldDelimiter(adbGpdistSink.getFieldDelimiter());
        model.setAdbGpfdistPath(adbGpdistSink.getAdbGpfdistPath());
        model.setAdbExternalTableName(adbGpdistSink.getAdbExternalTableName());
        model.setAdbExternalTableSchema(adbGpdistSink.getAdbExternalTableSchema());
        return model;
    }

    //组装EngineSinkJdbcModel
    private static EngineSinkDorisModel initDorisSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkDorisDTO dorisSinkDTO = sink.getDorisSink();
        String datasourceInfoId = dorisSinkDTO.getDatasourceInfoId();

        //获取数据源信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        //页面有传dbName以页面传的为准
        if (StringUtils.isNotBlank(dorisSinkDTO.getDbName())) {
            datasourceDTO.setDbName(dorisSinkDTO.getDbName());
        }
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSinkDorisModel dorisSinkModel = new EngineSinkDorisModel();
        String ip = JdbcUrlUtils.getIp(databaseConnectionDTO.getUrl());
        int port = JdbcUrlUtils.getPort(databaseConnectionDTO.getUrl());
        if (StringUtils.isNotBlank(databaseConnectionDTO.getFeNodes())) {
            dorisSinkModel.setFenodes(databaseConnectionDTO.getFeNodes());
        } else {
            //数据源中没有feNodes时，ip给jdbc里的ip，端口给默认端口
            dorisSinkModel.setFenodes(ip + ":8030");
        }
        dorisSinkModel.setQueryPort(port);
        dorisSinkModel.setUsername(databaseConnectionDTO.getUserName());
        dorisSinkModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        dorisSinkModel.setDatabase(databaseConnectionDTO.getSchemaName());
        dorisSinkModel.setTable(dorisSinkDTO.getTable());
        dorisSinkModel.setTableIdentifier(databaseConnectionDTO.getSchemaName() + "." + dorisSinkDTO.getTable());
        if (StringUtils.isNotEmpty(dorisSinkDTO.getSinkLabelPrefix())) {
            dorisSinkModel.setSinkLabelPrefix(dorisSinkDTO.getSinkLabelPrefix());
        }
        dorisSinkModel.setSinkEnable2pc(dorisSinkDTO.getSinkEnable2pc());
        dorisSinkModel.setSinkEnableDelete(dorisSinkDTO.getSinkEnableDelete());
        if (0 != dorisSinkDTO.getSinkCheckInterval()) {
            dorisSinkModel.setSinkCheckInterval(dorisSinkDTO.getSinkCheckInterval());
        }
        if (0 != dorisSinkDTO.getSinkMaxRetries()) {
            dorisSinkModel.setSinkMaxRetries(dorisSinkDTO.getSinkMaxRetries());
        }
        if (0 != dorisSinkDTO.getSinkBufferSize()) {
            dorisSinkModel.setSinkBufferSize(dorisSinkDTO.getSinkBufferSize());
        }
        if (0 != dorisSinkDTO.getSinkBufferCount()) {
            dorisSinkModel.setSinkBufferCount(dorisSinkDTO.getSinkBufferCount());
        }
        if (0 != dorisSinkDTO.getDorisBatchSize()) {
            dorisSinkModel.setDorisBatchSize(dorisSinkDTO.getDorisBatchSize());
        }
        if (StringUtils.isNotEmpty(dorisSinkDTO.getNeedsUnsupportedTypeCasting())) {
            dorisSinkModel.setNeedsUnsupportedTypeCasting(dorisSinkDTO.getNeedsUnsupportedTypeCasting());
        }
        if (StringUtils.isNotBlank(dorisSinkDTO.getDataSaveMode())) {
            dorisSinkModel.setDataSaveMode(dorisSinkDTO.getDataSaveMode());
            dorisSinkModel.setCustomSql(dorisSinkDTO.getCustomSql());
        }
        //dorisConfig参数，目前后台写死，不接收集成传过来的
        Map<String, String> dorisConfig = new HashMap<>();
        dorisConfig.put("format", "json");
        dorisConfig.put("read_json_by_line", "true");
        dorisSinkModel.setDorisConfig(dorisConfig);
        //数据源类型
        dorisSinkModel.setSourceTableName(sink.getSourceTableName());

        return dorisSinkModel;
    }

    private static EngineSinkClickhouseModel initClickHouseSink(EngineSinkDTO sink,
                                                                StringRedisTemplate redisTemplate,
                                                                String tenantCode,
                                                                Map<String, List<Tuple2<String, List<String>>>> preSqlMap,
                                                                Map<String, List<Tuple2<String, List<String>>>> postSqlMap) {
        String configName = sink.getConfigName();

        EngineSinkClickHouseDTO clickHouseSink = sink.getClickHouseSink();
        String datasourceInfoId = clickHouseSink.getDatasourceInfoId();

        //获取数据源信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        //页面有传dbName以页面传的为准
        if (StringUtils.isNotBlank(clickHouseSink.getDbName())) {
            datasourceDTO.setDbName(clickHouseSink.getDbName());
        }
        //页面有传schemaName以页面传的为准
        if (StringUtils.isNotBlank(clickHouseSink.getSchemaName())) {
            datasourceDTO.setSchema(clickHouseSink.getSchemaName());
        }
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        if (StringUtils.isEmpty(databaseConnectionDTO.getUserName()) || StringUtils.isEmpty(databaseConnectionDTO.getPassword())) {
            throw new RuntimeException("目标端账号或者密码不能为空.");
        }
        EngineSinkClickhouseModel clickhouseModel = new EngineSinkClickhouseModel();
        String host = JdbcUrlUtils.getIp(databaseConnectionDTO.getUrl()) + ":" + JdbcUrlUtils.getPort(databaseConnectionDTO.getUrl());
        LinkedHashMap<String, String> optionsConfig = new LinkedHashMap();
        optionsConfig.put("database", datasourceDTO.getDbName());
        if (databaseConnectionDTO.getUrl().contains("?")) {
            String url = databaseConnectionDTO.getUrl();
            String params = url.substring(url.lastIndexOf("?") + 1);
            if (StringUtils.isNotBlank(params)) {
                String[] split = params.split("&");
                for (String param : split) {
                    String[] split1 = param.split("=");
                    optionsConfig.put(split1[0], split1[1]);
                }
            }
        }
        clickhouseModel.setOptionsConfig(optionsConfig);
        clickhouseModel.setHost(host);
        clickhouseModel.setDatabase(datasourceDTO.getDbName());
        clickhouseModel.setTable(clickHouseSink.getTable());
        clickhouseModel.setUsername(databaseConnectionDTO.getUserName());
        clickhouseModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        List<String> pks = clickHouseSink.getFieldList().stream().filter(t -> "1".equals(t.getColumnPrimaryKey())).map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
        //优先取传过来的，如果没有传，则取元数据的。
        if (StringUtils.isNotBlank(clickHouseSink.getPrimaryKey())) {
            clickhouseModel.setPrimaryKey(clickHouseSink.getPrimaryKey());
        } else if (CollectionUtils.isNotEmpty(pks)) {
            clickhouseModel.setPrimaryKey(StringUtils.join(",", pks));
        }
        clickhouseModel.setPrimaryKey(clickHouseSink.getPrimaryKey());
        clickhouseModel.setSupportUpsert(clickHouseSink.getMerge());
        clickhouseModel.setSourceTableName(sink.getSourceTableName());

        //记录前后置sql
        EngineUtils.addPrePostSqlMap(datasourceInfoId, clickHouseSink.getPreSql(), clickHouseSink.getPostSql(), preSqlMap, postSqlMap, configName);

        return clickhouseModel;
    }

    private static EngineSinkKafkaModel initKafkaSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkKafkaDTO kafkaSink = sink.getKafkaSink();
        String datasourceInfoId = kafkaSink.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        Map<String, Object> kafkaJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        EngineSinkKafkaModel kafkaModel = new EngineSinkKafkaModel();
        kafkaModel.setTopic(kafkaSink.getTopic());
        String brokerList = StringUtils.defaultString(kafkaJson.get("brokerList") + "", "");
        if (StringUtils.isEmpty(brokerList)) {
            throw new RuntimeException("目标端为kafka时候，获取到brokerList地址为空，请检查参数!");
        }
        Map<String, String> kafkaConfig = kafkaSink.getKafkaConfig();
        if (null != kafkaJson.get("kafkaReact") && StringUtils.isNotBlank(kafkaJson.get("kafkaReact") + "")) {
            String kafkaReact = kafkaJson.get("kafkaReact") + "";
            if ("SASL_PLAINTEXT".equals(kafkaReact)) {
                if (null == kafkaConfig) {
                    kafkaConfig = new LinkedHashMap<>();
                }
                kafkaConfig.put("security.protocol", "SASL_PLAINTEXT");
                kafkaConfig.put("sasl.mechanism", "SCRAM-SHA-256");
                String jaas = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\\\"%s\\\" password=\\\"%s\\\";";
                kafkaConfig.put("sasl.jaas.config", String.format(jaas, kafkaJson.get("username"), kafkaJson.get("password")));
            }
        }
        kafkaModel.setSourceTableName(sink.getSourceTableName());
        kafkaModel.setBootstrapServers(kafkaJson.get("brokerList") + "");
        kafkaModel.setKafkaRequestTimeoutMs(kafkaSink.getKafkaRequestTimeoutMs());
        kafkaModel.setFormat(kafkaSink.getFormat());
        kafkaModel.setSemantics(kafkaSink.getSemantics());
        kafkaModel.setFieldDelimiter(kafkaSink.getFieldDelimiter());
        kafkaModel.setPartition(kafkaSink.getPartition());
        kafkaModel.setKafkaConfig(kafkaConfig);
        kafkaModel.setSourceTableName(sink.getSourceTableName());
        return kafkaModel;
    }


    private static EngineSinkHiveModel initHiveSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkHiveDTO hiveSink = sink.getHiveSink();
        String datasourceInfoId = hiveSink.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        Map<String, Object> hvieJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);

        EngineSinkHiveModel hiveModel = new EngineSinkHiveModel();
        //下载hive数据源的文件并给hiveModel设置文件路径
        try {
            Map<String, String> hiveFilePath = HiveFileUtils.setFile(hvieJson, hiveSink.getDatasourceInfoId());
            if (StringUtils.isNotBlank(hiveFilePath.get("useKerberos"))) {
                hiveModel.setUseKerberos("true");
                hiveModel.setKerberosPrincipal(hiveFilePath.get("kerberosPrincipal"));
                hiveModel.setKrb5Path(hiveFilePath.get("krb5Path"));
                hiveModel.setKerberosKeytabPath(hiveFilePath.get("kerberosKeytabPath"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("hdfsSitePath"))) {
                hiveModel.setHdfsSitePath(hiveFilePath.get("hdfsSitePath"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("hiveSitePath"))) {
                hiveModel.setHiveSitePath(hiveFilePath.get("hiveSitePath"));
            }
        } catch (Exception e) {
            log.error("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
        }
        if (StringUtils.isEmpty(hiveSink.getMetastoreUri())) {

            String metastoreUri = StringUtils.defaultString(hvieJson.get("metaStoreUris") + "", "");
            if (StringUtils.isBlank(metastoreUri)) {
                throw new RuntimeException("目标端为[" + datasourceDTO.getDataType() + "]时候，获取到[metaStoreUris]属性为空，请检查参数!");
            } else {
                hiveModel.setMetastoreUri(metastoreUri);
            }
        } else {
            hiveModel.setMetastoreUri(hiveSink.getMetastoreUri());
        }
        hiveModel.setTableName(hiveSink.getTableName());
        String hiveSitePath = hiveSink.getHiveSitePath();
        if (StringUtils.isNotBlank(hiveSitePath)) {
            hiveModel.setHiveSitePath(hiveSink.getHiveSitePath());
        }
        String hdfsSitePath = hiveSink.getHdfsSitePath();
        if (StringUtils.isNotBlank(hdfsSitePath)) {
            hiveModel.setHdfsSitePath(hdfsSitePath);
        }
        if (StringUtils.isNotBlank(hiveSink.getKrb5Path())) {
            hiveModel.setKrb5Path(hiveSink.getKrb5Path());
        }
        if (StringUtils.isNotBlank(hiveSink.getKerberosKeytabPath())) {
            hiveModel.setKerberosKeytabPath(hiveSink.getKerberosKeytabPath());
        }
        if (StringUtils.isNotBlank(hiveSink.getKerberosPrincipal())) {
            hiveModel.setKerberosPrincipal(hiveSink.getKerberosPrincipal());
        }
        hiveModel.setSourceTableName(sink.getSourceTableName());
        hiveModel.setParallelism(hiveSink.getParallelism());
        return hiveModel;
    }


    private static EngineSinkHdfsFileModel initHdfsFileSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkHdfsFileDTO hdfsFileSink = sink.getHdfsFileSink();
        String datasourceInfoId = hdfsFileSink.getDatasourceInfoId();
        EngineSinkHdfsFileModel hdfsFileModel = new EngineSinkHdfsFileModel();
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        Map<String, Object> hdfsJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        //下载hive数据源的文件并给hiveModel设置文件路径
        try {
            Map<String, String> hiveFilePath = HiveFileUtils.setFile(hdfsJson, hdfsFileSink.getDatasourceInfoId());
            if (StringUtils.isNotBlank(hiveFilePath.get("kerberosPrincipal"))) {
                hdfsFileModel.setKerberosPrincipal(hiveFilePath.get("kerberosPrincipal"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("krb5Path"))) {
                hdfsFileModel.setKrb5Path(hiveFilePath.get("krb5Path"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("kerberosKeytabPath"))) {
                hdfsFileModel.setKerberosKeytabPath(hiveFilePath.get("kerberosKeytabPath"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("hdfsSitePath"))) {
                hdfsFileModel.setHdfsSitePath(hiveFilePath.get("hdfsSitePath"));
            }
        } catch (Exception e) {
            log.error("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
        }
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        hdfsFileModel.setSourceTableName(sink.getSourceTableName());
        hdfsFileModel.setFsDefaultFS(databaseConnectionDTO.getUrl());
        String path = hdfsFileSink.getPath();
        String tmpPath = hdfsFileSink.getTmpPath();
        hdfsFileModel.setPath(path);
        if (StringUtils.isNotBlank(tmpPath)) {
            hdfsFileModel.setTmpPath(hdfsFileSink.getTmpPath());
        } else {
            hdfsFileModel.setTmpPath(path + "/tmp");
        }
        hdfsFileModel.setFieldDelimiter(hdfsFileSink.getFieldDelimiter());
        hdfsFileModel.setRowDelimiter(hdfsFileSink.getRowDelimiter());
        //以下非必填
        hdfsFileModel.setHavePartition(hdfsFileSink.getHavePartition());
        hdfsFileModel.setIsEnableTransaction(hdfsFileSink.getIsEnableTransaction());
        hdfsFileModel.setCustomFilename(hdfsFileSink.getCustomFilename());
        hdfsFileModel.setFileNameExpression(hdfsFileSink.getFileNameExpression());
        hdfsFileModel.setFilenameTimeFormat(hdfsFileSink.getFilenameTimeFormat());
        hdfsFileModel.setFileFormatType(hdfsFileSink.getFileFormatType());
        hdfsFileModel.setPartitionBy(JSONObject.toJSONString(hdfsFileSink.getPartitionBy()));
        hdfsFileModel.setPartitionDirExpression(hdfsFileSink.getPartitionDirExpression());
        hdfsFileModel.setIsPartitionFieldWriteInFile(hdfsFileSink.getIsPartitionFieldWriteInFile());
        hdfsFileModel.setSinkColumns(JSONObject.toJSONString(hdfsFileSink.getSinkColumns()));

        hdfsFileModel.setBatchSize(hdfsFileSink.getBatchSize());
        hdfsFileModel.setCompressCodec(hdfsFileSink.getCompressCodec());
        hdfsFileModel.setMaxRowsInMemory(hdfsFileSink.getMaxRowsInMemory());
        hdfsFileModel.setSheetName(hdfsFileSink.getSheetName());
        hdfsFileModel.setEmptyDataStrategy(hdfsFileSink.getEmptyDataStrategy());
        hdfsFileModel.setDateFormat(hdfsFileSink.getDateFormat());
        hdfsFileModel.setDatetimeFormat(hdfsFileSink.getDatetimeFormat());
        hdfsFileModel.setTimeFormat(hdfsFileSink.getTimeFormat());

        return hdfsFileModel;
    }

    private static EngineSinkMongoDBModel initMongoDBSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkMongoDBDTO mongoDBSink = sink.getMongodbSink();
        String datasourceInfoId = mongoDBSink.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        //页面有传dbName以页面传的为准
        if (StringUtils.isNotBlank(mongoDBSink.getDbName())) {
            datasourceDTO.setDbName(mongoDBSink.getDbName());
        }
        //页面有传schemaName以页面传的为准
        if (StringUtils.isNotBlank(mongoDBSink.getSchemaName())) {
            datasourceDTO.setSchema(mongoDBSink.getSchemaName());
        }
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkMongoDBModel engineSinkMongoDBModel = new EngineSinkMongoDBModel();
        engineSinkMongoDBModel.setSourceTableName(sink.getSourceTableName());
        //获取连接信息
        engineSinkMongoDBModel.setUri(databaseConnectionDTO.getUrl());
        engineSinkMongoDBModel.setDatabase(databaseConnectionDTO.getSchemaName());
        engineSinkMongoDBModel.setCollection(mongoDBSink.getTableName());
        engineSinkMongoDBModel.setSchema(mongoDBSink.getSchema());
        engineSinkMongoDBModel.setBufferFlushInterval(mongoDBSink.getBufferFlushInterval());
        engineSinkMongoDBModel.setBufferFlushMaxRows(mongoDBSink.getBufferFlushMaxRows());
        engineSinkMongoDBModel.setRetryMax(mongoDBSink.getRetryMax());
        engineSinkMongoDBModel.setRetryInterval(mongoDBSink.getRetryInterval());
        engineSinkMongoDBModel.setUpsertEnable(mongoDBSink.getUpsertEnable());
        engineSinkMongoDBModel.setPrimaryKey(mongoDBSink.getPrimaryKey());
        engineSinkMongoDBModel.setTransaction(mongoDBSink.getTransaction());

        return engineSinkMongoDBModel;
    }

    private static EngineSinkKuduModel initKuduSink(EngineSinkDTO sink, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSinkKuduDTO kuduSink = sink.getKuduSink();
        String datasourceInfoId = kuduSink.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSinkKuduModel engineSinkKuduModel = new EngineSinkKuduModel();
        engineSinkKuduModel.setSourceTableName(sink.getSourceTableName());
        engineSinkKuduModel.setKuduMasters(databaseConnectionDTO.getUrl());
        engineSinkKuduModel.setTableName(kuduSink.getTableName());
        engineSinkKuduModel.setClientWorkerCount(kuduSink.getClientWorkerCount());
        engineSinkKuduModel.setClientDefaultOperationTimeoutMs(kuduSink.getClientDefaultOperationTimeoutMs());
        engineSinkKuduModel.setClientDefaultAdminOperationTimeoutMs(kuduSink.getClientDefaultAdminOperationTimeoutMs());
        engineSinkKuduModel.setEnableKerberos(kuduSink.getEnableKerberos());
        engineSinkKuduModel.setKerberosPrincipal(kuduSink.getKerberosPrincipal());
        engineSinkKuduModel.setKerberosKeytab(kuduSink.getKerberosKeytab());
        engineSinkKuduModel.setKerberosKrb5conf(kuduSink.getKerberosKrb5conf());
        engineSinkKuduModel.setSaveMode(kuduSink.getSaveMode());
        engineSinkKuduModel.setSessionFlushMode(kuduSink.getSessionFlushMode());
        engineSinkKuduModel.setBatchSize(kuduSink.getBatchSize());
        engineSinkKuduModel.setBufferFlushInterval(kuduSink.getBufferFlushInterval());
        engineSinkKuduModel.setIgnoreNotFound(kuduSink.getIgnoreNotFound());
        engineSinkKuduModel.setIgnoreNotDuplicate(kuduSink.getIgnoreNotDuplicate());

        return engineSinkKuduModel;
    }


}
