package org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.joyadata.engine.common.beans.constants.Constants;
import com.joyadata.engine.common.beans.dto.DatabaseConnectionDTO;
import com.joyadata.engine.common.beans.dto.RowFilterDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataTableDTO;
import com.joyadata.engine.common.beans.dto.engine.EngineModel;
import com.joyadata.engine.common.beans.dto.engine.EngineTransformModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineHttpBaseSourceModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineMaxComputeBaseSourceModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceAdbGpdistModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceArgoHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceClickhouseModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceDataHubModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceDorisModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceElasticsearchModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceFTPModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceHbaseModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceInspurOSSFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceJdbcModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceKafkaModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceKuduModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceLocalFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceLocalFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceMongoDBModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceMysqlCDCModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOracleCDCModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOssAliFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOssAliFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOssHuaweiFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourcePostgresCDCModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceS3File2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceS3FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceSFTP2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceSFTPModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceSqlserverCDCModel;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDTO;
import com.joyadata.engine.common.beans.dto.source.EngineHttpBaseSourceDTO;
import com.joyadata.engine.common.beans.dto.source.EngineMaxComputeSourceDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceAdbGpdistDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceArgoHdfsFileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceClickhouseDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceDataHubDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceDorisDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceElasticsearchDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceFTPDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceHbaseDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceHdfsFileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceInspurOSSFile2FileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceJDBCDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceKafkaDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceKuduDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceLocalFile2FileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceLocalFileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceMongoDBDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceMysqlCDCDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceOracleCDCDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceOssAliFile2FileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceOssAliFileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceOssHuaweiFileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourcePostgreCDCDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceS3File2FileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceS3FileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceSFTP2FileDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceSFTPDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceSqlserverCDCDTO;
import com.joyadata.engine.common.beans.enums.DatabaseTypeEnum;
import com.joyadata.engine.common.beans.enums.EngineSinkTypeEnum;
import com.joyadata.engine.common.beans.enums.EngineSourceTypeEnum;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.dolphinscheduler.plugin.task.api.model.Property;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.dto.SavePointDTO;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.handler.DatabaseFactory;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.handler.DatabaseHandler;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.utils.rowfilter.mysql.MysqlUtil;
import org.apache.dolphinscheduler.plugin.task.joyadata.st.engine.utils.rowfilter.util.SqlUtil;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.joyadata.engine.common.beans.constants.Constants.ENGINE_SAVEPOINT;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSourceUtils
 * @date 2024/2/29
 */
@Slf4j
public class EngineSourceUtils {

    public static void initSources(List<EngineSourceDTO> source,
                                   EngineModel engineModel,
                                   StringRedisTemplate redisTemplate,
                                   List<SavePointDTO> needSaveTableId,
                                   String processDefineCode,
                                   String taskCode,
//                                   Map<String, List<String>> resultpkNames,
                                   String tenantCode,
                                   List<EngineTransformModel> transforms,
                                   List<EngineSinkDTO> sink,
                                   String gatewayHttp, Map<String, Property> paramsMap,
                                   Logger logger, Map<String, Integer> sourceCountMap,
                                   Map<String, List<Tuple2<String, List<String>>>> preSqlMap,
                                   Map<String, List<Tuple2<String, List<String>>>> postSqlMap) {
        AtomicInteger pipeline = new AtomicInteger(1);
        source.forEach(t -> {
            Optional<EngineSinkDTO> engineSink = EngineInitUtils.getEngineSink(t.getResultTableName(), transforms, sink);
            //TODO 多读一写时空文件参数暂未处理，目前source1有数据，source2无数据会出现问题
            //处理一读多写时空文件参数问题
            List<EngineSinkDTO> engineSinkList = EngineInitUtils.getEngineSinkList(t.getResultTableName(), transforms, sink);
            if (engineSink.isPresent()) {
                try {
                    //如果是jdbc类型，修正正确的type，EngineSourceDTO
                    if (EngineSourceTypeEnum.JDBC == t.getType()) {
                        fixEngineSourceDTO(t, redisTemplate, tenantCode, logger);
                    }
                    switch (t.getType()) {
                        case JDBC:
                            Tuple2<SavePointDTO, EngineSourceJdbcModel> tupleJdbc = initJdbcSources(t, redisTemplate,
                                    processDefineCode, taskCode, tenantCode, engineSinkList, gatewayHttp, paramsMap, logger, sourceCountMap, pipeline.get(), preSqlMap, postSqlMap);
                            needSaveTableId.add(tupleJdbc.getV1());
                            EngineSourceJdbcModel jdbcModel = tupleJdbc.getV2();
                            jdbcModel.setPipeline(pipeline.get());
                            engineModel.getJdbcSources().add(jdbcModel);
                            break;
                        case DORIS:
                            Tuple2<SavePointDTO, EngineSourceDorisModel> tuple2 = initDorisSources(t, redisTemplate,
                                    processDefineCode, taskCode, tenantCode, gatewayHttp, logger);
                            needSaveTableId.add(tuple2.getV1());
                            engineModel.getDorisSources().add(tuple2.getV2());
                            break;
                        case CLICKHOUSE:
                            Tuple2<SavePointDTO, EngineSourceClickhouseModel> tupleCk = initClickhouseSources(t, redisTemplate,
                                    processDefineCode, taskCode, tenantCode, logger, sourceCountMap, pipeline.get(), preSqlMap, postSqlMap);
                            needSaveTableId.add(tupleCk.getV1());
                            EngineSourceClickhouseModel clickhouseModel = tupleCk.getV2();
                            clickhouseModel.setPipeline(pipeline.get());
                            engineModel.getClickhouseSources().add(clickhouseModel);
                            break;
                        case ORACLECDC:
                            engineModel.getOracleSources().add(initOracleCDCSources(t, redisTemplate, tenantCode));
                            break;
                        case MYSQLCDC:
                            engineModel.getMysqlSources().add(initMysqlCDCSources(t, redisTemplate, tenantCode));
                            break;
                        case SQLSERVERCDC:
                            engineModel.getSqlserverSources().add(initSqlServerCDCSources(t, redisTemplate, tenantCode));
                            break;
                        case POSTGRESCDC:
                            engineModel.getPostgresSources().add(initPostgresCDCSources(t, redisTemplate, tenantCode));
                            break;
                        case KAFKA:
                            engineModel.getKafkaSources().add(initKafkaSources(t, redisTemplate, tenantCode));
                            break;
                        case ELASTICSEARCH:
                            engineModel.getElasticsearchSources().add(initElasticsearchSources(t, redisTemplate, tenantCode));
                            break;
                        case MONGODB:
                            engineModel.getMongodbSources().add(initMongoDBSources(t, redisTemplate, tenantCode, engineSink.get(), logger));
                            break;
                        case KUDU:
                            engineModel.getKuduSources().add(initKuduSources(t, redisTemplate, tenantCode));
                            break;
                        case HDFSFILE:
                            engineModel.getHdfsFileSources().add(initHdfsFileSources(t, redisTemplate, tenantCode));
                            break;
                        case LOCALFILE:
                            engineModel.getLocalFileSources().add(initLocalFileSources(t, redisTemplate, tenantCode));
                            break;
                        case SFTPFILE:
                            engineModel.getSftpSources().add(initSftpSources(t, redisTemplate, tenantCode));
                            break;
                        case FTPFILE:
                            engineModel.getFtpSources().add(initFtpSources(t, redisTemplate, tenantCode));
                            break;
                        case OSS_ALI:
                            engineModel.getOssAliSources().add(initOssAliSources(t, redisTemplate, tenantCode));
                            break;
                        case S3FILE:
                            engineModel.getS3FileSources().add(initS3FileSources(t, redisTemplate, tenantCode));
                            break;
                        case OSS_HUAWEI:
                            engineModel.getOssHuaweiSources().add(initOssHuaweiFileSources(t, redisTemplate, tenantCode));
                            break;
                        case DATA_HUB:
                            engineModel.getDataHubSources().add(initDataHubSources(t, redisTemplate, tenantCode));
                            break;
                        case ADB_GPDIST:
                            engineModel.getAdbGpdistSources().add(initAdbGpdistSources(t, redisTemplate, tenantCode));
                            break;
                        case ARGO_HDFS_FILE:
                            engineModel.getArgoHdfsFileSources().add(initArgoHdfsFileSource(t, redisTemplate, tenantCode));
                            break;
                        case HBASE:
                            engineModel.getHbaseSources().add(initHbaseSources(t, redisTemplate, tenantCode));
                            break;
                        case LOCALFILE2FILE:
                            engineModel.getLocalFile2FileSources().add(initLocalFile2FileSources(t));
                            break;
                        case OSS_ALI2FILE:
                            engineModel.getOssAliFile2FileSources().add(initOssAliFile2FileSources(t, redisTemplate, tenantCode, logger));
                            break;
                        case S3FILE2FILE:
                            engineModel.getS3File2FileSources().add(initS3File2FileSources(t, redisTemplate, tenantCode, logger));
                            break;
                        case SFTPFILE2FILE:
                            engineModel.getSftpFile2FileSources().add(initSftpFile2FileSources(t, redisTemplate, tenantCode, logger));
                            break;
                        case INSPUROSSFILE2FILE:
                            engineModel.getInspurOSSFile2FileSources().add(initInspurOSSFile2FileSources(t, redisTemplate, tenantCode, logger));
                            break;
                        case HTTP:
                            engineModel.getHttpBaseSources().add(initHttpBaseSources(t, redisTemplate, tenantCode));
                            break;
                        case MAXCOMPUTE:
                            engineModel.getMaxComputeSources().add(initMaxComputeSources(t, redisTemplate, tenantCode, logger));
                            break;
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                throw new RuntimeException("没有对应目标端！" + t.getResultTableName() + " type " + t.getType());
            }
            pipeline.getAndIncrement();
        });
    }

    public static EngineMaxComputeBaseSourceModel initMaxComputeSources(EngineSourceDTO t, StringRedisTemplate redisTemplate,
                                                                        String tenantCode, Logger logger) {
        EngineMaxComputeBaseSourceModel maxComputeBaseSourceModel = new EngineMaxComputeBaseSourceModel();
        EngineMaxComputeSourceDTO dto = t.getMaxComputeSource();
        String datasourceInfoId = dto.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        //页面传的有以页面为准
        String projectName = dto.getProjectName();
        if (StringUtils.isNotBlank(projectName)) {
            databaseConnectionDTO.setProject(projectName);
        }
        maxComputeBaseSourceModel.setAccessId(databaseConnectionDTO.getAccessId());
        maxComputeBaseSourceModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        maxComputeBaseSourceModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        maxComputeBaseSourceModel.setTunnelEndpoint(databaseConnectionDTO.getTunnelEndpoint());
        maxComputeBaseSourceModel.setProject(databaseConnectionDTO.getProject());
        maxComputeBaseSourceModel.setTableName(dto.getTableName());
        maxComputeBaseSourceModel.setPartitionSpec(dto.getPartitionValue());
        maxComputeBaseSourceModel.setSchema(dto.getSchema());
        maxComputeBaseSourceModel.setResultTableName(t.getResultTableName());
        return maxComputeBaseSourceModel;

    }

    private static EngineHttpBaseSourceModel initHttpBaseSources(EngineSourceDTO t, StringRedisTemplate redisTemplate,
                                                                 String tenantCode) {
        EngineHttpBaseSourceModel httpBaseSourceModel = new EngineHttpBaseSourceModel();

        EngineHttpBaseSourceDTO httpBaseSource = t.getHttpBaseSource();

        httpBaseSourceModel.setResultTableName(t.getResultTableName());
        httpBaseSourceModel.setUrl(httpBaseSource.getUrl());
        httpBaseSourceModel.setMethod(httpBaseSource.getMethod());
        httpBaseSourceModel.setFormat(httpBaseSource.getFormat());

        List<LinkedHashMap<String, String>> sourceOutputColumns = httpBaseSource.getSourceOutputColumns();
        httpBaseSourceModel.setSourceOutputColumns(sourceOutputColumns);


        /**
         * 存储字段---类型映射
         */
        Map<String, Object> fieldTypeMap = new HashMap<>();

        /**
         * 存储字段--jsonPath的映射
         */
        Map<String, Object> fieldJsonPathMap = new HashMap<>();

        /**
         * 存储字段--默认值的映射
         */
        Map<String, Object> defaultValueMap = new HashMap<>();

        for (LinkedHashMap<String, String> sourceMap : sourceOutputColumns) {
            String fieldName = sourceMap.get("columnName");
            String fieldType = sourceMap.get("columnType");
            String jsonPath = sourceMap.get("jsonPath");
            String defaultValue = sourceMap.get("defaultValue");
            fieldTypeMap.put(fieldName, fieldType);
            fieldJsonPathMap.put(fieldName, jsonPath);
            defaultValueMap.put(fieldName, defaultValue);
        }

        httpBaseSourceModel.setFieldTypeMap(fieldTypeMap);
        httpBaseSourceModel.setFieldJsonPathMap(fieldJsonPathMap);

        Map<String, String> params = httpBaseSource.getParams();
        httpBaseSourceModel.setParams(params);

        Map<String, String> headers = httpBaseSource.getHeaders();
        httpBaseSourceModel.setHeaders(headers);

        Map<String, Object> bodys = httpBaseSource.getBodys();
        if (null != bodys && !bodys.isEmpty()) {
            String jsonBody = JSON.toJSONString(bodys);
            httpBaseSourceModel.setBody(StringEscapeUtils.escapeJson(jsonBody));
        } else {
            httpBaseSourceModel.setBody("{}");
        }

        Map<String, Object> pageing = httpBaseSource.getPageing();
        if (pageing.size() > 0) {
            Object object = pageing.get("total_page_size");
            if (object == null) {
                pageing.put("total_page_size", -1);//如果没有设置总页数，默认设置-1
            } else if (object != null) {
                if (object.toString().trim().equals("")) {
                    pageing.put("total_page_size", -1);//如果没有设置总页数，默认设置-1
                } else {
                    pageing.put("total_page_size", Long.valueOf(object.toString()));
                }
            }
        }

        httpBaseSourceModel.setPageing(pageing);
        httpBaseSourceModel.setConnectTimeoutMs(httpBaseSource.getConnectTimeoutMs());

        httpBaseSourceModel.setDateFormat(httpBaseSource.getDateFormat());
        httpBaseSourceModel.setDatetimeFormat(httpBaseSource.getDatetimeFormat());
        httpBaseSourceModel.setTimeFormat(httpBaseSource.getTimeFormat());
        return httpBaseSourceModel;
    }

    private static EngineSourceInspurOSSFile2FileModel initInspurOSSFile2FileSources(EngineSourceDTO engineSourceDTO,
                                                                                     StringRedisTemplate redisTemplate,
                                                                                     String tenantCode,
                                                                                     Logger logger) {
        EngineSourceInspurOSSFile2FileDTO inspurOSSFile2FileSource = engineSourceDTO.getInspurOSSFile2FileSource();
        String datasourceInfoId = inspurOSSFile2FileSource.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        datasourceDTO.setDatasourceInfoIdInKerberosPath(datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSourceInspurOSSFile2FileModel inspurOSSFile2FileModel = new EngineSourceInspurOSSFile2FileModel();
        inspurOSSFile2FileModel.setPath(inspurOSSFile2FileSource.getPath());
        inspurOSSFile2FileModel.setBucket("s3a://" + databaseConnectionDTO.getBucket());
        inspurOSSFile2FileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        inspurOSSFile2FileModel.setSecretKey(databaseConnectionDTO.getAccessSecret());
        inspurOSSFile2FileModel.setFsS3aEndpoint(databaseConnectionDTO.getEndpoint());
        inspurOSSFile2FileModel.setFsS3aAwsCredentialsProvider(inspurOSSFile2FileSource.getFsS3aAwsCredentialsProvider());
        inspurOSSFile2FileModel.setResultTableName(engineSourceDTO.getResultTableName());

        return inspurOSSFile2FileModel;
    }

    private static EngineSourceSFTP2FileModel initSftpFile2FileSources(EngineSourceDTO engineSourceDTO,
                                                                       StringRedisTemplate redisTemplate,
                                                                       String tenantCode,
                                                                       Logger logger) {
        EngineSourceSFTP2FileDTO sftp2FileSource = engineSourceDTO.getSftp2FileSource();
        String datasourceInfoId = sftp2FileSource.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSourceSFTP2FileModel sftp2FileModel = new EngineSourceSFTP2FileModel();
        sftp2FileModel.setHost(databaseConnectionDTO.getHost());
        sftp2FileModel.setPort(databaseConnectionDTO.getPort());
        sftp2FileModel.setUser(databaseConnectionDTO.getUserName());
        sftp2FileModel.setPassword(databaseConnectionDTO.getPassword());
        sftp2FileModel.setPath(sftp2FileSource.getPath());
        sftp2FileModel.setResultTableName(engineSourceDTO.getResultTableName());

        return sftp2FileModel;
    }

    private static EngineSourceS3File2FileModel initS3File2FileSources(EngineSourceDTO engineSourceDTO,
                                                                       StringRedisTemplate redisTemplate,
                                                                       String tenantCode,
                                                                       Logger logger) {
        EngineSourceS3File2FileDTO s3File2FileSource = engineSourceDTO.getS3File2FileSource();
        String datasourceInfoId = s3File2FileSource.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSourceS3File2FileModel s3File2FileModel = new EngineSourceS3File2FileModel();
        s3File2FileModel.setPath(s3File2FileSource.getPath());
        s3File2FileModel.setBucket("s3a://" + databaseConnectionDTO.getBucket());
        s3File2FileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        s3File2FileModel.setSecretKey(databaseConnectionDTO.getAccessSecret());
        s3File2FileModel.setFsS3aEndpoint(databaseConnectionDTO.getEndpoint());
        s3File2FileModel.setResultTableName(engineSourceDTO.getResultTableName());
        return s3File2FileModel;
    }

    private static EngineSourceOssAliFile2FileModel initOssAliFile2FileSources(EngineSourceDTO engineSourceDTO,
                                                                               StringRedisTemplate redisTemplate,
                                                                               String tenantCode,
                                                                               Logger logger) {
        EngineSourceOssAliFile2FileDTO ossAliFile2FileSource = engineSourceDTO.getOssAliFile2FileSource();
        String datasourceInfoId = ossAliFile2FileSource.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSourceOssAliFile2FileModel ossAliFile2FileModel = new EngineSourceOssAliFile2FileModel();
        ossAliFile2FileModel.setPath(ossAliFile2FileSource.getPath());
        ossAliFile2FileModel.setBucket("oss://" + databaseConnectionDTO.getBucket());
        ossAliFile2FileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        ossAliFile2FileModel.setAccessSecret(databaseConnectionDTO.getAccessSecret());
        ossAliFile2FileModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        ossAliFile2FileModel.setResultTableName(engineSourceDTO.getResultTableName());
        return ossAliFile2FileModel;
    }

    private static EngineSourceLocalFile2FileModel initLocalFile2FileSources(EngineSourceDTO engineSourceDTO) {
        EngineSourceLocalFile2FileDTO localFile2FileSource = engineSourceDTO.getLocalFile2FileSource();
        EngineSourceLocalFile2FileModel localFile2FileModel = new EngineSourceLocalFile2FileModel();
        localFile2FileModel.setPath(localFile2FileSource.getPath());
        localFile2FileModel.setResultTableName(engineSourceDTO.getResultTableName());
        return localFile2FileModel;
    }

    /// /如果是jdbc类型，修正正确的type，EngineSourceDTO
    private static void fixEngineSourceDTO(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode, Logger logger) {
        EngineSourceJDBCDTO jdbcSource = engineSourceDTO.getJdbcSource();
        String datasourceInfoId = jdbcSource.getDatasourceInfoId();
        //获取表信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        //数据源转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        switch (DatabaseTypeEnum.getDatabaseTypeEnum(datasourceDTO.getDataType().toLowerCase())) {
            case CLICKHOUSE:
                EngineSourceClickhouseDTO clickhouseSource = new EngineSourceClickhouseDTO();
                BeanUtils.copyProperties(jdbcSource, clickhouseSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.CLICKHOUSE);
                engineSourceDTO.setClickhouseSource(clickhouseSource);
                engineSourceDTO.setJdbcSource(null);
                break;
            case ELASTICSEARCH:
                EngineSourceElasticsearchDTO elasticsearchSource = new EngineSourceElasticsearchDTO();
                BeanUtils.copyProperties(jdbcSource, elasticsearchSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.ELASTICSEARCH);
                engineSourceDTO.setElasticsearchSource(elasticsearchSource);
                engineSourceDTO.setJdbcSource(null);
                break;
            case MONGODB:
                EngineSourceMongoDBDTO mongodbSource = new EngineSourceMongoDBDTO();
                BeanUtils.copyProperties(jdbcSource, mongodbSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.MONGODB);
                engineSourceDTO.setMongodbSource(mongodbSource);
                engineSourceDTO.setJdbcSource(null);
                break;
            case KUDU:
                EngineSourceKuduDTO kuduSource = new EngineSourceKuduDTO();
                BeanUtils.copyProperties(jdbcSource, kuduSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.KUDU);
                engineSourceDTO.setKuduSource(kuduSource);
                engineSourceDTO.setJdbcSource(null);
                break;
/*            case DORIS:
                EngineSourceDorisDTO dorisSource = new EngineSourceDorisDTO();
                BeanUtils.copyProperties(jdbcSource, dorisSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.DORIS);
                engineSourceDTO.setDorisSource(dorisSource);
                engineSourceDTO.setJdbcSource(null);
                break;*/
            case DataHub:
                EngineSourceDataHubDTO dataHubSource = new EngineSourceDataHubDTO();
                BeanUtils.copyProperties(jdbcSource, dataHubSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.DATA_HUB);
                engineSourceDTO.setDataHubSource(dataHubSource);
                engineSourceDTO.setJdbcSource(null);
                break;
            case HBASE:
                EngineSourceHbaseDTO hbaseSource = new EngineSourceHbaseDTO();
                BeanUtils.copyProperties(jdbcSource, hbaseSource);
                engineSourceDTO.setType(EngineSourceTypeEnum.HBASE);
                engineSourceDTO.setHbaseSource(hbaseSource);
                engineSourceDTO.setJdbcSource(null);
                break;
        }
    }

    private static EngineSourceHbaseModel initHbaseSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceHbaseDTO hbaseSource = engineSourceDTO.getHbaseSource();
        String datasourceInfoId = hbaseSource.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        Map<String, Object> hbaseJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        EngineSourceHbaseModel engineSourceHbaseModel = new EngineSourceHbaseModel();
        engineSourceHbaseModel.setResultTableName(engineSourceDTO.getResultTableName());
        engineSourceHbaseModel.setZookeeperQuorum(hbaseJson.get("hbase_quorum") + "");
        engineSourceHbaseModel.setTableName(hbaseSource.getTableName());
        List<String> queryColumns = hbaseSource.getQueryColumns();
        if (null != queryColumns && queryColumns.size() > 0) {
            engineSourceHbaseModel.setQueryColumns(JSONObject.toJSONString(queryColumns));
        }
        LinkedHashMap<String, String> schema = hbaseSource.getSchema();
        if (null != schema && schema.size() > 0) {
            List<String> columns = new ArrayList<>();
            //要拼为：{name="cf1:id" type="int"}
            for (String key : schema.keySet()) {
                columns.add("{" + "name=\"" + key + "\",type=\"" + schema.get(key) + "\"}");
            }
            engineSourceHbaseModel.setColumns(columns);
        }
        //下载hbase数据源的文件并给Model设置文件路径
        try {
            String filePath = HbaseFileUtils.setFile(hbaseJson, hbaseSource.getDatasourceInfoId());
            if (null != hbaseJson.get("openKerberos") && "open".equals(hbaseJson.get("openKerberos").toString())) {
                engineSourceHbaseModel.setUser(hbaseJson.get("authUser") + "");
                engineSourceHbaseModel.setServerPrincipal(hbaseJson.get("serverPrincipal") + "");
            }
            engineSourceHbaseModel.setFilePath(filePath + "/");
        } catch (Exception e) {
            log.error("下载hbase数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("下载hbase数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
        }
        return engineSourceHbaseModel;
    }


    private static Tuple2<SavePointDTO, EngineSourceClickhouseModel> initClickhouseSources(EngineSourceDTO source,
                                                                                           StringRedisTemplate redisTemplate,
                                                                                           String processDefineCode,
                                                                                           String taskCode,
                                                                                           String tenantCode,
                                                                                           Logger logger,
                                                                                           Map<String, Integer> sourceCountMap,
                                                                                           Integer pipeline,
                                                                                           Map<String, List<Tuple2<String, List<String>>>> preSqlMap,
                                                                                           Map<String, List<Tuple2<String, List<String>>>> postSqlMap) {
        String configName = source.getConfigName();
        EngineSourceClickhouseDTO clickhouseSource = source.getClickhouseSource();
        String datasourceInfoId = clickhouseSource.getDatasourceInfoId();
        String tableId = clickhouseSource.getTableId();
        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "table");
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        MetadataTableDTO metadataTable = new MetadataTableDTO();
        Object field = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "field");
        if (null != table) {
            metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        }
        List<MetadataColumnDTO> metadataColumnList = new ArrayList<>();
        if (null != field) {
            metadataColumnList = JSONArray.parseArray(field.toString(), MetadataColumnDTO.class);
        }
        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSourceClickhouseModel clickhouseModel = new EngineSourceClickhouseModel();
        String host = JdbcUrlUtils.getIp(databaseConnectionDTO.getUrl()) + ":" + JdbcUrlUtils.getPort(databaseConnectionDTO.getUrl());
        LinkedHashMap<String, String> optionsConfig = new LinkedHashMap();
        optionsConfig.put("database", datasourceDTO.getDbName());
        if (databaseConnectionDTO.getUrl().contains("?")) {
            String url = databaseConnectionDTO.getUrl();
            String params = url.substring(url.lastIndexOf("?") + 1);
            if (StringUtils.isNotBlank(params)) {
                String[] split = params.split("&");
                for (String param : split) {
                    String[] split1 = param.split("=");
                    optionsConfig.put(split1[0], split1[1]);
                }
            }
        }
        clickhouseModel.setOptionsConfig(optionsConfig);
        clickhouseModel.setHost(host);
        clickhouseModel.setUsername(databaseConnectionDTO.getUserName());
        clickhouseModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        clickhouseModel.setDatabase(datasourceDTO.getDbName());


        String sql = clickhouseSource.getQuery();

        List<RowFilterDTO> rowFilterDTOS = clickhouseSource.getWhereCondition();
        //拼接行过滤
        if (!CollectionUtils.isEmpty(rowFilterDTOS)) {
            sql = EngineUtils.getNewWhereCondition(clickhouseSource.getQuery(), SqlUtil.MYSQL, rowFilterDTOS);
        }
        //拼接用户自定义where
        if (StringUtils.isNotEmpty(clickhouseSource.getUserDefineWhere())) {
            String defineWhere = clickhouseSource.getUserDefineWhere().trim();
            String _and = defineWhere.substring(3);
            if ("and".equalsIgnoreCase(_and)) {
                sql = sql + " " + defineWhere;
            } else {
                sql = sql + " and " + defineWhere;
            }
        }

        //拼接自定义条件sql
        String customWhereSql = clickhouseSource.getCustomWhereSql();
        if (StringUtils.isNotBlank(customWhereSql)) {
            sql = sql + " AND " + customWhereSql;
        }

        //数据源类型
        String dataType = datasourceDTO.getDataType();
        DatabaseTypeEnum databaseTypeEnum = DatabaseTypeEnum.getDatabaseTypeEnum(dataType.toLowerCase());
        //获取query语句
        DatabaseHandler handler = DatabaseFactory.getHandler(databaseTypeEnum);
        if (null == handler) {
            log.error("不支持的数据库类型!!!");
            throw new RuntimeException("不支持的数据库类型!!!");
        }
        String incrField = clickhouseSource.getIncrField();
        String min = "0";
        String max = "0";
        if (StringUtils.isNotBlank(incrField)) {
            //最大值
            //增量字段
            String redisMin = redisTemplate.opsForValue().get("dedp:database:engine:savepoint:" + processDefineCode + ":" + taskCode + ":" + metadataTable.getTableName());
            min = StringUtils.defaultString(redisMin, "0");
            Optional<MetadataColumnDTO> incrFieldCol = metadataColumnList.stream().filter(t -> t.getColumnName().equals(incrField)).findFirst();
            if (incrFieldCol.isPresent()) {
                MetadataColumnDTO dto = incrFieldCol.get();
                String maxSql = handler.getMaxRecordValueSql(databaseConnectionDTO.getSchemaName(), metadataTable.getTableName(), incrField, dto.getColumnType(), null);
                max = handler.getMaxRecordValueForExecuteSql(datasourceInfoId, maxSql, logger, min, tenantCode);
                max = StringUtils.defaultString(max, "0");
            }
            logger.info("最小值是{}", incrField + ":" + min);
            logger.info("最大值是{}", incrField + ":" + max);
            logger.info("抽取方式：增量，获取到max为:{}，min值为:{}", max, min);

            sql = sql + EngineInitUtils.initIncrWhereCondition(databaseTypeEnum, incrField, min, max, "");
            clickhouseModel.setResultTableName(StringUtils.isBlank(source.getResultTableName()) ? clickhouseSource.getResultTableName() : source.getResultTableName());
        } else {
            logger.info("抽取方式：全量");
        }
        String newQuery = getNewQuery(sql);
        if (StringUtils.isNotEmpty(newQuery)) {
            clickhouseModel.setSql(newQuery);
        }
        //任务同步进度统计是否开启
        Boolean syncProgress = source.getSyncProgress();
        if (null != syncProgress && syncProgress) {
            //开启
            Integer sourceCount = handler.getCountByQuery(newQuery, datasourceDTO, tenantCode);
            sourceCountMap.put(String.valueOf(pipeline), sourceCount);
            logger.info("任务同步进度统计:开启，获取到count为:{}", sourceCount);
        } else {
            //关闭
            logger.info("任务同步进度统计:关闭");
        }
        clickhouseModel.setResultTableName(StringUtils.isBlank(source.getResultTableName()) ? clickhouseSource.getResultTableName() : source.getResultTableName());
        //记录前后置sql
        EngineUtils.addPrePostSqlMap(datasourceInfoId, clickhouseSource.getPreSql(), clickhouseSource.getPostSql(), preSqlMap, postSqlMap, configName);
        return new Tuple2<>(new SavePointDTO(metadataTable.getTableName(), max, min), clickhouseModel);
    }

    private static EngineSourceOssHuaweiFileModel initOssHuaweiFileSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceOssHuaweiFileDTO ossHuaweiFileSource = engineSourceDTO.getOssHuaweiFileSource();
        String datasourceInfoId = ossHuaweiFileSource.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSourceOssHuaweiFileModel sourceOssHuaweiFileModel = new EngineSourceOssHuaweiFileModel();
        sourceOssHuaweiFileModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceOssHuaweiFileModel.setPath(ossHuaweiFileSource.getPath());
        sourceOssHuaweiFileModel.setBucket("obs://" + databaseConnectionDTO.getBucket());
        sourceOssHuaweiFileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        sourceOssHuaweiFileModel.setAccessSecret(databaseConnectionDTO.getAccessSecret());
        sourceOssHuaweiFileModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        sourceOssHuaweiFileModel.setFileFormatType(ossHuaweiFileSource.getFileFormatType());
        if (ossHuaweiFileSource.getReadColumns() != null && ossHuaweiFileSource.getReadColumns().size() > 0) {
            sourceOssHuaweiFileModel.setReadColumns(JSONObject.toJSONString(ossHuaweiFileSource.getReadColumns()));
        }
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != ossHuaweiFileSource.getFieldDelimiter() && !ossHuaweiFileSource.getFieldDelimiter().equals("\\001")) {
            sourceOssHuaweiFileModel.setFieldDelimiter(ossHuaweiFileSource.getFieldDelimiter());
        }
        sourceOssHuaweiFileModel.setParsePartitionFromPath(ossHuaweiFileSource.getParsePartitionFromPath());
        sourceOssHuaweiFileModel.setDateFormat(ossHuaweiFileSource.getDateFormat());
        sourceOssHuaweiFileModel.setDatetimeFormat(ossHuaweiFileSource.getDatetimeFormat());
        sourceOssHuaweiFileModel.setTimeFormat(ossHuaweiFileSource.getTimeFormat());
        sourceOssHuaweiFileModel.setSkipHeaderRowNumber(ossHuaweiFileSource.getSkipHeaderRowNumber());
        sourceOssHuaweiFileModel.setSchema(ossHuaweiFileSource.getSchema());
        sourceOssHuaweiFileModel.setCompressCodec(ossHuaweiFileSource.getCompressCodec());
        sourceOssHuaweiFileModel.setFileFilterPattern(ossHuaweiFileSource.getFileFilterPattern());
        sourceOssHuaweiFileModel.setSheetName(ossHuaweiFileSource.getSheetName());
        sourceOssHuaweiFileModel.setEncoding(ossHuaweiFileSource.getEndpoint());
        return sourceOssHuaweiFileModel;
    }

    private static EngineSourceS3FileModel initS3FileSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceS3FileDTO s3FileSource = engineSourceDTO.getS3FileSource();
        String datasourceInfoId = s3FileSource.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        Map<String, Object> datasourceMap = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        String secretKey = datasourceMap.get("secretKey") + "";
        String endPoint = datasourceMap.get("endPoint") + "";
        String accessKey = datasourceMap.get("accessKey") + "";
        String Bucket = datasourceMap.get("Bucket") + "";
        EngineSourceS3FileModel sourceS3FileModel = new EngineSourceS3FileModel();
        sourceS3FileModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceS3FileModel.setPath(s3FileSource.getPath());
        sourceS3FileModel.setFileFormatType(s3FileSource.getFileFormatType());
        sourceS3FileModel.setBucket("s3a://" + Bucket);
        sourceS3FileModel.setEndpoint(endPoint);
        sourceS3FileModel.setFsS3aAwsCredentialsProvider(s3FileSource.getFsS3aAwsCredentialsProvider());
        if (null != s3FileSource.getReadColumns() && s3FileSource.getReadColumns().size() > 0) {
            sourceS3FileModel.setReadColumns(JSONObject.toJSONString(s3FileSource.getReadColumns()));
        }
        sourceS3FileModel.setAccessKey(accessKey);
        sourceS3FileModel.setAccessSecret(secretKey);
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != s3FileSource.getFieldDelimiter() && !s3FileSource.getFieldDelimiter().equals("\\001")) {
            sourceS3FileModel.setFieldDelimiter(s3FileSource.getFieldDelimiter());
        }
        sourceS3FileModel.setParsePartitionFromPath(s3FileSource.getParsePartitionFromPath());
        sourceS3FileModel.setDateFormat(s3FileSource.getDateFormat());
        sourceS3FileModel.setDatetimeFormat(s3FileSource.getDatetimeFormat());
        sourceS3FileModel.setTimeFormat(s3FileSource.getTimeFormat());
        sourceS3FileModel.setSkipHeaderRowNumber(s3FileSource.getSkipHeaderRowNumber());
        sourceS3FileModel.setSchema(s3FileSource.getSchema());
        sourceS3FileModel.setSheetName(s3FileSource.getSheetName());
        sourceS3FileModel.setCompressCodec(s3FileSource.getCompressCodec());
        sourceS3FileModel.setEncoding(s3FileSource.getEncoding());
        return sourceS3FileModel;

    }

    private static EngineSourceKafkaModel initKafkaSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceKafkaDTO kafkaSource = engineSourceDTO.getKafkaSource();
        String datasourceInfoId = kafkaSource.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        Map<String, Object> kafkaJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        EngineSourceKafkaModel sourceKafkaModel = new EngineSourceKafkaModel();
        sourceKafkaModel.setTopic(kafkaSource.getTopic());
        String brokerList = StringUtils.defaultString(kafkaJson.get("brokerList") + "", "");
        if (StringUtils.isEmpty(brokerList)) {
            throw new RuntimeException("源端为kafka时候，获取到brokerList地址为空，请检查参数!");
        }
        Map<String, String> kafkaConfig = kafkaSource.getKafkaConfig();
        if (null != kafkaJson.get("kafkaReact") && StringUtils.isNotBlank(kafkaJson.get("kafkaReact") + "")) {
            String kafkaReact = kafkaJson.get("kafkaReact") + "";
            if ("SASL_PLAINTEXT".equals(kafkaReact)) {
                if (null == kafkaConfig) {
                    kafkaConfig = new LinkedHashMap<>();
                }
                kafkaConfig.put("security.protocol", "SASL_PLAINTEXT");
                kafkaConfig.put("sasl.mechanism", "SCRAM-SHA-256");
                String jaas = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\\\"%s\\\" password=\\\"%s\\\";";
                kafkaConfig.put("sasl.jaas.config", String.format(jaas, kafkaJson.get("username"), kafkaJson.get("password")));
            }
        }
        sourceKafkaModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceKafkaModel.setBootstrapServers(kafkaJson.get("brokerList") + "");
        sourceKafkaModel.setPattern(kafkaSource.getPattern());
        sourceKafkaModel.setConsumerGroup(kafkaSource.getConsumerGroup());
        sourceKafkaModel.setCommitOnCheckpoint(kafkaSource.getCommitOnCheckpoint());
        sourceKafkaModel.setKafkaConfig(kafkaConfig);
        sourceKafkaModel.setSchema(kafkaSource.getSchema());
        sourceKafkaModel.setFormat(kafkaSource.getFormat());
        sourceKafkaModel.setFormatErrorHandleWay(kafkaSource.getFormatErrorHandleWay());
        sourceKafkaModel.setFieldDelimiter(kafkaSource.getFieldDelimiter());
        sourceKafkaModel.setStartMode(kafkaSource.getStartMode());
        sourceKafkaModel.setStartModeOffsets(kafkaSource.getStartModeOffsets());
        sourceKafkaModel.setStartModeTimestamp(kafkaSource.getStartModeTimestamp());
        sourceKafkaModel.setPartitionDiscoveryIntervalMillis(kafkaSource.getPartitionDiscoveryIntervalMillis());
        sourceKafkaModel.setMappers(kafkaSource.getMappers());
        return sourceKafkaModel;
    }

    private static EngineSourceOssAliFileModel initOssAliSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceOssAliFileDTO ossAliFileSource = engineSourceDTO.getOssAliFileSource();
        String datasourceInfoId = ossAliFileSource.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSourceOssAliFileModel sourceOssAliFileModel = new EngineSourceOssAliFileModel();
        sourceOssAliFileModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceOssAliFileModel.setPath(ossAliFileSource.getPath());
        sourceOssAliFileModel.setBucket("oss://" + databaseConnectionDTO.getBucket());
        sourceOssAliFileModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        sourceOssAliFileModel.setAccessSecret(databaseConnectionDTO.getAccessSecret());
        sourceOssAliFileModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        sourceOssAliFileModel.setFileFormatType(ossAliFileSource.getFileFormatType());
        if (ossAliFileSource.getReadColumns() != null && ossAliFileSource.getReadColumns().size() > 0) {
            sourceOssAliFileModel.setReadColumns(JSONObject.toJSONString(ossAliFileSource.getReadColumns()));
        }
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != ossAliFileSource.getFieldDelimiter() && !ossAliFileSource.getFieldDelimiter().equals("\\001")) {
            sourceOssAliFileModel.setFieldDelimiter(ossAliFileSource.getFieldDelimiter());
        }
        sourceOssAliFileModel.setParsePartitionFromPath(ossAliFileSource.getParsePartitionFromPath());
        sourceOssAliFileModel.setDateFormat(ossAliFileSource.getDateFormat());
        sourceOssAliFileModel.setDatetimeFormat(ossAliFileSource.getDatetimeFormat());
        sourceOssAliFileModel.setTimeFormat(ossAliFileSource.getTimeFormat());
        sourceOssAliFileModel.setSkipHeaderRowNumber(ossAliFileSource.getSkipHeaderRowNumber());
        sourceOssAliFileModel.setSchema(ossAliFileSource.getSchema());
        sourceOssAliFileModel.setCompressCodec(ossAliFileSource.getCompressCodec());
        sourceOssAliFileModel.setFileFilterPattern(ossAliFileSource.getFileFilterPattern());
        sourceOssAliFileModel.setSheetName(ossAliFileSource.getSheetName());
        sourceOssAliFileModel.setEncoding(ossAliFileSource.getEncoding());
        return sourceOssAliFileModel;

    }

    private static EngineSourceFTPModel initFtpSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceFTPDTO ftpFileSource = engineSourceDTO.getFtpFileSource();
        String datasourceInfoId = ftpFileSource.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSourceFTPModel sourceFTPModel = new EngineSourceFTPModel();
        sourceFTPModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceFTPModel.setHost(databaseConnectionDTO.getHost());
        sourceFTPModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        sourceFTPModel.setPort(databaseConnectionDTO.getPort());
        sourceFTPModel.setUser(databaseConnectionDTO.getUserName());
        sourceFTPModel.setPath(ftpFileSource.getPath());
        sourceFTPModel.setFileFormatType(ftpFileSource.getFileFormatType());
        sourceFTPModel.setConnectionMode(ftpFileSource.getConnectionMode());
        if (ftpFileSource.getReadColumns() != null && ftpFileSource.getReadColumns().size() > 0) {
            sourceFTPModel.setReadColumns(JSONObject.toJSONString(ftpFileSource.getReadColumns()));
        }
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != ftpFileSource.getFieldDelimiter() && !ftpFileSource.getFieldDelimiter().equals("\\001")) {
            sourceFTPModel.setFieldDelimiter(ftpFileSource.getFieldDelimiter());
        }
        sourceFTPModel.setParsePartitionFromPath(ftpFileSource.getParsePartitionFromPath());
        sourceFTPModel.setDateFormat(ftpFileSource.getDateFormat());
        sourceFTPModel.setDatetimeFormat(ftpFileSource.getDatetimeFormat());
        sourceFTPModel.setTimeFormat(ftpFileSource.getTimeFormat());
        sourceFTPModel.setSkipHeaderRowNumber(ftpFileSource.getSkipHeaderRowNumber());
        sourceFTPModel.setSchema(ftpFileSource.getSchema());
        sourceFTPModel.setCompressCodec(ftpFileSource.getCompressCodec());
        sourceFTPModel.setFileFilterPattern(ftpFileSource.getFileFilterPattern());
        sourceFTPModel.setSheetName(ftpFileSource.getSheetName());
        return sourceFTPModel;
    }

    private static EngineSourceSFTPModel initSftpSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {

        EngineSourceSFTPDTO sftpFileSource = engineSourceDTO.getSftpFileSource();
        String datasourceInfoId = sftpFileSource.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSourceSFTPModel sourceSFTPModel = new EngineSourceSFTPModel();
        sourceSFTPModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceSFTPModel.setHost(databaseConnectionDTO.getHost());
        sourceSFTPModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        sourceSFTPModel.setPort(databaseConnectionDTO.getPort());
        sourceSFTPModel.setUser(databaseConnectionDTO.getUserName());
        sourceSFTPModel.setPath(sftpFileSource.getPath());
        sourceSFTPModel.setFileFormatType(sftpFileSource.getFileFormatType());
        if (sftpFileSource.getReadColumns() != null && sftpFileSource.getReadColumns().size() > 0) {
            sourceSFTPModel.setReadColumns(JSONObject.toJSONString(sftpFileSource.getReadColumns()));
        }
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != sftpFileSource.getFieldDelimiter() && !sftpFileSource.getFieldDelimiter().equals("\\001")) {
            sourceSFTPModel.setFieldDelimiter(sftpFileSource.getFieldDelimiter());
        }
        sourceSFTPModel.setParsePartitionFromPath(sftpFileSource.getParsePartitionFromPath());
        sourceSFTPModel.setDateFormat(sftpFileSource.getDateFormat());
        sourceSFTPModel.setDatetimeFormat(sftpFileSource.getDatetimeFormat());
        sourceSFTPModel.setTimeFormat(sftpFileSource.getTimeFormat());
        sourceSFTPModel.setSkipHeaderRowNumber(sftpFileSource.getSkipHeaderRowNumber());
        sourceSFTPModel.setSchema(sftpFileSource.getSchema());
        sourceSFTPModel.setCompressCodec(sftpFileSource.getCompressCodec());
        sourceSFTPModel.setFileFilterPattern(sftpFileSource.getFileFilterPattern());
        sourceSFTPModel.setSheetName(sftpFileSource.getSheetName());
        return sourceSFTPModel;
    }

    private static EngineSourceLocalFileModel initLocalFileSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceLocalFileDTO localFileSource = engineSourceDTO.getLocalFileSource();
        EngineSourceLocalFileModel engineSourceLocalFileModel = new EngineSourceLocalFileModel();
        engineSourceLocalFileModel.setPath(localFileSource.getPath());
        engineSourceLocalFileModel.setFileFormatType(localFileSource.getFileFormatType());
        if (null != localFileSource.getReadColumns() && localFileSource.getReadColumns().size() > 0) {
            engineSourceLocalFileModel.setReadColumns(JSONObject.toJSONString(localFileSource.getReadColumns()));
        }
        //st默认就是\001，不需要传，传\001这个值还会报错
        if (null != localFileSource.getFieldDelimiter() && !localFileSource.getFieldDelimiter().equals("\\001")) {
            engineSourceLocalFileModel.setFieldDelimiter(localFileSource.getFieldDelimiter());
        }
        engineSourceLocalFileModel.setParsePartitionFromPath(Boolean.toString(localFileSource.getParsePartitionFromPath()));
        engineSourceLocalFileModel.setDateFormat(localFileSource.getDateFormat());
        engineSourceLocalFileModel.setTimeFormat(localFileSource.getTimeFormat());
        engineSourceLocalFileModel.setDatetimeFormat(localFileSource.getDatetimeFormat());
        if (localFileSource.getSkipHeaderRowNumber() > 0) {
            engineSourceLocalFileModel.setSkipHeaderRowNumber(Long.toString(localFileSource.getSkipHeaderRowNumber()));
        }
        engineSourceLocalFileModel.setSchema(localFileSource.getSchema());
        engineSourceLocalFileModel.setSheetName(localFileSource.getSheetName());
        engineSourceLocalFileModel.setCompressCodec(localFileSource.getCompressCodec());
        engineSourceLocalFileModel.setResultTableName(engineSourceDTO.getResultTableName());
        engineSourceLocalFileModel.setEncoding(localFileSource.getEncoding());
        return engineSourceLocalFileModel;
    }

    private static EngineSourceHdfsFileModel initHdfsFileSources(EngineSourceDTO engineSourceDTO, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceHdfsFileDTO hdfsFileSource = engineSourceDTO.getHdfsFileSource();
        String datasourceInfoId = hdfsFileSource.getDatasourceInfoId();

        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + hdfsFileSource.getDatasourceInfoId());
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        Map<String, Object> hdfsJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
        EngineSourceHdfsFileModel sourceHdfsFileModel = new EngineSourceHdfsFileModel();
        try {
            Map<String, String> hiveFilePath = HiveFileUtils.setFile(hdfsJson, hdfsFileSource.getDatasourceInfoId());
            if (StringUtils.isNotBlank(hiveFilePath.get("kerberosPrincipal"))) {
                sourceHdfsFileModel.setKerberosPrincipal(hiveFilePath.get("kerberosPrincipal"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("krb5Path"))) {
                sourceHdfsFileModel.setKrb5Path(hiveFilePath.get("krb5Path"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("kerberosKeytabPath"))) {
                sourceHdfsFileModel.setKerberosKeytabPath(hiveFilePath.get("kerberosKeytabPath"));
            }
            if (StringUtils.isNotBlank(hiveFilePath.get("hdfsSitePath"))) {
                sourceHdfsFileModel.setHdfsSitePath(hiveFilePath.get("hdfsSitePath"));
            }
        } catch (Exception e) {
            log.error("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
            throw new RuntimeException("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
        }
        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        sourceHdfsFileModel.setResultTableName(engineSourceDTO.getResultTableName());
        sourceHdfsFileModel.setFsDefaultFS(databaseConnectionDTO.getUrl());
        sourceHdfsFileModel.setPath(hdfsFileSource.getPath());
        sourceHdfsFileModel.setFileFormatType(hdfsFileSource.getFileFormatType());
        sourceHdfsFileModel.setFieldDelimiter(hdfsFileSource.getFieldDelimiter());
        sourceHdfsFileModel.setSchema(hdfsFileSource.getSchema());
        //以下非必填
        List<String> readColumns = hdfsFileSource.getReadColumns();
        if (null != readColumns && readColumns.size() > 0) {
            sourceHdfsFileModel.setReadColumns(JSONObject.toJSONString(readColumns));
        }
        sourceHdfsFileModel.setHdfsSitePath(hdfsFileSource.getHdfsSitePath());
        sourceHdfsFileModel.setParsePartitionFromPath(hdfsFileSource.getParsePartitionFromPath());
        sourceHdfsFileModel.setDateFormat(hdfsFileSource.getDateFormat());
        sourceHdfsFileModel.setDatetimeFormat(hdfsFileSource.getDatetimeFormat());
        sourceHdfsFileModel.setTimeFormat(hdfsFileSource.getTimeFormat());
        sourceHdfsFileModel.setRemoteUser(hdfsFileSource.getRemoteUser());
        sourceHdfsFileModel.setSkipHeaderRowNumber(hdfsFileSource.getSkipHeaderRowNumber());
        sourceHdfsFileModel.setSheetName(hdfsFileSource.getSheetName());
        sourceHdfsFileModel.setCompressCodec(hdfsFileSource.getCompressCodec());
        return sourceHdfsFileModel;
    }

    /**
     * 组装EngineSourcePostgresCDCModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourcePostgresCDCModel initPostgresCDCSources(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourcePostgreCDCDTO postgresqlCdcSource = source.getPostgresqlCdcSource();
        String datasourceInfoId = postgresqlCdcSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + postgresqlCdcSource.getTableId(), "table");
        if (StringUtils.isBlank(datasourceStr) || null == table) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //获取连接信息
        MetadataTableDTO metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourcePostgresCDCModel engineSourcePostgresCDCModel = new EngineSourcePostgresCDCModel();
        engineSourcePostgresCDCModel.setBaseUrl(databaseConnectionDTO.getUrl());
        engineSourcePostgresCDCModel.setDatabaseNames(datasourceDTO.getDbName());
        engineSourcePostgresCDCModel.setSchemaNames(databaseConnectionDTO.getSchemaName());
        engineSourcePostgresCDCModel.setUsername(databaseConnectionDTO.getUserName());
        engineSourcePostgresCDCModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
//        engineSourcePostgresCDCModel.setTableNames(databaseConnectionDTO.getSchemaName() + "." + metadataTable.getTableName());
        //seatunnel2.3.4版本要拼接三层
        engineSourcePostgresCDCModel.setTableNames(datasourceDTO.getDbName() + "." + databaseConnectionDTO.getSchemaName() +
                "." + metadataTable.getTableName());
        engineSourcePostgresCDCModel.setResultTableName(source.getResultTableName());
        return engineSourcePostgresCDCModel;
    }

    /**
     * 组装EngineSourceSqlserverCDCModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceSqlserverCDCModel initSqlServerCDCSources(EngineSourceDTO source,
                                                                         StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceSqlserverCDCDTO sqlserverCdcSource = source.getSqlserverCdcSource();
        String datasourceInfoId = sqlserverCdcSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + sqlserverCdcSource.getTableId(), "table");
        if (StringUtils.isBlank(datasourceStr) || null == table) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //获取连接信息
        MetadataTableDTO metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourceSqlserverCDCModel engineSourceSqlserverCDCModel = new EngineSourceSqlserverCDCModel();
        engineSourceSqlserverCDCModel.setBaseUrl(databaseConnectionDTO.getUrl());
        engineSourceSqlserverCDCModel.setDatabaseNames(datasourceDTO.getDbName());
        engineSourceSqlserverCDCModel.setUsername(databaseConnectionDTO.getUserName());
        engineSourceSqlserverCDCModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        engineSourceSqlserverCDCModel.setTableNames(datasourceDTO.getDbName() + "." + databaseConnectionDTO.getSchemaName() + "." + metadataTable.getTableName());
        engineSourceSqlserverCDCModel.setResultTableName(source.getResultTableName());
        return engineSourceSqlserverCDCModel;
    }

    /**
     * 组装EngineSourceMysqlCDCModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceMysqlCDCModel initMysqlCDCSources(EngineSourceDTO source,
                                                                 StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceMysqlCDCDTO mysqlCdcSource = source.getMysqlCdcSource();
        String datasourceInfoId = mysqlCdcSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + mysqlCdcSource.getTableId(), "table");
        if (StringUtils.isBlank(datasourceStr) || null == table) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //获取连接信息
        MetadataTableDTO metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourceMysqlCDCModel engineSourceMysqlCDCModel = new EngineSourceMysqlCDCModel();
        if (StringUtils.isNotBlank(source.getMysqlCdcSource().getServerId())) {
            engineSourceMysqlCDCModel.setServerId(source.getMysqlCdcSource().getServerId());
        }
        engineSourceMysqlCDCModel.setBaseUrl(databaseConnectionDTO.getUrl());
        engineSourceMysqlCDCModel.setUsername(databaseConnectionDTO.getUserName());
        engineSourceMysqlCDCModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        engineSourceMysqlCDCModel.setTableNames(databaseConnectionDTO.getSchemaName() + "." + metadataTable.getTableName());
        engineSourceMysqlCDCModel.setResultTableName(source.getResultTableName());
        return engineSourceMysqlCDCModel;
    }

    /**
     * 组装EngineSourceOracleCDCModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceOracleCDCModel initOracleCDCSources(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceOracleCDCDTO oracleCdcSource = source.getOracleCdcSource();
        String datasourceInfoId = oracleCdcSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + oracleCdcSource.getTableId(), "table");
        if (StringUtils.isBlank(datasourceStr) || null == table) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //获取连接信息
        MetadataTableDTO metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourceOracleCDCModel engineSourceOracleCDCModel = new EngineSourceOracleCDCModel();
        engineSourceOracleCDCModel.setBaseUrl(databaseConnectionDTO.getUrl());
        //获取服务名
        String jdbcUrl = databaseConnectionDTO.getUrl();
        String serviceName = jdbcUrl.substring(jdbcUrl.lastIndexOf("/") + 1);
        engineSourceOracleCDCModel.setDatabaseNames(serviceName.toUpperCase());
        engineSourceOracleCDCModel.setSchemaName(databaseConnectionDTO.getSchemaName());
        engineSourceOracleCDCModel.setUsername(databaseConnectionDTO.getUserName());
        engineSourceOracleCDCModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
//        engineSourceOracleCDCModel.setTableNames(databaseConnectionDTO.getSchemaName() + "." + metadataTable.getTableName());
        //seatunnel2.3.4版本要拼接三层
        engineSourceOracleCDCModel.setTableNames(serviceName.toUpperCase() + "." + databaseConnectionDTO.getSchemaName() + "." + metadataTable.getTableName());
        engineSourceOracleCDCModel.setResultTableName(source.getResultTableName());
        return engineSourceOracleCDCModel;
    }


    /**
     * 组装EngineSourceJdbcModel
     *
     * @param source
     * @param redisTemplate
     * @param processDefineCode
     * @param taskCode
     * @param tenantCode
     * @return
     */
    private static Tuple2<SavePointDTO, EngineSourceJdbcModel> initJdbcSources(EngineSourceDTO source,
                                                                               StringRedisTemplate redisTemplate,
                                                                               String processDefineCode,
                                                                               String taskCode,
                                                                               String tenantCode,
                                                                               List<EngineSinkDTO> sinkList, String gatewayHttp,
                                                                               Map<String, Property> paramsMap,
                                                                               Logger logger,
                                                                               Map<String, Integer> sourceCountMap,
                                                                               Integer pipeline,
                                                                               Map<String, List<Tuple2<String, List<String>>>> preSqlMap,
                                                                               Map<String, List<Tuple2<String, List<String>>>> postSqlMap) {
        String configName = source.getConfigName();
        EngineSourceJDBCDTO jdbcSource = source.getJdbcSource();
        String tableId = jdbcSource.getTableId();

        //获取表信息
        String datasourceInfoId = jdbcSource.getDatasourceInfoId();
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
//        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "table");
        Object field = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "field");
        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
//        MetadataTableDTO metadataTable = new MetadataTableDTO();
        List<MetadataColumnDTO> metadataColumnList = new ArrayList<>();
        if (null != field) {
            metadataColumnList = JSONArray.parseArray(field.toString(), MetadataColumnDTO.class);
        }

        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSourceJdbcModel engineSourceJdbcModel = new EngineSourceJdbcModel();
        //如果是tdsql 数据源，需要走代理
        if (true == jdbcSource.isUseProxy() && StringUtils.isNotEmpty(jdbcSource.getIpPort())) {
            String url = databaseConnectionDTO.getUrl();
            engineSourceJdbcModel.setUrl(TdSqlProxyUtils.replaceHostPort(url, jdbcSource.getIpPort(), logger));
        } else {
            engineSourceJdbcModel.setUrl(databaseConnectionDTO.getUrl());
        }
        //数据源类型
        String dataType = datasourceDTO.getDataType();
        DatabaseTypeEnum databaseTypeEnum = DatabaseTypeEnum.getDatabaseTypeEnum(dataType.toLowerCase());
        engineSourceJdbcModel.setDriver(databaseConnectionDTO.getDriverClassName());

        engineSourceJdbcModel.setUser(databaseConnectionDTO.getUserName());
        //2025-06-13注释掉的，这个参数在ftl中没有用，在initSources()方法上setPipeline(),pipeline=ftl中的table_path
//        engineSourceJdbcModel.setTablePath(jdbcSource.getTablePath());
        engineSourceJdbcModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        engineSourceJdbcModel.setSchemaName(jdbcSource.getSchemaName());
        engineSourceJdbcModel.setExtractType(jdbcSource.getExtractType());
        //oceanBase需要区分是mysql内核或oracle内核
        if (DatabaseTypeEnum.OCEANBASE_FOR_ORACLE.equals(databaseTypeEnum)) {
            engineSourceJdbcModel.setCompatibleMode("oracle");
        }
        //如果是hive数据源，根据下载xml文件和Kerberos认证文件
        if (DatabaseTypeEnum.HIVE.equals(databaseTypeEnum)) {
            //下载hive数据源的文件并给hiveModel设置文件路径
            Map<String, Object> hvieJson = JSON.parseObject(MyBase64.decoder(datasourceDTO.getDataJson()), Map.class);
            try {
                Map<String, String> hiveFilePath = HiveFileUtils.setFile(hvieJson, datasourceInfoId);
                if (StringUtils.isNotBlank(hiveFilePath.get("useKerberos"))) {
                    engineSourceJdbcModel.setUseKerberos("true");
                    engineSourceJdbcModel.setKerberosPrincipal(hiveFilePath.get("kerberosPrincipal"));
                    engineSourceJdbcModel.setKrb5Path(hiveFilePath.get("krb5Path"));
                    engineSourceJdbcModel.setKerberosKeytabPath(hiveFilePath.get("kerberosKeytabPath"));
                }
                if (StringUtils.isNotBlank(hiveFilePath.get("hdfsSitePath"))) {
                    engineSourceJdbcModel.setHdfsSitePath(hiveFilePath.get("hdfsSitePath"));
                }
                if (StringUtils.isNotBlank(hiveFilePath.get("hiveSitePath"))) {
                    engineSourceJdbcModel.setHiveSitePath(hiveFilePath.get("hiveSitePath"));
                }
            } catch (Exception e) {
                log.error("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因={}", e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("下载hive数据源的文件并给EngineModel设置文件路径失败！失败原因=" + e.getMessage());
            }
        }
        //获取query语句
        String query = jdbcSource.getQuery();
        DatabaseHandler handler = DatabaseFactory.getHandler(databaseTypeEnum);
        if (null == handler) {
            log.error("不支持的数据库类型!!!");
            throw new RuntimeException("不支持的数据库类型!!!");
        }
        //20250626 将拼接where条件的代码抽取成方法，TdSqlProxyUtils类中并行获取源表数据是否为空时也需要拼接
        query = AppendWhere.appendWhere(jdbcSource, query, databaseTypeEnum, paramsMap);

        String incrField = jdbcSource.getIncrField();
        String min = "0";
        String max = "0";
        if (StringUtils.isNotBlank(incrField)) {
            //20250626 将拼接增量where条件的代码抽取成方法，TdSqlProxyUtils类中并行获取源表数据是否为空时也需要拼接，但是目前这种做法如果页面选增量字段并且走代理，会查询两次增量字段最大值
            Tuple3<String, String, String> tuple3 = AppendWhere.appendIncrWhere(metadataColumnList, jdbcSource, tableId, query,
                    incrField, processDefineCode, taskCode,
                    tenantCode, redisTemplate,
                    databaseConnectionDTO, databaseTypeEnum,
                    handler, logger, datasourceInfoId);
            query = tuple3.getV1();
            min = tuple3.getV2();
            max = tuple3.getV3();
        } else {
            logger.info("抽取方式：全量");
        }
//        List<String> pks = metadataColumnList.stream().filter(t -> "1".equals(t.getColumnPrimaryKey())).map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
        engineSourceJdbcModel.setResultTableName(StringUtils.isBlank(source.getResultTableName()) ? jdbcSource.getResultTableName() : source.getResultTableName());
        String newQuery = getNewQuery(query);
        logger.info("执行的sql是{}", newQuery);
        //engineSourceJdbcModel.setQuery(query.replace("\n", " ").replace("\t", " "));
//        if (DatabaseTypeEnum.ORACLE.equals(databaseTypeEnum)) {
//            engineSourceJdbcModel.setQuery(newQuery.toUpperCase());
//        } else {
//            engineSourceJdbcModel.setQuery(newQuery);
//        }
        engineSourceJdbcModel.setQuery(newQuery);
        //任务同步进度统计是否开启
        Boolean syncProgress = source.getSyncProgress();
        if (null != syncProgress && syncProgress) {
            //开启
            Integer sourceCount = handler.getCountByQuery(newQuery, datasourceDTO, tenantCode);
            sourceCountMap.put(String.valueOf(pipeline), sourceCount);
            logger.info("任务同步进度统计:开启，获取到count为:{}", sourceCount);
        } else {
            //关闭
            logger.info("任务同步进度统计:关闭");
        }
        //优先取页面设置的拆片字段，没有就取增量字段，都没有就不设置
//        String partitionColumn = StringUtils.defaultString(jdbcSource.getPartitionColumn(), incrField);
        String partitionColumn = jdbcSource.getPartitionColumn();
        if (StringUtils.isNotBlank(partitionColumn)) {
            engineSourceJdbcModel.setPartitionColumn(partitionColumn);
            engineSourceJdbcModel.setPartitionNum(StringUtils.defaultString(jdbcSource.getPartitionNum(), "5"));
            //达梦、sap_hana、db2分片时分片字段需要是数值型，不然分配时会报错，具体看问题解决文档
            if (DatabaseTypeEnum.DMDB.getName().equalsIgnoreCase(dataType)
                    || DatabaseTypeEnum.SAP_HANA.getName().equalsIgnoreCase(dataType)
                    || DatabaseTypeEnum.DB2.getName().equalsIgnoreCase(dataType)) {
                cancelSharding(metadataColumnList, partitionColumn, engineSourceJdbcModel);
            }
        }
        if (null != jdbcSource.getParallelism()) {
            engineSourceJdbcModel.setParallelism(String.valueOf(jdbcSource.getParallelism()));
        }
        engineSourceJdbcModel.setBatchSize(jdbcSource.getBatchSize() == null ? "1024" : jdbcSource.getBatchSize().toString());
//        resultpkNames.put(engineSourceJdbcModel.getResultTableName(), pks);

        //空值处理
        String emptyDataStrategy = null;
        for (EngineSinkDTO sink : sinkList) {
            EngineSinkTypeEnum sinkType = sink.getType();
            if (sinkType == EngineSinkTypeEnum.FTPFILE || sinkType == EngineSinkTypeEnum.SFTPFILE ||
                    sinkType == EngineSinkTypeEnum.S3FILE || sinkType == EngineSinkTypeEnum.LOCALFILE ||
                    sinkType == EngineSinkTypeEnum.OSS_ALI || sinkType == EngineSinkTypeEnum.OSS_HUAWEI ||
                    sinkType == EngineSinkTypeEnum.HDFSFILE || sinkType == EngineSinkTypeEnum.ARGO_HDFS_FILE) {
                if (StringUtils.isNotEmpty(jdbcSource.getEmptyDataStrategy())) {
                    emptyDataStrategy = jdbcSource.getEmptyDataStrategy();
                }
                if (null == emptyDataStrategy) {
                    emptyDataStrategy = String.valueOf(ConnectionFactory.checkTableIsEmpty(query, datasourceInfoId, tenantCode, gatewayHttp, jdbcSource.getIpPort()));
                }
                engineSourceJdbcModel.setEmptyDataStrategy(emptyDataStrategy);
                //源端控制处理参数
                switch (sinkType) {
                    case FTPFILE:
                        //sink值和源端一致
                        if (null != sink.getFtpFileSink()) {
                            sink.getFtpFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case SFTPFILE:
                        //sink值和源端一致
                        if (null != sink.getSftpFileSink()) {
                            sink.getSftpFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case S3FILE:
                        //sink值和源端一致
                        if (null != sink.getS3FileSink()) {
                            sink.getS3FileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case LOCALFILE:
                        //sink值和源端一致
                        if (null != sink.getLocalFileSink()) {
                            sink.getLocalFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case OSS_ALI:
                        //sink值和源端一致
                        if (null != sink.getOssAliFileSink()) {
                            sink.getOssAliFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case OSS_HUAWEI:
                        //sink值和源端一致
                        if (null != sink.getOssHuaweiFileSink()) {
                            sink.getOssHuaweiFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case HDFSFILE:
                        //sink值和源端一致
                        if (null != sink.getHdfsFileSink()) {
                            sink.getHdfsFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                    case ARGO_HDFS_FILE:
                        //sink值和源端一致
                        if (null != sink.getArgoHdfsFileSink()) {
                            sink.getArgoHdfsFileSink().setEmptyDataStrategy(emptyDataStrategy);
                        }
                        break;
                }
            }
        }

        //记录前后置sql
        EngineUtils.addPrePostSqlMap(datasourceInfoId, jdbcSource.getPreSql(), jdbcSource.getPostSql(), preSqlMap, postSqlMap, configName);

        return new Tuple2<>(new SavePointDTO(jdbcSource.getTableName(), max, min), engineSourceJdbcModel);
    }

    private static void cancelSharding(List<MetadataColumnDTO> metadataColumnList, String partitionColumn, EngineSourceJdbcModel engineSourceJdbcModel) {
        Optional<MetadataColumnDTO> incrFieldCol = metadataColumnList.stream().filter(t -> t.getColumnName().equals(partitionColumn)).findFirst();
        if (incrFieldCol.isPresent()) {
            int dataType = Integer.parseInt(incrFieldCol.get().getDataType());
            //是字符类型就不分片
            if (dataType == Types.CHAR
                    || dataType == Types.VARCHAR
                    || dataType == Types.LONGVARCHAR
                    || dataType == Types.CLOB
                    || dataType == Types.NCHAR
                    || dataType == Types.NVARCHAR
                    || dataType == Types.LONGNVARCHAR
                    || dataType == Types.NCLOB) {
                engineSourceJdbcModel.setPartitionColumn(null);
                engineSourceJdbcModel.setPartitionNum(null);
            }
        }
    }


    private static Tuple2<SavePointDTO, EngineSourceDorisModel> initDorisSources(EngineSourceDTO source,
                                                                                 StringRedisTemplate redisTemplate,
                                                                                 String processDefineCode,
                                                                                 String taskCode,
                                                                                 String tenantCode,
                                                                                 String gatewayHttp, Logger logger) {
        EngineSourceDorisDTO dorisSource = source.getDorisSource();
        String datasourceInfoId = dorisSource.getDatasourceInfoId();
        String tableId = dorisSource.getTableId();
        //获取表信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "table");
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //数据源、表、字段信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        MetadataTableDTO metadataTable = new MetadataTableDTO();
        Object field = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "field");
        if (null != table) {
            metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        }
        List<MetadataColumnDTO> metadataColumnList = new ArrayList<>();
        if (null != field) {
            metadataColumnList = JSONArray.parseArray(field.toString(), MetadataColumnDTO.class);
        }
        //datajson转换对象
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        EngineSourceDorisModel dorisModel = new EngineSourceDorisModel();
        String ip = JdbcUrlUtils.getIp(databaseConnectionDTO.getUrl());
        int port = JdbcUrlUtils.getPort(databaseConnectionDTO.getUrl());
        if (StringUtils.isNotBlank(databaseConnectionDTO.getFeNodes())) {
            dorisModel.setFenodes(databaseConnectionDTO.getFeNodes());
        } else {
            //数据源中没有feNodes时，ip给jdbc里的ip，端口给默认端口
            dorisModel.setFenodes(ip + ":8030");
        }
        dorisModel.setUsername(databaseConnectionDTO.getUserName());
        dorisModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        String dbName1 = dorisSource.getDbName();
        if (StringUtils.isNotEmpty(dbName1)) {
            dorisModel.setDatabase(dbName1);
        } else {
            String dbName = datasourceDTO.getDbName();
            dorisModel.setDatabase(dbName);
        }
        dorisModel.setTable(dorisSource.getTableName());

        dorisModel.setDorisReadField(dorisSource.getDorisReadField());
        dorisModel.setQueryPort(Integer.toString(port));
        String whereCondition = " 1 = 1 ";

        //TODO 预处理 doris没有WhereCondition
        List<RowFilterDTO> rowFilterDTOS = dorisSource.getWhereCondition();
        StringBuilder dorisFilterQuery = new StringBuilder();
        //拼接行过滤
        if (!CollectionUtils.isEmpty(rowFilterDTOS)) {
            for (int i = 0; i < rowFilterDTOS.size(); i++) {
                RowFilterDTO rowFilterDTO = rowFilterDTOS.get(i);
                String fieldName = rowFilterDTO.getFieldName();
                String value = rowFilterDTO.getValue();
                String condition = rowFilterDTO.getCondition();
                String mysqlConditionType = MysqlUtil.getMysqlConditionType(condition);
                dorisFilterQuery.append(fieldName);
                dorisFilterQuery.append(mysqlConditionType);
                dorisFilterQuery.append(value);
                if (i != rowFilterDTOS.size() - 1) {
                    dorisFilterQuery.append(" and ");
                }
            }

        }
        dorisModel.setDorisFilterQuery(dorisFilterQuery.toString());
        /*//拼接用户自定义where
        if (StringUtils.isNotEmpty(dorisSource.getUserDefineWhere())) {
            String defineWhere = dorisSource.getUserDefineWhere().trim();
            String _and = defineWhere.substring(3);
            if ("and".equalsIgnoreCase(_and)) {
                whereCondition = whereCondition + " " + defineWhere;
            } else {
                whereCondition = whereCondition + " and " + defineWhere;
            }
        }*/

        //拼接自定义条件sql
        String customWhereSql = dorisSource.getCustomWhereSql();
        if (StringUtils.isNotBlank(customWhereSql)) {
            whereCondition = whereCondition + " AND " + customWhereSql;
        }
        if (StringUtils.isNotEmpty(dorisSource.getDorisBatchSize())) {
            dorisModel.setDorisBatchSize(dorisSource.getDorisBatchSize());
        }
        if (StringUtils.isNotEmpty(dorisSource.getDorisRequestQueryTimeouts())) {
            dorisModel.setDorisRequestQueryTimeouts(dorisSource.getDorisRequestQueryTimeouts());
        }
        if (0 != dorisSource.getDorisExecMemLimit()) {
            dorisModel.setDorisExecMemLimit(dorisSource.getDorisExecMemLimit());
        }
        if (0 != dorisSource.getDorisRequestRetries()) {
            dorisModel.setDorisRequestRetries(dorisSource.getDorisRequestRetries());
        }
        if (0 != dorisSource.getDorisRequestReadTimeoutMs()) {
            dorisModel.setDorisRequestReadTimeoutMs(dorisSource.getDorisRequestReadTimeoutMs());
        }
        if (0 != dorisSource.getDorisRequestConnectTimeoutMs()) {
            dorisModel.setDorisRequestConnectTimeoutMs(dorisSource.getDorisRequestConnectTimeoutMs());
        }

        //数据源类型
        String dataType = datasourceDTO.getDataType();
        DatabaseTypeEnum databaseTypeEnum = DatabaseTypeEnum.getDatabaseTypeEnum(dataType.toLowerCase());
        //获取query语句
        DatabaseHandler handler = DatabaseFactory.getHandler(databaseTypeEnum);


        String incrField = dorisSource.getIncrField();
        String min = "0";
        String max = "0";
        if (StringUtils.isNotBlank(incrField)) {
            //最大值
            //增量字段
            String engineSavePointKey = ENGINE_SAVEPOINT + processDefineCode + ":" + taskCode + ":" + metadataTable.getTableName();
            String redisMin = redisTemplate.opsForValue().get(engineSavePointKey);
            logger.info(" 从redis获取最小值：", engineSavePointKey + ":" + redisMin);
            min = StringUtils.defaultString(redisMin, "0");
            //@DESC: 如果没有采集过表元数据信息，这里tableId是空的，就不能获取元数据信息,无法从redis中获取字段信息
            Optional<MetadataColumnDTO> incrFieldCol = metadataColumnList.stream().filter(t -> t.getColumnName().equals(incrField)).findFirst();
            if (incrFieldCol.isPresent()) {
                MetadataColumnDTO dto = incrFieldCol.get();
                String maxSql = handler.getMaxRecordValueSql(databaseConnectionDTO.getSchemaName(), metadataTable.getTableName(), incrField, dto.getColumnType(), null);
                max = handler.getMaxRecordValueForExecuteSql(datasourceInfoId, maxSql, logger, min, tenantCode);
                max = StringUtils.defaultString(max, "0");
            }
            logger.info("最小值是{}", incrField + ":" + min);
            logger.info("最大值是{}", incrField + ":" + max);
            whereCondition = whereCondition + EngineInitUtils.initIncrWhereCondition(databaseTypeEnum, incrField, min, max, "");
        }
        if (StringUtils.isNotEmpty(whereCondition)) {
            String dorisFilterQuery1 = dorisModel.getDorisFilterQuery();
            if (StringUtils.isNotBlank(dorisFilterQuery1)) {
                dorisModel.setDorisFilterQuery(whereCondition + " and " + dorisFilterQuery1);
            } else {
                dorisModel.setDorisFilterQuery(whereCondition);
            }
        }
        dorisModel.setResultTableName(StringUtils.isBlank(source.getResultTableName()) ? dorisSource.getResultTableName() : source.getResultTableName());
        //设置并行度
        String parallelism = dorisSource.getParallelism();
        dorisModel.setParallelism(parallelism == null ? 1 : Integer.parseInt(parallelism));
        return new Tuple2<>(new SavePointDTO(metadataTable.getTableName(), max, min), dorisModel);
    }

    private static String getNewQuery(String query) {
        String[] lines = query.split("\\r?\\n");
        List<String> list = new ArrayList<>();
        for (String line : lines) {
            if (line.contains("--") && !line.contains("'--'")) {
                int i = line.indexOf("--");
                line = line.substring(0, i);
            }
            line = line.replaceAll("\t", " ");
            list.add(line);
        }
        //2024-05-10 因为是要渲染模板seatunnel得再接受，转译双引号
        return list.stream()
                .map(Object::toString)
                .collect(Collectors.joining(" ")).replace("\"", "\\\"");
    }

    /**
     * 组装EngineSourcePostgresCDCModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceElasticsearchModel initElasticsearchSources(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceElasticsearchDTO elasticsearchSource = source.getElasticsearchSource();
        String datasourceInfoId = elasticsearchSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourceElasticsearchModel engineSourceElasticsearchModel = new EngineSourceElasticsearchModel();
        engineSourceElasticsearchModel.setHosts(databaseConnectionDTO.getUrl());
        engineSourceElasticsearchModel.setUsername(databaseConnectionDTO.getUserName());
        engineSourceElasticsearchModel.setPassword(EngineUtils.transformPassword(databaseConnectionDTO.getPassword()));
        if (StringUtils.isBlank(source.getElasticsearchSource().getIndex())) {
            throw new RuntimeException("index不可为空！");
        }
        engineSourceElasticsearchModel.setIndex(source.getElasticsearchSource().getIndex().replaceAll("<_doc>", ""));
        engineSourceElasticsearchModel.setSource(source.getElasticsearchSource().getSource());
        if (StringUtils.isNotBlank(source.getElasticsearchSource().getQuery())) {
            String newQuery = getNewQuery(source.getElasticsearchSource().getQuery());
            engineSourceElasticsearchModel.setQuery(newQuery);
        }
        engineSourceElasticsearchModel.setScrollTime(source.getElasticsearchSource().getScrollTime());
        engineSourceElasticsearchModel.setScrollSize(source.getElasticsearchSource().getScrollSize() > 0 ? source.getElasticsearchSource().getScrollSize() : 100);
        engineSourceElasticsearchModel.setSchema(source.getElasticsearchSource().getSchema());
        engineSourceElasticsearchModel.setResultTableName(source.getResultTableName());
        engineSourceElasticsearchModel.setParallelism(source.getElasticsearchSource().getParallelism());

        return engineSourceElasticsearchModel;
    }

    /**
     * 组装datahub source 参数
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceDataHubModel initDataHubSources(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceDataHubDTO dataHubSource = source.getDataHubSource();
        String datasourceInfoId = dataHubSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourceDataHubModel engineSourceDataHubModel = new EngineSourceDataHubModel();
        engineSourceDataHubModel.setEndpoint(databaseConnectionDTO.getEndpoint());
        engineSourceDataHubModel.setAccessKey(databaseConnectionDTO.getAccessKey());
        engineSourceDataHubModel.setAccessId(databaseConnectionDTO.getAccessId());
        engineSourceDataHubModel.setProject(databaseConnectionDTO.getProject());
        engineSourceDataHubModel.setCustomTimestamp(dataHubSource.getCustomTimestamp());
        engineSourceDataHubModel.setPartitionMillis(dataHubSource.getPartitionMillis());
        engineSourceDataHubModel.setSchema(dataHubSource.getSchema());
        engineSourceDataHubModel.setTopic(dataHubSource.getTopic());
        engineSourceDataHubModel.setCursorMode(dataHubSource.getCursorMode());
        if (StringUtils.isNotBlank(dataHubSource.getEndTimestampMillis())) {

            engineSourceDataHubModel.setEndTimestampMillis(dataHubSource.getEndTimestampMillis());
        }
        engineSourceDataHubModel.setResultTableName(source.getResultTableName());
        engineSourceDataHubModel.setSubId(dataHubSource.getSubId());
        return engineSourceDataHubModel;
    }

    private static EngineSourceAdbGpdistModel initAdbGpdistSources(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceAdbGpdistDTO adbGpdistSource = source.getAdbGpdistSource();
        String datasourceInfoId = adbGpdistSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        //组装对象
        EngineSourceAdbGpdistModel engineAdbGpdistSourceModel = new EngineSourceAdbGpdistModel();
        engineAdbGpdistSourceModel.setAdbUrl(databaseConnectionDTO.getUrl());
        engineAdbGpdistSourceModel.setAdbDriver(DatabaseTypeEnum.ADB_GPDIST.getDriver());
        engineAdbGpdistSourceModel.setAdbUser(databaseConnectionDTO.getUserName());
        engineAdbGpdistSourceModel.setAdbPassword(databaseConnectionDTO.getPassword());
        engineAdbGpdistSourceModel.setAdbDatabase(adbGpdistSource.getAdbDatabase());
        engineAdbGpdistSourceModel.setAdbTable(adbGpdistSource.getAdbTable());
        engineAdbGpdistSourceModel.setAdbSchema(adbGpdistSource.getAdbSchema());
        engineAdbGpdistSourceModel.setAdbPrefix(adbGpdistSource.getAdbPrefix());
        engineAdbGpdistSourceModel.setAdbGpfdistDddress(adbGpdistSource.getAdbGpfdistDddress());
        engineAdbGpdistSourceModel.setPath(adbGpdistSource.getPath());
        engineAdbGpdistSourceModel.setResultTableName(source.getResultTableName());
        engineAdbGpdistSourceModel.setFileFormatType(adbGpdistSource.getFileFormatType());
        engineAdbGpdistSourceModel.setFieldDelimiter(adbGpdistSource.getFieldDelimiter());
        engineAdbGpdistSourceModel.setAdbTmpFilePath(adbGpdistSource.getAdbTmpFilePath());
        engineAdbGpdistSourceModel.setAdbExternalTableName(adbGpdistSource.getAdbExternalTableName());
        engineAdbGpdistSourceModel.setAdbExternalTableSchema(adbGpdistSource.getAdbExternalTableSchema());
        engineAdbGpdistSourceModel.setAdbGpfdistPath(adbGpdistSource.getAdbGpfdistPath());
        engineAdbGpdistSourceModel.setAdbExternalTableDelimiter(adbGpdistSource.getAdbExternalTableDelimiter());
        engineAdbGpdistSourceModel.setSchemaName(adbGpdistSource.getSchemaName());

        String adbWhereSql = StringUtils.EMPTY;
        List<RowFilterDTO> whereCondition = adbGpdistSource.getWhereCondition();
        //拼接行过滤
        if (!CollectionUtils.isEmpty(whereCondition)) {
            adbWhereSql = EngineUtils.getNewWhereCondition(adbWhereSql, DatabaseTypeEnum.ADB_GPDIST.getName(), whereCondition);
        }

        //拼接自定义条件sql
        String customWhereSql = adbGpdistSource.getCustomWhereSql();
        if (StringUtils.isNotBlank(customWhereSql)) {
            adbWhereSql = adbWhereSql + " AND " + customWhereSql;
        }
        engineAdbGpdistSourceModel.setAdbWhereSql(adbWhereSql);
        return engineAdbGpdistSourceModel;
    }

    private static EngineSourceArgoHdfsFileModel initArgoHdfsFileSource(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceArgoHdfsFileDTO sourceArgoHdfsFileDTO = source.getArgoHdfsFileSource();
        String datasourceInfoId = sourceArgoHdfsFileDTO.getDatasourceInfoId();

        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        //数据源信息转对象
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        EngineSourceArgoHdfsFileModel model = new EngineSourceArgoHdfsFileModel();
        String defaultFS = sourceArgoHdfsFileDTO.getDefaultFS();
        if (StringUtils.isBlank(defaultFS)) {
            model.setDefaultFS(databaseConnectionDTO.getDefaultFS());
        } else {
            model.setDefaultFS(defaultFS);
        }
        model.setPath(sourceArgoHdfsFileDTO.getPath());
        model.setArgoUrl(databaseConnectionDTO.getUrl());
        model.setArgoUser(databaseConnectionDTO.getUserName());
        model.setArgoPassword(databaseConnectionDTO.getPassword());
        model.setArgoSchema(databaseConnectionDTO.getSchemaName());
        model.setArgoTable(sourceArgoHdfsFileDTO.getArgoTable());
        model.setArgoTmpTableName(sourceArgoHdfsFileDTO.getArgoTmpTableName());
        model.setArgoTmpFilePath(sourceArgoHdfsFileDTO.getArgoTmpFilePath());
        model.setArgoSchemas(sourceArgoHdfsFileDTO.getArgoSchemas());
        model.setKrb5Path(sourceArgoHdfsFileDTO.getKrb5Path());
        model.setKerberosKeytabPath(sourceArgoHdfsFileDTO.getKerberosKeytabPath());
        model.setKerberosPrincipal(sourceArgoHdfsFileDTO.getKerberosPrincipal());
        model.setHdfsSitePath(sourceArgoHdfsFileDTO.getHdfsSitePath());
        model.setEmptyDataStrategy(sourceArgoHdfsFileDTO.getEmptyDataStrategy());
        model.setResultTableName(source.getResultTableName());
        model.setFileFormatType(sourceArgoHdfsFileDTO.getFileFormatType());
        model.setRowDelimiter(sourceArgoHdfsFileDTO.getRowDelimiter());
        model.setFieldDelimiter(sourceArgoHdfsFileDTO.getFieldDelimiter());
        model.setArgoTmpTableName(sourceArgoHdfsFileDTO.getArgoTmpTableName());
        model.setArgoTmpSchema(sourceArgoHdfsFileDTO.getArgoTmpSchema());
        return model;
    }

    /**
     * 组装EngineSourceMongoDBModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceMongoDBModel initMongoDBSources(EngineSourceDTO source,
                                                               StringRedisTemplate redisTemplate, String tenantCode,
                                                               EngineSinkDTO sink,
                                                               Logger logger) {
        EngineSourceMongoDBDTO mongodbSource = source.getMongodbSource();
        String datasourceInfoId = mongodbSource.getDatasourceInfoId();
        String tableId = mongodbSource.getTableId();
        //目标端连接信息
        String datasourceStr = EngineUtils.getDatasourceStr(logger, redisTemplate, tenantCode, datasourceInfoId, "源端");
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + tableId, "table");
        if (StringUtils.isBlank(datasourceStr) || null == table) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }

        //获取连接信息
        MetadataTableDTO metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);
        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);

        List<MetadataColumnDTO> metadataColumnList = new ArrayList<>();
        switch (sink.getType()) {
            case JDBC:
                metadataColumnList = sink.getJdbcSink().getFieldList();
                break;
            case CLICKHOUSE:
                metadataColumnList = sink.getClickHouseSink().getFieldList();
                break;
            case MONGODB:
                metadataColumnList = sink.getMongodbSink().getFieldList();
                break;
            case KUDU:
                metadataColumnList = sink.getKuduSink().getFieldList();
                break;
        }
        List<MetadataColumnDTO> columnDTOList = metadataColumnList.stream().sorted(Comparator.comparingLong(MetadataColumnDTO::getColumnSort)).collect(Collectors.toList());
//        LinkedHashMap<String, String> schema = (LinkedHashMap) columnDTOList.stream().collect(Collectors.toMap(MetadataColumnDTO::getColumnName, t -> SQLFactory.getMongoDbStTypeFromColumnType(t.getColumnType())));
        LinkedHashMap<String, String> schema = new LinkedHashMap<>();
        columnDTOList.forEach(columnDTO -> schema.put(columnDTO.getColumnName(), SQLFactory.getMongoDbStTypeFromColumnType(columnDTO.getColumnType().toLowerCase())));
        //组装对象
        EngineSourceMongoDBModel engineSourceMongoDBModel = new EngineSourceMongoDBModel();
        engineSourceMongoDBModel.setResultTableName(source.getResultTableName());
        engineSourceMongoDBModel.setUri(databaseConnectionDTO.getUrl());
        engineSourceMongoDBModel.setDatabase(databaseConnectionDTO.getSchemaName());
        engineSourceMongoDBModel.setCollection(metadataTable.getTableName());
        String query = mongodbSource.getQuery();
        if (StringUtils.isNotBlank(query)) {
            String newQuery = getNewQuery(mongodbSource.getQuery());
            engineSourceMongoDBModel.setQuery(newQuery);
        }
        engineSourceMongoDBModel.setSchema(schema);
        engineSourceMongoDBModel.setProjection(mongodbSource.getProjection());
        engineSourceMongoDBModel.setPartitionSplitKey(mongodbSource.getPartitionSplitKey());
        engineSourceMongoDBModel.setCursorNoTimeout(mongodbSource.getCursorNoTimeout());
        engineSourceMongoDBModel.setMaxTimeMin(mongodbSource.getMaxTimeMin());
        engineSourceMongoDBModel.setFetchSize(mongodbSource.getFetchSize());
        engineSourceMongoDBModel.setFlatSyncString(mongodbSource.getFlatSyncString());

        return engineSourceMongoDBModel;
    }

    /**
     * 组装EngineSourceMongoDBModel
     *
     * @param source
     * @param redisTemplate
     * @param tenantCode
     * @return
     */
    private static EngineSourceKuduModel initKuduSources(EngineSourceDTO source, StringRedisTemplate redisTemplate, String tenantCode) {
        EngineSourceKuduDTO kuduSource = source.getKuduSource();
        String datasourceInfoId = kuduSource.getDatasourceInfoId();

        //目标端连接信息
        String datasourceStr = redisTemplate.opsForValue().get(Constants.ENGINE_DATA_SOURCE_KEY + tenantCode + ":" + datasourceInfoId);
        Object table = redisTemplate.opsForHash().get(Constants.ENGINE_METADATA_KEY + tenantCode + ":" + kuduSource.getTableId(), "table");
        if (StringUtils.isBlank(datasourceStr)) {
            log.error("数据源信息不能为空！！！");
            throw new RuntimeException("数据源信息不能为空！！！");
        }
        DatasourceDTO datasourceDTO = EngineUtils.getDatasourceDTO(datasourceStr, datasourceInfoId);

        DatabaseConnectionDTO databaseConnectionDTO = EngineInitUtils.initDatabaseConnection(datasourceDTO);
        MetadataTableDTO metadataTable = JSONObject.parseObject(table.toString(), MetadataTableDTO.class);


        //组装对象
        EngineSourceKuduModel engineSourceKuduModel = new EngineSourceKuduModel();
        engineSourceKuduModel.setKuduMasters(databaseConnectionDTO.getUrl());
        engineSourceKuduModel.setTableName(metadataTable.getTableName());
        engineSourceKuduModel.setClientWorkerCount(kuduSource.getClientWorkerCount());
        engineSourceKuduModel.setClientDefaultAdminOperationTimeoutMs(kuduSource.getClientDefaultAdminOperationTimeoutMs());
        engineSourceKuduModel.setClientDefaultOperationTimeoutMs(kuduSource.getClientDefaultOperationTimeoutMs());
        engineSourceKuduModel.setEnableKerberos(kuduSource.getEnableKerberos());
        engineSourceKuduModel.setKerberosPrincipal(kuduSource.getKerberosPrincipal());
        engineSourceKuduModel.setKerberosKeytab(kuduSource.getKerberosKeytab());
        engineSourceKuduModel.setKerberosKrb5conf(kuduSource.getKerberosKrb5conf());
        engineSourceKuduModel.setScanTokenQueryTimeout(kuduSource.getScanTokenQueryTimeout());
        engineSourceKuduModel.setScanTokenBatchSizeBytes(kuduSource.getScanTokenBatchSizeBytes());
        engineSourceKuduModel.setSchema(kuduSource.getSchema());
        engineSourceKuduModel.setTableList(kuduSource.getTableList());
        engineSourceKuduModel.setResultTableName(source.getResultTableName());

        return engineSourceKuduModel;
    }

}
