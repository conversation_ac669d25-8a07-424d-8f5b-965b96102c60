/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.plugin.task.shell;

import org.apache.commons.lang3.SystemUtils;
import org.apache.dolphinscheduler.plugin.task.api.*;
import org.apache.dolphinscheduler.plugin.task.api.model.Property;
import org.apache.dolphinscheduler.plugin.task.api.model.TaskResponse;
import org.apache.dolphinscheduler.plugin.task.api.parameters.AbstractParameters;
import org.apache.dolphinscheduler.plugin.task.api.parser.ParamUtils;
import org.apache.dolphinscheduler.plugin.task.api.parser.ParameterUtils;
import org.apache.dolphinscheduler.spi.utils.JSONUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

import javax.sql.DataSource;
import java.io.File;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.FileAttribute;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Map;
import java.util.Set;

import static org.apache.dolphinscheduler.plugin.task.api.TaskConstants.EXIT_CODE_FAILURE;
import static org.apache.dolphinscheduler.plugin.task.api.TaskConstants.RWXR_XR_X;

/**
 * shell task
 */
public class ShellTask extends AbstractTaskExecutor {

    /**
     * shell parameters
     */
    private ShellParameters shellParameters;

    /**
     * shell command executor
     */
    private ShellCommandExecutor shellCommandExecutor;

    /**
     * taskExecutionContext
     */
    private TaskExecutionContext taskExecutionContext;

    private StringRedisTemplate globalRedisTemplate;

    /**
     * constructor
     *
     * @param taskExecutionContext taskExecutionContext
     */
    public ShellTask(TaskExecutionContext taskExecutionContext) {
        super(taskExecutionContext);

        this.taskExecutionContext = taskExecutionContext;
        this.shellCommandExecutor = new ShellCommandExecutor(this::logHandle,
                taskExecutionContext,
                logger);
    }

    @Override
    public void init() {
        logger.info("shell task params {}", taskExecutionContext.getTaskParams());

        shellParameters = JSONUtils.parseObject(taskExecutionContext.getTaskParams(), ShellParameters.class);

        if (!shellParameters.checkParameters()) {
            throw new RuntimeException("shell task params is not valid");
        }
    }

    @Override
    public void initCustomParam(StringRedisTemplate redisTemplate, String engineHome, String stxHome, String gatewayHttp, DataSource dataSource, RestTemplate restTemplate, String datasourcexHome) {
        globalRedisTemplate = redisTemplate;
    }

    /**
     * shell 任务执行入口
     * @throws TaskException
     */
    @Override
    public void handle() throws TaskException {
        try {
            // construct process
            String command = buildCommand();
            TaskResponse commandExecuteResult = shellCommandExecutor.run(command,globalRedisTemplate,null);
            setExitStatusCode(commandExecuteResult.getExitStatusCode());
            setAppIds(String.join(TaskConstants.COMMA, getApplicationIds()));
            setProcessId(commandExecuteResult.getProcessId());
            //lihj处理输入输出参数问题
            shellParameters.dealOutParam(shellCommandExecutor.getVarPool());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("The current Shell task has been interrupted", e);
            setExitStatusCode(EXIT_CODE_FAILURE);
            throw new TaskException("The current Shell task has been interrupted", e);
        } catch (Exception e) {
            logger.error("shell task error", e);
            setExitStatusCode(EXIT_CODE_FAILURE);
            throw new TaskException("Execute shell task error", e);
        }
    }

    @Override
    public void cancelApplication(boolean cancelApplication) throws Exception {
        // cancel process
        shellCommandExecutor.cancelApplication();
    }

    /**
     * create command
     *
     * @return file name
     * @throws Exception exception
     */
    private String buildCommand() throws Exception {
        // generate scripts
        String fileName = String.format("%s/%s_node.%s",
                taskExecutionContext.getExecutePath(),
                taskExecutionContext.getTaskAppId(), SystemUtils.IS_OS_WINDOWS ? "bat" : "sh");

        File file = new File(fileName);
        Path path = file.toPath();

        if (Files.exists(path)) {
            return fileName;
        }

        String script = shellParameters.getRawScript().replaceAll("\\r\\n", "\n");
        script = parseScript(script);//上下文值传递变量值替换 lihj
        script = ParameterUtils.convertParameterPlaceholdersForSpecial(script,taskExecutionContext.getFileParamsPath());
        shellParameters.setRawScript(script);

        logger.info("raw script : {}", shellParameters.getRawScript());
        logger.info("task execute path : {}", taskExecutionContext.getExecutePath());

        // 使用安全的文件创建方法，避免 "文本文件忙" 问题
        createNodeScriptFileSafely(fileName, shellParameters.getRawScript());

        return fileName;
    }

    @Override
    public AbstractParameters getParameters() {
        return shellParameters;
    }

    /**
     * 安全地创建节点脚本文件，彻底避免 "文本文件忙" 问题
     *
     * @param fileName 文件路径
     * @param content 文件内容
     * @throws Exception 异常
     */
    private void createNodeScriptFileSafely(String fileName, String content) throws Exception {
        Path targetPath = Paths.get(fileName);
        Path tempPath = Paths.get(fileName + ".tmp." + System.currentTimeMillis());

        try {
            logger.info("开始安全创建节点脚本文件: {}", fileName);

            // 确保父目录存在
            Path parentDir = targetPath.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }

            // 步骤1: 写入临时文件
            Files.write(tempPath, content.getBytes(StandardCharsets.UTF_8),
                       StandardOpenOption.CREATE,
                       StandardOpenOption.WRITE,
                       StandardOpenOption.TRUNCATE_EXISTING);

            // 步骤2: 设置权限
            if (!SystemUtils.IS_OS_WINDOWS) {
                Set<PosixFilePermission> perms = PosixFilePermissions.fromString(RWXR_XR_X);
                Files.setPosixFilePermissions(tempPath, perms);
            }

            // 步骤3: 强制同步到磁盘
            try (FileChannel channel = FileChannel.open(tempPath, StandardOpenOption.WRITE)) {
                channel.force(true);
            }

            // 步骤4: 原子性移动到目标位置
            Files.move(tempPath, targetPath,
                      StandardCopyOption.REPLACE_EXISTING,
                      StandardCopyOption.ATOMIC_MOVE);

            logger.info("节点脚本文件创建成功: {}", fileName);

            // 步骤5: 验证文件状态
            if (!Files.exists(targetPath)) {
                throw new Exception("节点脚本文件创建失败，文件不存在: " + fileName);
            }

            if (!Files.isExecutable(targetPath)) {
                logger.warn("节点脚本文件不可执行，尝试重新设置权限: {}", fileName);
                if (!SystemUtils.IS_OS_WINDOWS) {
                    Set<PosixFilePermission> perms = PosixFilePermissions.fromString(RWXR_XR_X);
                    Files.setPosixFilePermissions(targetPath, perms);
                }
            }

        } catch (Exception e) {
            // 清理临时文件
            try {
                Files.deleteIfExists(tempPath);
            } catch (Exception cleanupException) {
                logger.warn("清理临时文件失败: {}", tempPath, cleanupException);
            }
            throw new Exception("创建节点脚本文件失败: " + fileName, e);
        }
    }

    private String parseScript(String script) {
        // combining local and global parameters
        Map<String, Property> paramsMap = taskExecutionContext.getPrepareParamsMap();
        //开始 20240902处理全局参数覆盖任务参数
        String globalParams = taskExecutionContext.getGlobalParams();
        ParameterUtils.convertGlobalParams(paramsMap,globalParams);
        //结束
        return ParameterUtils.convertParameterPlaceholders(script, ParamUtils.convert(paramsMap));
    }
}
