[{"description": "trigger task execute", "request": {"uri": "/tjs/coredefine/coredefine.ajax", "method": "post", "headers": {"Content-Type": "application/x-www-form-urlencoded", "appname": "mysql_elastic"}, "text": "action=datax_action&emethod=trigger_fullbuild_task"}, "response": {"text": "{\n \"success\":true,\n \"errormsg\":[],\n \"msg\":[],\n \"bizresult\":{\"taskid\": \"1087\"}\n}"}}, {"description": "Get task execute status", "request": {"uri": "/tjs/config/config.ajax", "method": "post", "headers": {"Content-Type": "text/plain; charset=UTF-8"}, "queries": {"action": "collection_action", "emethod": "get_task_status"}, "text": "{\n taskid: 1087\n, log: false }"}, "response": {"seq": [{"text": "{\n  \"success\": true,\n  \"errormsg\": [\n    \"err1\"\n  ],\n  \"bizresult\": {\n    \"status\": {\n      \"state\": 2\n    }\n  }\n}"}, {"text": "{\n  \"success\": true,\n  \"errormsg\": [\n    \"err1\"\n  ],\n  \"bizresult\": {\n    \"status\": {\n      \"state\": 1\n    }\n  }\n}"}]}}, {"websocket": {"uri": "/tjs/download/logfeedback", "connected": "connected", "sessions": [{"request": {"text": "logtype=full&collection=mysql_elastic&taskid=1087"}, "response": {"broadcast": {"content": "{\n  \"logType\": \"FULL\",\n  \"msg\": \"message 1\",\n  \"taskId\": \"1087\"\n}"}}}]}}]