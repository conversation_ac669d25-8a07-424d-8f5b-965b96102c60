#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

job.trigger.url=http://%s/tjs/coredefine/coredefine.ajax
job.trigger.post.body=action=datax_action&emethod=trigger_fullbuild_task

job.cancel.post.body=action=core_action&event_submit_do_cancel_task=y&taskid=%s

job.status.url=http://%s/tjs/config/config.ajax?action=collection_action&emethod=get_task_status
job.status.post.body={\n taskid: %s\n, log: false }

job.logs.fetch.url=ws://%s/tjs/download/logfeedback?logtype=full&collection=%s&taskid=%s