env {
"job.mode"="${engine.env.jobModel}"
"job.name"="${engine.env.executionParallelism}"
<#--"execution.parallelism"="${engine.env.executionParallelism}"-->
<#--"checkpoint.interval"="1000"-->
}
source {
<#-- source jdbc -->
<#if engine.jdbcSources?? && engine.jdbcSources?size != 0>
    <#list engine.jdbcSources as source>
        Jdbc {
        url="${source.url}"
        driver="${source.driver}"
        <#if source.schemaName??>
            schema="${source.schemaName}"
        </#if>
        <#if source.user??>
            user="${source.user}"
        <#else>
        </#if>
        <#if source.password??>
            password="${source.password}"
        <#else>
        </#if>
        query="${source.query}"
        "fetch_size"="${source.batchSize}"
        <#if source.resultTableName?? >
            "result_table_name"="${source.resultTableName}"
        <#else>
        </#if>
        <#if source.partitionColumn??>
            "partition_column"="${source.partitionColumn}"
        <#else>
        </#if>
        <#if source.partitionNum??>
            "partition_num"="${source.partitionNum}"
        <#else>
        </#if>
        <#if source.parallelism??>
            "parallelism"=${source.parallelism}
        <#else>
        </#if>
        <#if source.compatibleMode??>
            "compatible_mode"=${source.compatibleMode}
        <#else>
        </#if>
        <#if source.useKerberos??>
            "use_kerberos"=${source.useKerberos}
        <#else>
        </#if>
        <#if source.kerberosPrincipal??>
            "kerberos_principal"="${source.kerberosPrincipal}"
        <#else>
        </#if>
        <#if source.kerberosKeytabPath??>
            "kerberos_keytab_path"=${source.kerberosKeytabPath}
        <#else>
        </#if>
        <#if source.krb5Path??>
            "krb5_path"=${source.krb5Path}
        <#else>
        </#if>
        <#if source.emptyDataStrategy??>
            "empty_data_strategy"=${source.emptyDataStrategy}
        <#else>
        </#if>
        <#if source.pipeline??>
            "table_path"=${source.pipeline}
        </#if>
        <#if source.extractType=="copy">
            use_copy_source="true"
            copy_format = "CSV"
            use_direct_copy = false
            pipe_size = 10485760
            copy_buffer_size = 8192
        </#if>
        }
    </#list>
</#if>
<#-- source oracle -->
<#if engine.oracleSources?? && engine.oracleSources?size != 0>
    <#list engine.oracleSources as config>
        Oracle-CDC {
        <#if config.resultTableName??>
            result_table_name = "${config.resultTableName}"
        </#if>
        <#if config.username??>
            username = "${config.username}"
        </#if>
        <#if config.password??>
            password = "${config.password}"
        </#if>
        <#if config.databaseNames??>
            database-names = ["${config.databaseNames}"]
        </#if>
        <#if config.schemaName??>
            schema-name = ["${config.schemaName}"]
        </#if>
        <#if config.tableNames??>
            table-names = ["${config.tableNames}"]
        </#if>
        <#if config.baseUrl??>
            base-url = "${config.baseUrl}"
        </#if>
        <#if config.sourceReaderCloseTimeout??>
            source.reader.close.timeout = ${config.sourceReaderCloseTimeout}
        </#if>
        <#if config.startupMode??>
            startup.mode = "${config.startupMode}"
        </#if>
        <#if config.debezium??>
            debezium {
            <#list config.debezium as key, value>
                ${key} = "${value}"
            </#list>
            }
        </#if>
        }
    </#list>
</#if>
<#-- source mysql -->
<#if engine.mysqlSources?? && engine.mysqlSources?size != 0>
    <#list engine.mysqlSources as config>
        MySQL-CDC {
        <#if config.resultTableName??>
            result_table_name = "${config.resultTableName}"
        </#if>
        <#if config.serverId??>
            server-id = ${config.serverId}
        </#if>
        <#if config.username??>
            username = "${config.username}"
        </#if>
        <#if config.password??>
            password = "${config.password}"
        </#if>
        <#if config.tableNames??>
            table-names = ["${config.tableNames}"]
        </#if>
        <#if config.baseUrl??>
            base-url = "${config.baseUrl}"
        </#if>
        <#if config.startupMode??>
            startup.mode = "${config.startupMode}"
        </#if>
        <#if config.serverTimeZone??>
            server-time-zone = "${config.serverTimeZone}"
        </#if>
        <#if config.catalog??>
            catalog {
            <#list config.catalog as key, value>
                ${key} = ${value}
            </#list>
            }
        </#if>
        }
    </#list>
</#if>
<#-- source sqlserver -->
<#if engine.sqlserverSources?? && engine.sqlserverSources?size != 0>
    <#list engine.sqlserverSources as source>
        SqlServer-CDC {
        <#if source.resultTableName??>
            result_table_name = "${source.resultTableName}"
        </#if>
        <#if source.username??>
            username = "${source.username}"
        </#if>
        <#if source.password??>
            password = "${source.password}"
        </#if>
        <#if source.databaseNames??>
            database-names = ["${source.databaseNames}"]
        </#if>
        <#if source.tableNames??>
            table-names = ["${source.tableNames}"]
        </#if>
        <#if source.baseUrl??>
            base-url = "${source.baseUrl}"
        </#if>
        }
    </#list>
</#if>
<#-- source postgresCdc -->
<#if engine.postgresSources?? && engine.postgresSources?size != 0>
    <#list engine.postgresSources as source>
        Postgres-CDC {
        <#if source.baseUrl??>
            base-url = "${source.baseUrl}"
        </#if>
        <#if source.username??>
            username = "${source.username}"
        </#if>
        <#if source.password??>
            password = "${source.password}"
        </#if>
        <#if source.databaseNames??>
            database-names = ["${source.databaseNames}"]
        </#if>
        <#if source.schemaNames??>
            schema-names = ["${source.schemaNames}"]
        </#if>
        <#if source.tableNames??>
            table-names = ["${source.tableNames}"]
        </#if>
        <#if source.resultTableName??>
            result_table_name = "${source.resultTableName}"
        </#if>
        <#if source.incrementalParallelism??>
            incremental.parallelism = ${source.incrementalParallelism}
        </#if>
        <#if source.debezium??>
            debezium {
            <#if source.debezium.logMiningStrategy??>
                log.mining.strategy = "${source.debezium.logMiningStrategy}"
            </#if>
            <#if source.debezium.logMiningContinuousMine??>
                log.mining.continuous.mine = "${source.debezium.logMiningContinuousMine}"
            </#if>
            <#if source.debezium.databaseOracleJdbcTimezoneAsRegion??>
                database.oracle.jdbc.timezoneAsRegion = "${source.debezium.databaseOracleJdbcTimezoneAsRegion}"
            </#if>
            }
        </#if>
        }
    </#list>
</#if>
<#-- source elasticsearch -->
<#if engine.elasticsearchSources?? && engine.elasticsearchSources?size != 0>
    <#list engine.elasticsearchSources as source>
        Elasticsearch {
    <#-- result_table_name -->
        <#if source.resultTableName??>
            result_table_name = "${source.resultTableName}"
        </#if>
    <#-- hosts -->
        <#if source.hosts??>
            hosts = ["${source.hosts}"]
        </#if>
    <#-- index -->
        <#if source.index??>
            index = "${source.index}"
        </#if>
    <#-- username -->
        <#if source.username??>
            username = "${source.username}"
        </#if>
    <#-- password -->
        <#if source.password??>
            password = "${source.password}"
        </#if>
    <#-- source -->
        <#assign index = 0>
        <#if source.source?? && source.source?size != 0>
            source = [
            <#list source.source as entry>
                <#if index != 0>,</#if>
                ${entry?trim}
                <#assign index = index+1>
            </#list>
            ]
        </#if>
    <#-- query -->
        <#if source.query??>
            query = "${source.query}"
        </#if>
    <#-- scrollTime -->
        <#if source.scrollTime??>
            scroll_time = "${source.scrollTime}"
        </#if>
    <#-- scrollSize -->
        <#if source.scrollSize??>
            scroll_size = "${source.scrollSize}"
        </#if>
    <#-- schema -->
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        }
    </#list>
</#if>
<#-- source MongoDB -->
<#if engine.mongodbSources?? && engine.mongodbSources?size != 0>
    <#list engine.mongodbSources as source>
        MongoDB {
        uri="${source.uri}"
        database="${source.database}"
        collection="${source.collection}"
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if source.query??>
            match.query="${source.query}"
        </#if>
        <#if source.projection??>
            match.projection="${source.projection}"
        </#if>
        <#if source.partitionSplitKey??>
            partition.split-key="${source.partitionSplitKey}"
        </#if>
        <#if source.partitionSplitSize??>
            partition.split-size="${source.partitionSplitSize}"
        </#if>
        <#if source.cursorNoTimeout??>
            cursor.no-timeout="${source.cursorNoTimeout}"
        </#if>
        <#if source.fetchSize??>
            fetch.size="${source.fetchSize}"
        </#if>
        <#if source.maxTimeMin??>
            max.time-min="${source.maxTimeMin}"
        </#if>
        <#if source.flatSyncString??>
            flat.sync-string="${source.flatSyncString}"
        </#if>
        <#if source.resultTableName??>
            result_table_name = "${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source kudu -->
<#if engine.kuduSources?? && engine.kuduSources?size != 0>
    <#list engine.kuduSources as source>
        kudu {
        kudu_masters="${source.kuduMasters}"
        table_name="${source.tableName}"
        <#if source.resultTableName??>
            result_table_name = "${source.resultTableName}"
        </#if>
        <#if source.clientWorkerCount != 0>
            client_worker_count="${source.clientWorkerCount}"
        </#if>
        <#if source.clientDefaultOperationTimeoutMs??>
            client_default_operation_timeout_ms="${source.clientDefaultOperationTimeoutMs}"
        </#if>
        <#if source.clientDefaultAdminOperationTimeoutMs??>
            client_default_admin_operation_timeout_ms="${source.clientDefaultAdminOperationTimeoutMs}"
        </#if>
        <#if source.enableKerberos??>
            enable_kerberos="${source.enableKerberos}"
        </#if>
        <#if source.kerberosPrincipal??>
            kerberos_principal="${source.kerberosPrincipal}"
        </#if>
        <#if source.kerberosKeytab??>
            kerberos_keytab="${source.kerberosKeytab}"
        </#if>
        <#if source.kerberosKrb5conf??>
            kerberos_krb5conf="${source.kerberosKrb5conf}"
        </#if>
        <#if source.scanTokenQueryTimeout??>
            scan_token_query_timeout="${source.scanTokenQueryTimeout}"
        </#if>
        <#if source.scanTokenBatchSizeBytes != 0>
            scan_token_batch_size_bytes="${source.scanTokenBatchSizeBytes}"
        </#if>
        <#if source.schema??>
            schema="${source.schema}"
        </#if>
        <#if source.tableList??>
            table_list="${source.tableList}"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#if engine.hdfsFileSources?? && engine.hdfsFileSources?size != 0>
    <#list engine.hdfsFileSources as config>
        HdfsFile {
        path = "${config.path}"
        file_format_type = "${config.fileFormatType}"
        fs.defaultFS = "${config.fsDefaultFS}"
        <#if config.readColumns??>
            read_columns = ${config.readColumns}
        </#if>
        <#if config.resultTableName??>
            result_table_name = "${config.resultTableName}"
        </#if>
        <#if config.hdfsSitePath?? >
            hdfs_site_path="${config.hdfsSitePath}"
        <#else>
        </#if>
        <#if config.fieldDelimiter?? >
            field_delimiter="${config.fieldDelimiter}"
        <#else>
        </#if>
        <#if config.parsePartitionFromPath?? >
            parse_partition_from_path="${config.parsePartitionFromPath}"
        <#else>
        </#if>
        <#if config.dateFormat?? >
            date_format="${config.dateFormat}"
        <#else>
        </#if>
        <#if config.datetimeFormat?? >
            datetime_format="${config.datetimeFormat}"
        <#else>
        </#if>
        <#if config.timeFormat?? >
            time_format="${config.timeFormat}"
        <#else>
        </#if>
        <#if config.remoteUser?? >
            remote_user="${config.remoteUser}"
        <#else>
        </#if>
        <#if config.krb5Path?? >
            krb5_path="${config.krb5Path}"
        <#else>
        </#if>
        <#if config.kerberosPrincipal?? >
            kerberos_principal="${config.kerberosPrincipal}"
        <#else>
        </#if>
        <#if config.kerberosKeytabPath?? >
            kerberos_keytab_path="${config.kerberosKeytabPath}"
        <#else>
        </#if>
        <#if config.skipHeaderRowNumber?? >
            skip_header_row_number=${config.skipHeaderRowNumber}
        <#else>
        </#if>
        <#if config.sheetName?? >
            sheet_name="${config.sheetName}"
        <#else>
        </#if>
        <#if config.compressCodec?? >
            compress_codec="${config.compressCodec}"
        <#else>
        </#if>
        <#if config.schema??>
            schema= {
            fields {
            <#list config.schema?keys as key>
                ${key}="${config.schema[key]}"
            </#list>
            }
            }
        </#if>
        }
    </#list>
</#if>
<#-- source localfile -->
<#if engine.localFileSources?? && engine.localFileSources?size != 0>
    <#list engine.localFileSources as source>
        LocalFile {
        path="${source.path}"
        <#if source.resultTableName??>
            result_table_name = "${source.resultTableName}"
        </#if>
        file_format_type="${source.fileFormatType}"
        <#if source.readColumns??>
            read_columns=${source.readColumns}
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.parsePartitionFromPath??>
            parse_partition_from_path=${source.parsePartitionFromPath}
        </#if>
        <#if source.dateFormat??>
            date_format="${source.dateFormat}"
        </#if>
        <#if source.datetimeFormat??>
            datetime_format="${source.datetimeFormat}"
        </#if>
        <#if source.timeFormat??>
            time_format="${source.timeFormat}"
        </#if>
        <#if source.skipHeaderRowNumber??>
            skip_header_row_number=${source.skipHeaderRowNumber}
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if source.sheetName??>
            sheet_name=${source.sheetName}
        </#if>
        <#if source.fileFilterPattern??>
            file_filter_pattern=${source.fileFilterPattern}
        </#if>
        <#if source.compressCodec??>
            compress_codec=${source.compressCodec}
        </#if>
        <#if source.encoding??>
            encoding=${source.encoding}
        </#if>
        }
    </#list>
</#if>
<#-- source localfile2file -->
<#if engine.localFile2FileSources?? && engine.localFile2FileSources?size != 0>
    <#list engine.localFile2FileSources as source>
        LocalFile {
        path="${source.path}"
        <#if source.fileFormatType??>
            file_format_type=${source.fileFormatType}
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source sftp -->
<#if engine.sftpSources?? && engine.sftpSources?size != 0>
    <#list engine.sftpSources as source>
        SftpFile {
        host="${source.host}"
        port="${source.port}"
        user="${source.user}"
        password="${source.password}"
        path="${source.path}"
        file_format_type="${source.fileFormatType}"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.fileFilterPattern??>
            file_filter_pattern="${source.fileFilterPattern}"
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.parsePartitionFromPath??>
            parse_partition_from_path="${source.parsePartitionFromPath}"
        </#if>
        <#if source.dateFormat??>
            date_format="${source.dateFormat}"
        </#if>
        <#if source.datetimeFormat??>
            datetime_format="${source.datetimeFormat}"
        </#if>
        <#if source.timeFormat??>
            time_format="${source.timeFormat}"
        </#if>
        <#if source.skipHeaderRowNumber??>
            skip_header_row_number=${source.skipHeaderRowNumber}
        </#if>
        <#if source.readColumns??>
            read_columns="${source.readColumns}"
        </#if>
        <#if source.sheetName??>
            sheet_name="${source.sheetName}"
        </#if>
        <#if source.compressCodec??>
            compress_codec="${source.compressCodec}"
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        }
    </#list>
</#if>
<#-- source sftpFile2File -->
<#if engine.sftpFile2FileSources?? && engine.sftpFile2FileSources?size != 0>
    <#list engine.sftpFile2FileSources as source>
        SftpFile {
        host="${source.host}"
        port="${source.port}"
        user="${source.user}"
        password="${source.password}"
        path="${source.path}"
        file_format_type="${source.fileFormatType}"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source inspurOSSFile2File -->
<#if engine.inspurOSSFile2FileSources?? && engine.inspurOSSFile2FileSources?size != 0>
    <#list engine.inspurOSSFile2FileSources as source>
        S3File {
        path="${source.path}"
        bucket="${source.bucket}"
        access_key="${source.accessKey}"
        secret_key="${source.secretKey}"
        fs.s3a.endpoint="${source.fsS3aEndpoint}"
        file_format_type="${source.fileFormatType}"
        <#if source.fsS3aAwsCredentialsProvider??>
            fs.s3a.aws.credentials.provider="${source.fsS3aAwsCredentialsProvider}"
        <#else>
            fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source ftp -->
<#if engine.ftpSources?? && engine.ftpSources?size != 0>
    <#list engine.ftpSources as source>
        FtpFile {
        host="${source.host}"
        port="${source.port}"
        user="${source.user}"
        password="${source.password}"
        path="${source.path}"
        file_format_type="${source.fileFormatType}"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.connectionMode??>
            connection_mode="${source.connectionMode}"
        </#if>
        <#if source.fileFilterPattern??>
            file_filter_pattern="${source.fileFilterPattern}"
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.parsePartitionFromPath??>
            parse_partition_from_path="${source.parsePartitionFromPath}"
        </#if>
        <#if source.dateFormat??>
            date_format="${source.dateFormat}"
        </#if>
        <#if source.datetimeFormat??>
            datetime_format="${source.datetimeFormat}"
        </#if>
        <#if source.timeFormat??>
            time_format="${source.timeFormat}"
        </#if>
        <#if source.skipHeaderRowNumber??>
            skip_header_row_number=${source.skipHeaderRowNumber}
        </#if>
        <#if source.readColumns??>
            read_columns="${source.readColumns}"
        </#if>
        <#if source.sheetName??>
            sheet_name="${source.sheetName}"
        </#if>
        <#if source.compressCodec??>
            compress_codec="${source.compressCodec}"
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        }
    </#list>
</#if>
<#-- source ossAli -->
<#if engine.ossAliSources?? && engine.ossAliSources?size != 0>
    <#list engine.ossAliSources as source>
        OssFile {
        path="${source.path}"
        bucket="${source.bucket}"
        access_key="${source.accessKey}"
        access_secret="${source.accessSecret}"
        endpoint="${source.endpoint}"
        file_format_type="${source.fileFormatType}"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.parsePartitionFromPath??>
            parse_partition_from_path="${source.parsePartitionFromPath}"
        </#if>
        <#if source.dateFormat??>
            date_format="${source.dateFormat}"
        </#if>
        <#if source.datetimeFormat??>
            datetime_format="${source.datetimeFormat}"
        </#if>
        <#if source.timeFormat??>
            time_format="${source.timeFormat}"
        </#if>
        <#if source.skipHeaderRowNumber??>
            skip_header_row_number=${source.skipHeaderRowNumber}
        </#if>
        <#if source.readColumns??>
            read_columns="${source.readColumns}"
        </#if>
        <#if source.sheetName??>
            sheet_name="${source.sheetName}"
        </#if>
        <#if source.compressCodec??>
            compress_codec="${source.compressCodec}"
        </#if>
        <#if source.fileFilterPattern??>
            file_filter_pattern="${source.fileFilterPattern}"
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if source.encoding??>
            encoding="${source.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- source ossAliFile2File -->
<#if engine.ossAliFile2FileSources?? && engine.ossAliFile2FileSources?size != 0>
    <#list engine.ossAliFile2FileSources as source>
        OssFile {
        path="${source.path}"
        bucket="${source.bucket}"
        access_key="${source.accessKey}"
        access_secret="${source.accessSecret}"
        endpoint="${source.endpoint}"
        file_format_type="${source.fileFormatType}"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source kafka -->
<#if engine.kafkaSources?? && engine.kafkaSources?size != 0>
    <#list engine.kafkaSources as source>
        Kafka {
        topic="${source.topic}"
        bootstrap.servers="${source.bootstrapServers}"
        <#if source.pattern??>
            pattern="${source.pattern}"
        </#if>
        <#if source.consumerGroup??>
            consumer.group="${source.consumerGroup}"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.commitOnCheckpoint??>
            commit_on_checkpoint="${source.commitOnCheckpoint}"
        </#if>
        <#if source.kafkaConfig??>
            kafka.config = {
            <#list source.kafkaConfig as key, value>
                ${key} = "${value}"
            </#list>
            }
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                "${key}"="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if source.mappers??>
            mappers = {
            <#list source.mappers as key, value>
                "${key}" = "${value}"
            </#list>
            }
        </#if>
        <#if source.format??>
            format="${source.format}"
        </#if>
        <#if source.formatErrorHandleWay??>
            format_error_handle_way="${source.formatErrorHandleWay}"
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.startMode??>
            start_mode=${source.startMode}
        </#if>
        <#if source.startModeOffsets??>
            start_mode.offsets="${source.startModeOffsets}"
        </#if>
        <#if source.startModeTimestamp??>
            start_mode.timestamp="${source.startModeTimestamp}"
        </#if>
        <#if source.partitionDiscoveryIntervalMillis??>
            partition-discovery.interval-millis="${source.partitionDiscoveryIntervalMillis}"
        </#if>
        }
    </#list>
</#if>
<#-- source S3File -->
<#if engine.s3FileSources?? && engine.s3FileSources?size != 0>
    <#list engine.s3FileSources as source>
        S3File {
        path="${source.path}"
        bucket="${source.bucket}"
        access_key="${source.accessKey}"
        secret_key="${source.accessSecret}"
        fs.s3a.endpoint="${source.endpoint}"
        file_format_type="${source.fileFormatType}"
        <#if source.fsS3aAwsCredentialsProvider??>
            fs.s3a.aws.credentials.provider="${source.fsS3aAwsCredentialsProvider}"
        <#else>
            fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.readColumns??>
            read_columns="${source.readColumns}"
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.parsePartitionFromPath??>
            parse_partition_from_path="${source.parsePartitionFromPath}"
        </#if>
        <#if source.dateFormat??>
            date_format="${source.dateFormat}"
        </#if>
        <#if source.datetimeFormat??>
            datetime_format="${source.datetimeFormat}"
        </#if>
        <#if source.timeFormat??>
            time_format="${source.timeFormat}"
        </#if>
        <#if source.skipHeaderRowNumber??>
            skip_header_row_number=${source.skipHeaderRowNumber}
        </#if>
        <#if source.sheetName??>
            sheet_name="${source.sheetName}"
        </#if>
        <#if source.compressCodec??>
            compress_codec="${source.compressCodec}"
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if source.encoding??>
            encoding="${source.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- source S3File2File -->
<#if engine.s3File2FileSources?? && engine.s3File2FileSources?size != 0>
    <#list engine.s3File2FileSources as source>
        S3File {
        path="${source.path}"
        bucket="${source.bucket}"
        access_key="${source.accessKey}"
        secret_key="${source.secretKey}"
        fs.s3a.endpoint="${source.fsS3aEndpoint}"
        file_format_type="${source.fileFormatType}"
        <#if source.fsS3aAwsCredentialsProvider??>
            fs.s3a.aws.credentials.provider="${source.fsS3aAwsCredentialsProvider}"
        <#else>
            fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source Doris  -->
<#if engine.dorisSources?? && engine.dorisSources?size != 0>
    <#list engine.dorisSources as source>
        Doris {
        fenodes="${source.fenodes}"
        username="${source.username}"
        <#if source.password??>
            password="${source.password}"
        <#else>
            password=""
        </#if>
        database="${source.database}"
        table="${source.table}"
        <#if source.dorisReadField??>
            doris.read.field="${source.dorisReadField}"
        </#if>
        <#if source.queryPort??>
            query-port="${source.queryPort}"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.dorisFilterQuery??>
            doris.filter.query="${source.dorisFilterQuery}"
        </#if>
        <#if source.dorisBatchSize??>
            doris.batch.size="${source.dorisBatchSize}"
        </#if>
        <#if source.dorisRequestQueryTimeouts??>
            doris.request.query.timeout.s="${source.dorisRequestQueryTimeouts}"
        </#if>
    <#-- 需要加?string("##0")，不然千位会有逗号，例如：9,030 -->
        <#if source.dorisExecMemLimit != 0>
            doris.exec.mem.limit="${source.dorisExecMemLimit?string("##0")}"
        </#if>
        <#if source.dorisRequestReadTimeoutMs != 0>
            doris.request.read.timeout.ms="${source.dorisRequestReadTimeoutMs?string("##0")}"
        </#if>
        <#if source.dorisRequestConnectTimeoutMs != 0>
            doris.request.connect.timeout.ms=${source.dorisRequestConnectTimeoutMs?string("##0")}
        </#if>
        <#if source.parallelism??>
            "parallelism"=${source.parallelism}
        </#if>
        }
    </#list>
</#if>
<#-- source ossHuawei -->
<#if engine.ossHuaweiSources?? && engine.ossHuaweiSources?size != 0>
    <#list engine.ossHuaweiSources as source>
        ObsFile {
        path="${source.path}"
        bucket="${source.bucket}"
        access_key="${source.accessKey}"
        security_key="${source.accessSecret}"
        endpoint="${source.endpoint}"
        file_format_type="${source.fileFormatType}"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.fieldDelimiter??>
            field_delimiter="${source.fieldDelimiter}"
        </#if>
        <#if source.parsePartitionFromPath??>
            parse_partition_from_path="${source.parsePartitionFromPath}"
        </#if>
        <#if source.dateFormat??>
            date_format="${source.dateFormat}"
        </#if>
        <#if source.datetimeFormat??>
            datetime_format="${source.datetimeFormat}"
        </#if>
        <#if source.timeFormat??>
            time_format="${source.timeFormat}"
        </#if>
        <#if source.skipHeaderRowNumber??>
            skip_header_row_number=${source.skipHeaderRowNumber}
        </#if>
        <#if source.readColumns??>
            read_columns="${source.readColumns}"
        </#if>
        <#if source.sheetName??>
            sheet_name="${source.sheetName}"
        </#if>
        <#if source.compressCodec??>
            compress_codec="${source.compressCodec}"
        </#if>
        <#if source.fileFilterPattern??>
            file_filter_pattern="${source.fileFilterPattern}"
        </#if>
        <#if source.schema??>
            schema= {
            fields {
            <#list source.schema?keys as key>
                ${key}="${source.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if source.encoding??>
            encoding="${source.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- source DataHub -->
<#if engine.dataHubSources?? && engine.dataHubSources?size != 0>
    <#list engine.dataHubSources as source>
        DataHub {
        endpoint="${source.endpoint}"
        accessId="${source.accessId}"
        accessKey="${source.accessKey}"
        project="${source.project}"
        topic="${source.topic}"
        subId="${source.subId}"
        cursor_mode="${source.cursorMode}"
        customTimestamp=${source.customTimestamp}
        schema= {
        fields {
        <#list source.schema?keys as key>
            ${key}="${source.schema[key]}"
        </#list>
        }
        }
        timeout = 3000
        <#if source.endTimestampMillis??>
            endTimestampMillis=${source.endTimestampMillis}
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- source adb gpdist -->
<#if engine.adbGpdistSources?? && engine.adbGpdistSources?size != 0>
    <#list engine.adbGpdistSources as source>
        ArgoLocalFile {
        path="${source.path}"
        schema= {
        fields {
        <#list source.adbSchema?keys as key>
            ${key}="${source.adbSchema[key]}"
        </#list>
        }
        }
        file_format_type="${source.fileFormatType}"
        field_delimiter="${source.fieldDelimiter}"
        adb_url="${source.adbUrl}"
        adb_driver="${source.adbDriver}"
        adb_user="${source.adbUser}"
        adb_password="${source.adbPassword}"
        adb_database="${source.schemaName}"
        adb_table="${source.adbTable}"
        argo_schema="xxx"
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        adb_gpfdist_address="${source.adbGpfdistDddress}"
        <#if source.adbTmpFilePath??>
            adb_tmp_file_path="${source.adbTmpFilePath}"
        </#if>
        <#if source.adbExternalTableSchema??>
            adb_external_table_schema="${source.adbExternalTableSchema}"
        </#if>
        <#if source.adbExternalTableName??>
            adb_external_table_name="${source.adbExternalTableName}"
        </#if>
        <#if source.adbGpfdistPath??>
            adb_gpfdist_path="${source.adbGpfdistPath}"
        </#if>
        <#if source.adbExternalTableDelimiter??>
            adb_external_table_delimiter="${source.adbExternalTableDelimiter}"
        </#if>
        <#if source.adbWhereSql??>
            adb_where_sql="${source.adbWhereSql}"
        </#if>
        }
    </#list>
</#if>
<#-- argo hafs source -->
<#if engine.argoHdfsFileSources?? && engine.argoHdfsFileSources?size != 0>
    <#list engine.argoHdfsFileSources as source>
        ArgoAdbHdfsFile{
        fs.defaultFS="${source.defaultFS}"
        path="${source.path}"
        file_format_type = "${source.fileFormatType}"
        field_delimiter = "${source.fieldDelimiter}"
        row_delimiter = "${source.rowDelimiter}"
        is_enable_transaction = true
        argo_url="${source.argoUrl}"
        argo_user="${source.argoUser}"
        argo_password="${source.argoPassword}"
        argo_schema="${source.argoSchema}"
        argo_table="${source.argoTable}"
        argo_tmp_table_name="${source.argoTmpTableName}"
        <#if source.argoTmpSchema??>
            argo_tmp_schema="${source.argoTmpSchema}"
        </#if>
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        schema= {
        fields {
        <#list source.argoSchemas?keys as key>
            ${key}="${source.argoSchemas[key]}"
        </#list>
        }
        }
        }
    </#list>
</#if>
<#-- source clickhouse -->
<#if engine.clickhouseSources?? && engine.clickhouseSources?size != 0>
    <#list engine.clickhouseSources as source>
        Clickhouse {
        <#if source.resultTableName?? >
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.host??>
            host="${source.host}"
        </#if>
        <#if source.database??>
            database="${source.database}"
        </#if>
        <#if source.username??>
            username="${source.username}"
        </#if>
        <#if source.password??>
            password="${source.password}"
        </#if>
        <#if source.sql??>
            sql="${source.sql}"
        </#if>
        <#if source.optionsConfig??>
            clickhouse.options.config= {
            <#list source.optionsConfig?keys as key>
                "${key}"="${source.optionsConfig[key]}"
            </#list>
            }
        </#if>
        <#if source.pipeline??>
            "table_path"=${source.pipeline}
        </#if>
        }
    </#list>
</#if>
<#-- source hbaseSource -->
<#if engine.hbaseSources?? && engine.hbaseSources?size != 0>
    <#list engine.hbaseSources as source>
        Hbase {
        result_table_name = "${source.resultTableName}"
        zookeeper_quorum = "${source.zookeeperQuorum}"
        table = "${source.tableName}"
        <#if source.queryColumns??>
            query_columns= ${source.queryColumns}
        <#else>
        </#if>
        <#if source.columns??>
            schema={
            columns= [
            <#list source.columns as item>
                ${item}<#if item_has_next>, </#if>
            </#list>
            ]
            }
        <#else>
        </#if>
        <#if source.filePath??>
            file_path= "${source.filePath}"
        <#else>
        </#if>
        <#if source.user??>
            user= "${source.user}"
        <#else>
        </#if>
        <#if source.serverPrincipal??>
            server_principal= "${source.serverPrincipal}"
        <#else>
        </#if>
        }
    </#list>
</#if>

<#--  http base source -->
<#if engine.httpBaseSources?? && engine.httpBaseSources?size != 0>
    <#list engine.httpBaseSources as source>
        HTTP {
        <#if source.resultTableName??>
            result_table_name="${source.resultTableName}"
        </#if>
        <#if source.url??>
            url="${source.url}"
        </#if>
        method="${source.method}"
        format="${source.format}"
        params= {
        <#list source.params?keys as key>
            ${key}="${source.params[key]}"
        </#list>
        }
        headers = {
        <#list source.headers?keys as key>
            ${key}="${source.headers[key]}"
        </#list>
        }
        <#if source.body??>
            body="${source.body}"
        </#if>
        schema = {
        fields = {
        <#list  source.fieldTypeMap?keys as nestedKey>
            ${nestedKey}="${source.fieldTypeMap[nestedKey]}"
        </#list>
        }
        }
        json_field = {
        <#list  source.fieldJsonPathMap?keys as nestedKey>
            ${nestedKey}="${source.fieldJsonPathMap[nestedKey]}"
        </#list>
        }
        <#if source.pageing?? && source.pageing?size != 0 >
            pageing = {
            <#list source.pageing?keys as key>
                ${key}="${source.pageing[key]}"
            </#list>
            }
        </#if>
        body_send_type="${source.bodySendType}"
        connect_timeout_ms = "${source.connectTimeoutMs}"
        socket_timeout_ms = "${source.connectTimeoutMs}"
        datetimeFormat = "${source.datetimeFormat}"
        dateFormat ="${source.dateFormat}"
        timeFormat ="${source.timeFormat}"
        }
    </#list>
</#if>
<#-- source MaxCompute -->
<#if engine.maxComputeSources?? && engine.maxComputeSources?size != 0>
    <#list engine.maxComputeSources as source>
        Maxcompute {
            accessId="${source.accessId}"
            accessKey="${source.accessKey}"
            endpoint="${source.endpoint}"
            <#if source.tunnelEndpoint?has_content>
            tunnel_endpoint="${source.tunnelEndpoint}"
            </#if>
            project="${source.project}"
            table_name="${source.tableName}"
            split_row="${source.splitRow}"
            <#if source.partitionSpec?has_content>
            partition_spec="${source.partitionSpec}"
            </#if>
            schema= {
                fields {
                <#list source.schema?keys as key>
                    ${key}="${source.schema[key]}"
                </#list>
                }
            }
        }
    </#list>
</#if>

}
transform {
<#if engine.transforms ??>
    <#list engine.transforms as transform>
        ${transform.engineTransformType}{
        <#if transform.sourceTableName?? >
            source_table_name="${transform.sourceTableName}"
        <#else>
        </#if>
        <#if transform.resultTableName?? >
            result_table_name="${transform.resultTableName}"
        <#else>
        </#if>
        <#switch transform.engineTransformType>
            <#case "Copy">
                <#if transform.copyFiles??>
                    fields{
                    <#list transform.copyFiles?keys as key>
                        "${key}"="${transform.copyFiles["${key}"]}"
                    </#list>
                    }
                <#else>
                </#if>
                <#break>
            <#case "FieldMapper">
                <#if transform.fieldMapper??>
                    field_mapper={
                    <#list transform.fieldMapper?keys as key>
                        "${key}"="${transform.fieldMapper["${key}"]}"
                    </#list>
                    }
                <#else>
                </#if>
                <#break>
            <#case "Filter">
                <#if transform.filterFields??>
                    fields=[${transform.filterFields}]
                <#else>
                </#if>
                <#break>
            <#case "Replace">
                <#if transform.replaceField?? && transform.replacePattern?? && transform.replaceReplacement?? && transform.replaceIsRegex??>
                    replace_field="${transform.replaceField}"
                    pattern="${transform.replacePattern}"
                    replacement="${transform.replaceReplacement}"
                    is_regex=${transform.replaceIsRegex}
                    <#if transform.replaceFirst?? && transform.replaceFirst =="true" && transform.replaceIsRegex=='true' >
                        replace_first= true
                    <#else>
                    </#if>
                <#else>
                </#if>
                <#break>
            <#case "Split">
                <#if transform.splitSeparator?? && transform.splitField?? && transform.splitOutputFields??>
                    separator="${transform.splitSeparator}"
                    split_field="${transform.splitField}"
                    output_fields=[${transform.splitOutputFields}]
                <#else>
                </#if>
                <#break>
            <#case "SQL">
                <#if transform.sqlQuery??>
                    query="${transform.sqlQuery}"
                <#else>
                </#if>
                <#break>
            <#case "RandomInfo">
                <#if transform.replaceField?? && transform.randomInfoType??>
                    random_info_field="${transform.randomInfoField}"
                    random_info_type="${transform.randomInfoType}"
                <#else>
                </#if>
                <#break>
            <#case "ConstantAndNum">
                <#if transform.randomConstantNumField??>
                    random_constant_num_field="${transform.randomConstantNumField}"
                    random_num_len="${transform.randomNumLen?default(6)}"
                    prefix_constant="${transform.prefixConstant?default("")}"
                <#else>
                </#if>
                <#break>
            <#case "CustomDateTime">
                <#if transform.randomTimeField??>
                    random_time_field="${transform.randomTimeField}"
                    timestamp_type="${transform.timestampType?default(0)}"
                    time_format="${transform.timeFormat?default("yyyy-MM-dd HH:mm:ss")}"
                <#else>
                </#if>
                <#break>
            <#case "UniqueID">
                <#if transform.uniqueIdField??>
                    unique_id_field="${transform.uniqueIdField}"
                    unique_id_type="${transform.uniqueIdType?default(0)}"
                <#else>
                </#if>
                <#break>
            <#case "LeftJoin">
            <#case "InnerJoin">
            <#case "RightJoin">
                <#if transform.fieldsString??>
                    fields = ${transform.fieldsString}
                </#if>
                <#if transform.primaryKey??>
                    primary_key = "${transform.primaryKey}"
                </#if>
                <#if transform.joinKeys??>
                    join_keys={
                    <#list transform.joinKeys?keys as key>
                        ${key}=${transform.joinKeys[key]}
                    </#list>
                    }
                </#if>
                <#if transform.mappingKeys??>
                    mapping_keys={
                    <#list transform.mappingKeys?keys as key>
                        ${key}=${transform.mappingKeys[key]}
                    </#list>
                    }
                </#if>
                <#if transform.joinState??>
                    join_state = "${transform.joinState}"
                </#if>
                <#if transform.tableId??>
                    table_id = "${transform.tableId}"
                </#if>
                <#if transform.masterFieldsString??>
                    master_fields = ${transform.masterFieldsString}
                </#if>
                <#if transform.addFieldsString??>
                    add_fields = ${transform.addFieldsString}
                </#if>
                <#break>
            <#case "Dah">
                <#if transform.dahDesensitization??>
                    <#list transform.dahDesensitization?keys as key>
                        "dah_${key}"="${transform.dahDesensitization["${key}"]}"
                    </#list>
                <#else>
                </#if>
                <#break>
            <#case "ReplaceAll">
                <#if transform.replaceAllRewrite??>
                    replace_field_patterns=[
                    <#list transform.replaceAllRewrite as item>
                        {
                        <#list item?keys as key>
                            "${key}": ${item[key]}
                        </#list>
                        }<#if item_has_next>,</#if>
                    </#list>
                    ]
                <#else>
                </#if>
                <#break>
            <#case "XmlPath">
                <#if transform.timeFormat??>
                    timeFormat="${transform.timeFormat}"
                </#if>
                <#if transform.dateFormat??>
                    dateFormat="${transform.dateFormat}"
                </#if>
                <#if transform.datetimeFormat??>
                    datetimeFormat="${transform.datetimeFormat}"
                </#if>
                <#if transform.lineSplit??>
                    lineSplit="${transform.lineSplit}"
                </#if>
                <#if transform.xmlMappingKeys??>
                    mapping_keys= [
                    <#list transform.xmlMappingKeys  as nestedMap>
                        {
                        <#list nestedMap?keys as nestedKey>
                            ${nestedKey}="${nestedMap[nestedKey]}"
                        </#list>
                        }
                    </#list>
                    ]
                <#else>
                </#if>
                <#if transform.xmlOtherOutputFields??>
                    output_fields= [
                    <#list transform.xmlOtherOutputFields as nestedMap>
                        {
                        <#list nestedMap?keys as nestedKey>
                            ${nestedKey}="${nestedMap[nestedKey]}"
                        </#list>
                        }
                    </#list>
                    ]
                <#else>
                </#if>
                <#break>
        </#switch>
        }
    </#list>
<#else>
</#if>
}
sink {
<#-- sink jdbc -->
<#if engine.jdbcSinks?? && engine.jdbcSinks?size != 0>
    <#list engine.jdbcSinks as sink>
        Jdbc {
        url="${sink.url}"
        driver="${sink.driver}"
        <#if sink.user??>
            user="${sink.user}"
        </#if>
        <#if sink.password??>
            password="${sink.password}"
        </#if>
        database="${sink.database}"
        table="${sink.table}"
        "batch_size"="${sink.batchSize}"
        "support_upsert_by_query_primary_key_exist"="${sink.supportUpsertByQueryPrimaryKeyExist}"
        <#if sink.sourceTableName?? >
            "source_table_name"="${sink.sourceTableName}"
        <#else>
        </#if>
        "generate_sink_sql"="${sink.generateSinkSql}"
        <#if sink.primaryKeys??>
            "primary_keys" = ${sink.primaryKeys}
        </#if>
        <#if sink.fieldIde?? >
            "field_ide"=${sink.fieldIde}
        <#else>
        </#if>
        <#if sink.enableUpsert?? >
            "enable_upsert"="${sink.enableUpsert}"
        <#else>
        </#if>
        <#if sink.pkStrategy?? >
            "pk_strategy"="${sink.pkStrategy}"
        <#else>
        </#if>
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        <#if sink.useCopyStatement=="true">
            "use_copy_statement"="true"
        <#else>
        </#if>
        <#if sink.insertErrorStrategy??>
            "insert_error_strategy"=${sink.insertErrorStrategy}
        <#else>
        </#if>
        <#if sink.conflictStrategyRow??>
            "conflict_strategy_row"=${sink.conflictStrategyRow}
        <#else>
        </#if>
        <#if sink.joyadataJoinType?? && config.joyadataJoinOns??>
            "joyadata_join_type"=${sink.joyadataJoinType}
            "joyadata_join_on"=${sink.joyadataJoinOns}
        <#else>
        </#if>
        <#if sink.compatibleMode??>
            "compatible_mode"=${sink.compatibleMode}
        <#else>
        </#if>
        <#if sink.useKerberos??>
            "use_kerberos"=${sink.useKerberos}
        <#else>
        </#if>
        <#if sink.kerberosPrincipal??>
            "kerberos_principal"="${sink.kerberosPrincipal}"
        <#else>
        </#if>
        <#if sink.kerberosKeytabPath??>
            "kerberos_keytab_path"=${sink.kerberosKeytabPath}
        <#else>
        </#if>
        <#if sink.krb5Path??>
            "krb5_path"=${sink.krb5Path}
        <#else>
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        <#if sink.partitionKeys??>
            "partition_keys"=${sink.partitionKeys}
        <#else>
        </#if>
        }
    </#list>
</#if>
<#-- sink kafka -->
<#if engine.kafkaSinks?? && engine.kafkaSinks?size != 0>
    <#list engine.kafkaSinks as config>
        kafka {
        <#if config.sourceTableName?? >
            "source_table_name"="${config.sourceTableName}"
        <#else>
        </#if>
        <#if config.topic??>
            topic = "${config.topic}"
        </#if>
        <#if config.bootstrapServers??>
            bootstrap.servers = "${config.bootstrapServers}"
        </#if>
        <#if config.kafkaRequestTimeoutMs??>
            kafka.request.timeout.ms = ${config.kafkaRequestTimeoutMs}
        </#if>
        <#if config.format??>
            format = "${config.format}"
        </#if>
        <#if config.semantics??>
            semantics = ${config.semantics}
        </#if>
        <#if config.fieldDelimiter??>
            field_delimiter = "${config.fieldDelimiter}"
        </#if>
        <#if config.partition??>
            partition = "${config.partition}"
        </#if>
        <#if config.kafkaConfig??>
            kafka.config = {
            <#list config.kafkaConfig as key, value>
                ${key} = "${value}"
            </#list>
            }
        </#if>
        }
    </#list>
</#if>
<#-- sink clickhouse -->
<#if engine.clickhouseSinks?? && engine.clickhouseSinks?size != 0>
    <#list engine.clickhouseSinks as config>
        Clickhouse {
        <#if config.sourceTableName?? >
            source_table_name = "${config.sourceTableName}"
        </#if>
        <#if config.host??>
            host = "${config.host}"
        </#if>
        <#if config.database??>
            database = "${config.database}"
        </#if>
        <#if config.table??>
            table = "${config.table}"
        </#if>
        <#if config.username??>
            username = "${config.username}"
        </#if>
        <#if config.password??>
            password = "${config.password}"
        </#if>
        <#if config.primaryKey??>
            primary_key = "${config.primaryKey}"
        </#if>
        <#if config.supportUpsert??>
            support_upsert = ${config.supportUpsert}
        </#if>
        <#if config.optionsConfig??>
            clickhouse.options.config= {
            <#list config.optionsConfig?keys as key>
                "${key}"="${config.optionsConfig[key]}"
            </#list>
            }
        </#if>
        }
    </#list>
</#if>
<#-- sink hive -->
<#if engine.hiveSinks?? && engine.hiveSinks?size != 0>
    <#list engine.hiveSinks as config>
        Hive {
        <#if config.sourceTableName?? >
            source_table_name = "${config.sourceTableName}"
        </#if>
        <#if config.tableName??>
            table_name = "${config.tableName}"
        </#if>
        <#if config.metastoreUri??>
            metastore_uri = "${config.metastoreUri}"
        </#if>
        <#if config.hdfsSitePath??>
            hdfs_site_path = "${config.hdfsSitePath}"
        </#if>
        <#if config.hiveSitePath??>
            hive_site_path = "${config.hiveSitePath}"
        </#if>
        <#if config.krb5Path??>
            krb5_path = "${config.krb5Path}"
        </#if>
        <#if config.kerberosPrincipal??>
            kerberos_principal = "${config.kerberosPrincipal}"
        </#if>
        <#if config.kerberosKeytabPath??>
            "kerberos_keytab_path" = "${config.kerberosKeytabPath}"
        </#if>
        <#if config.remoteUser??>
            "remote_user" = "${config.remoteUser}"
        </#if>
        }
    </#list>
</#if>
<#-- sink hdfsFile -->
<#if engine.hdfsFileSinks?? && engine.hdfsFileSinks?size != 0>
    <#list engine.hdfsFileSinks as config>
        HdfsFile {
        fs.defaultFS = "${config.fsDefaultFS}"
        path = "${config.path}"
        tmp_path = "${config.tmpPath}"
        <#if config.sourceTableName?? >
            source_table_name = "${config.sourceTableName}"
        </#if>
        <#if config.hdfsSitePath??>
            hdfs_site_path = "${config.hdfsSitePath}"
        </#if>
        <#if config.fileNameExpression??>
            custom_filename=true
            file_name_expression="${config.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if config.filenameTimeFormat??>
            filename_time_format = "${config.filenameTimeFormat}"
        </#if>
        <#if config.fileFormatType??>
            file_format_type = "${config.fileFormatType}"
        </#if>
        <#if config.fieldDelimiter??>
            field_delimiter = "${config.fieldDelimiter}"
        </#if>
        <#if config.rowDelimiter??>
            row_delimiter = "${config.rowDelimiter}"
        </#if>
        <#if config.havePartition??>
            have_partition = "${config.havePartition}"
        </#if>
        <#if config.partitionBy??>
            partition_by=${config.partitionBy}
        </#if>
        <#if config.partitionDirExpression??>
            partition_dir_expression="${config.partitionDirExpression}"
        </#if>
        <#if config.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${config.isPartitionFieldWriteInFile}"
        </#if>
        <#if config.sinkColumns??>
            sink_columns=${config.sinkColumns}
        </#if>
        <#if config.batchSize??>
            batch_size=${config.batchSize}
        </#if>
        <#if config.compressCodec??>
            compress_codec="${config.compressCodec}"
        </#if>
        <#if config.krb5Path??>
            krb5_path = "${config.krb5Path}"
        </#if>
        <#if config.kerberosPrincipal??>
            kerberos_principal = "${config.kerberosPrincipal}"
        </#if>
        <#if config.kerberosKeytabPath??>
            kerberos_keytab_path = "${config.kerberosKeytabPath}"
        </#if>
        <#if config.maxRowsInMemory??>
            max_rows_in_memory = ${config.maxRowsInMemory}
        </#if>
        <#if config.sheetName??>
            sheet_name = "${config.sheetName}"
        </#if>
        <#if config.dateFormat??>
            date_format="${config.dateFormat}"
        </#if>
        <#if config.datetimeFormat??>
            datetime_format="${config.datetimeFormat}"
        </#if>
        <#if config.timeFormat??>
            time_format="${config.timeFormat}"
        </#if>
        }
    </#list>
</#if>
<#-- sink MongoDB -->
<#if engine.mongodbSinks?? && engine.mongodbSinks?size != 0>
    <#list engine.mongodbSinks as sink>
        MongoDB {
        uri="${sink.uri}"
        database="${sink.database}"
        collection="${sink.collection}"
        <#if sink.schema??>
            schema= {
            fields {
            <#list sink.schema?keys as key>
                ${key}="${sink.schema[key]}"
            </#list>
            }
            }
        </#if>
        <#if sink.bufferFlushMaxRows??>
            buffer-flush.max-rows="${sink.bufferFlushMaxRows}"
        </#if>
        <#if sink.sourceTableName?? >
            source_table_name = "${sink.sourceTableName}"
        </#if>
        <#if sink.bufferFlushInterval??>
            buffer-flush.interval="${sink.bufferFlushInterval}"
        </#if>
        <#if sink.retryMax??>
            retry.max ="${sink.retryMax}"
        </#if>
        <#if sink.retryInterval??>
            retry.interval ="${sink.retryInterval}"
        </#if>
        <#if sink.upsertEnable??>
            upsert-enable ="${sink.upsertEnable}"
        </#if>
        <#if sink.primaryKey??>
            primary-key ="${sink.primaryKey}"
        </#if>
        <#if sink.transaction??>
            transaction ="${sink.transaction}"
        </#if>
        }
    </#list>
</#if>
<#-- sink kudu -->
<#if engine.kuduSinks?? && engine.kuduSinks?size != 0>
    <#list engine.kuduSinks as sink>
        kudu {
        kudu_masters="${sink.kuduMasters}"
        table_name="${sink.tableName}"
        <#if sink.clientWorkerCount != 0>
            client_worker_count="${sink.clientWorkerCount}"
        </#if>
        <#if sink.clientDefaultOperationTimeoutMs??>
            client_default_operation_timeout_ms="${sink.clientDefaultOperationTimeoutMs}"
        </#if>
        <#if sink.clientDefaultAdminOperationTimeoutMs??>
            client_default_admin_operation_timeout_ms="${sink.clientDefaultAdminOperationTimeoutMs}"
        </#if>
        <#if sink.enableKerberos??>
            enable_kerberos="${sink.enableKerberos}"
        </#if>
        <#if sink.kerberosPrincipal??>
            kerberos_principal="${sink.kerberosPrincipal}"
        </#if>
        <#if sink.kerberosKeytab??>
            kerberos_keytab="${sink.kerberosKeytab}"
        </#if>
        <#if sink.kerberosKrb5conf??>
            kerberos_krb5conf="${sink.kerberosKrb5conf}"
        </#if>
        <#if sink.saveMode??>
            save_mode="${sink.saveMode}"
        </#if>
        <#if sink.sessionFlushMode??>
            session_flush_mode="${sink.sessionFlushMode}"
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.bufferFlushInterval != 0>
            buffer_flush_interval="${sink.bufferFlushInterval}"
        </#if>
        <#if sink.ignoreNotFound??>
            ignore_not_found="${sink.ignoreNotFound}"
        </#if>
        <#if sink.ignoreNotDuplicate??>
            ignore_not_duplicate="${sink.ignoreNotDuplicate}"
        </#if>
        <#if sink.sourceTableName?? >
            source_table_name = "${sink.sourceTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink localFile -->
<#if engine.localFileSinks?? && engine.localFileSinks?size != 0>
    <#list engine.localFileSinks as sink>
        LocalFile {
        path="${sink.path}"
        source_table_name = "${sink.sourceTableName}"
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=true
        </#if>
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.customFilename??>
            custom_filename="${sink.customFilename}"
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by=${sink.partitionBy}
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.enableHeaderWrite??>
            enable_header_write="${sink.enableHeaderWrite}"
        </#if>
        <#if sink.validates??>
            validates="${sink.validates}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.fixedFieldLengthStrategy??>
            fixed_field_length_strategy="${sink.fixedFieldLengthStrategy}"
        </#if>
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        clean_target_folder="${sink.cleanTargetFolder?c}"
        <#if sink.nullToValue??>
            null_to_value="${sink.nullToValue}"
        </#if>
        <#if sink.tdsqlPartitionName??>
            tdsql_partition_name="${sink.tdsqlPartitionName}"
        </#if>
        }
    </#list>
</#if>
<#if engine.localFile2FileSinks?? && engine.localFile2FileSinks?size != 0>
    <#list engine.localFile2FileSinks as sink>
        LocalFile {
        path="${sink.path}"
        <#if sink.isFile??>
            is_file=${sink.isFile}
        </#if>
        <#if sink.fileName??>
            file_name="${sink.fileName}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink mergrLocalFile -->
<#if engine.mergeLocalFileSinks?? && engine.mergeLocalFileSinks?size != 0>
    <#list engine.mergeLocalFileSinks as sink>
        MergeLocalFile {
        final_name="${sink.finalName}"
        path="${sink.path}"
        source_table_name = "${sink.sourceTableName}"
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.customFilename??>
            custom_filename="${sink.customFilename}"
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by=${sink.partitionBy}
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.enableHeaderWrite??>
            enable_header_write="${sink.enableHeaderWrite}"
        </#if>
        <#if sink.validates??>
            validates="${sink.validates}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        overwrite_file="${sink.overwriteFile?c}"
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.fixedFieldLengthStrategy??>
            fixed_field_length_strategy="${sink.fixedFieldLengthStrategy}"
        </#if>
        <#if sink.nullToValue??>
            null_to_value="${sink.nullToValue}"
        </#if>
        <#if sink.tdsqlPartitionName??>
            tdsql_partition_name="${sink.tdsqlPartitionName}"
        </#if>
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- sink sftp -->
<#if engine.sftpSinks?? && engine.sftpSinks?size != 0>
    <#list engine.sftpSinks as sink>
        SftpFile {
        host="${sink.host}"
        port="${sink.port}"
        user="${sink.user}"
        password="${sink.password}"
        path="${sink.path}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by="${sink.partitionBy}"
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.validates??>
            validates="${sink.validates}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        clean_target_folder="${sink.cleanTargetFolder?c}"
        <#if sink.tdsqlPartitionName??>
            tdsql_partition_name="${sink.tdsqlPartitionName}"
        </#if>
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- sink sftp -->
<#if engine.mergeSftpSinks?? && engine.mergeSftpSinks?size != 0>
    <#list engine.mergeSftpSinks as sink>
        MergeSftpFile {
        final_name="${sink.finalName}"
        host="${sink.host}"
        port="${sink.port}"
        user="${sink.user}"
        password="${sink.password}"
        path="${sink.path}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by="${sink.partitionBy}"
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.validates??>
            validates="false"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        clean_target_folder="${sink.cleanTargetFolder?c}"
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- sink sftpFile2File -->
<#if engine.sftpFile2FileSinks?? && engine.sftpFile2FileSinks?size != 0>
    <#list engine.sftpFile2FileSinks as sink>
        SftpFile {
        host="${sink.host}"
        port="${sink.port}"
        user="${sink.user}"
        password="${sink.password}"
        path="${sink.path}"
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.isFile??>
            is_file=${sink.isFile}
        </#if>
        <#if sink.fileName??>
            file_name="${sink.fileName}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink inspurOSSFile2File -->
<#if engine.inspurOSSFile2FileSinks?? && engine.inspurOSSFile2FileSinks?size != 0>
    <#list engine.inspurOSSFile2FileSinks as sink>
        <#if sink.loadType == "S3">
            S3File {
            fs.s3a.endpoint="${sink.fsS3aEndpoint}"
            <#if sink.fsS3aAwsCredentialsProvider??>
                fs.s3a.aws.credentials.provider="${sink.fsS3aAwsCredentialsProvider}"
            <#else>
                fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
            </#if>
        </#if>
        <#if sink.loadType == "ICFSDOS">
            S3IcfsdosFile {
            fs.icfsdos.endpoint="${sink.fsS3aEndpoint}"
            <#if sink.fsS3aAwsCredentialsProvider??>
                fs.icfsdos.aws.credentials.provider="${sink.fsS3aAwsCredentialsProvider}"
            <#else>
                fs.icfsdos.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
            </#if>
        </#if>
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        bucket="${sink.bucket}"
        access_key="${sink.accessKey}"
        secret_key="${sink.secretKey}"
        <#if sink.isFile??>
            is_file=${sink.isFile}
        </#if>
        <#if sink.fileName??>
            file_name="${sink.fileName}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        }
    </#list>
</#if>
<#-- sink ftp -->
<#if engine.ftpSinks?? && engine.ftpSinks?size != 0>
    <#list engine.ftpSinks as sink>
        FtpFile {
        host="${sink.host}"
        port="${sink.port}"
        user="${sink.user}"
        password="${sink.password}"
        path="${sink.path}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.connectionMode??>
            connection_mode="${sink.connectionMode}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by="${sink.partitionBy}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        }
    </#list>
</#if>
<#if engine.mergeFtpSinks?? && engine.mergeFtpSinks?size != 0>
    <#list engine.mergeFtpSinks as sink>
        MergeFtpFile {
        final_name="${sink.finalName}"
        host="${sink.host}"
        port="${sink.port}"
        user="${sink.user}"
        password="${sink.password}"
        path="${sink.path}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.tmpPath??>
            tmp_path="${sink.tmpPath}"
        </#if>
        <#if sink.connectionMode??>
            connection_mode="${sink.connectionMode}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by="${sink.partitionBy}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        <#if sink.validates??>
            validates="false"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        }
    </#list>
</#if>
<#-- sink ossAli -->
<#if engine.ossAliSinks?? && engine.ossAliSinks?size != 0>
    <#list engine.ossAliSinks as sink>
        OssFile {
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        bucket="${sink.bucket}"
        access_key="${sink.accessKey}"
        access_secret="${sink.accessSecret}"
        endpoint="${sink.endpoint}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by=${sink.partitionBy}
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.validates??>
            validates="${sink.validates}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        clean_target_folder="${sink.cleanTargetFolder?c}"
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        <#if sink.tdsqlPartitionName??>
            tdsql_partition_name="${sink.tdsqlPartitionName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink ossAliFile2File -->
<#if engine.ossAliFile2FileSinks?? && engine.ossAliFile2FileSinks?size != 0>
    <#list engine.ossAliFile2FileSinks as sink>
        OssFile {
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        bucket="${sink.bucket}"
        access_key="${sink.accessKey}"
        access_secret="${sink.accessSecret}"
        endpoint="${sink.endpoint}"
        <#if sink.isFile??>
            is_file=${sink.isFile}
        </#if>
        <#if sink.fileName??>
            file_name="${sink.fileName}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink s3File -->
<#if engine.s3FileSinks?? && engine.s3FileSinks?size != 0>
    <#list engine.s3FileSinks as sink>
        <#if sink.loadType == "S3">
            S3File {
            fs.s3a.endpoint="${sink.endpoint}"
            <#if sink.fsS3aAwsCredentialsProvider??>
                fs.s3a.aws.credentials.provider="${sink.fsS3aAwsCredentialsProvider}"
            <#else>
                fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
            </#if>
        </#if>
        <#if sink.loadType == "ICFSDOS">
            S3IcfsdosFile {
            fs.icfsdos.endpoint="${sink.endpoint}"
            <#if sink.fsS3aAwsCredentialsProvider??>
                fs.icfsdos.aws.credentials.provider="${sink.fsS3aAwsCredentialsProvider}"
            <#else>
                fs.icfsdos.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
            </#if>
        </#if>
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        bucket="${sink.bucket}"
        access_key="${sink.accessKey}"
        secret_key="${sink.accessSecret}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by=${sink.partitionBy}
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.schemaSaveMode??>
            schema_save_mode="${sink.schemaSaveMode}"
        </#if>
        <#if sink.dataSaveMode??>
            data_save_mode="${sink.dataSaveMode}"
        </#if>
        <#if sink.validates??>
            validates="${sink.validates}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        clean_target_folder="${sink.cleanTargetFolder?c}"
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        <#if sink.tdsqlPartitionName??>
            tdsql_partition_name="${sink.tdsqlPartitionName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink s3File2file -->
<#if engine.s3File2FileSinks?? && engine.s3File2FileSinks?size != 0>
    <#list engine.s3File2FileSinks as sink>
        S3File {
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        bucket="${sink.bucket}"
        access_key="${sink.accessKey}"
        secret_key="${sink.secretKey}"
        fs.s3a.endpoint="${sink.fsS3aEndpoint}"
        <#if sink.fsS3aAwsCredentialsProvider??>
            fs.s3a.aws.credentials.provider="${sink.fsS3aAwsCredentialsProvider}"
        <#else>
            fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        </#if>
        <#if sink.isFile??>
            is_file=${sink.isFile}
        </#if>
        <#if sink.fileName??>
            file_name="${sink.fileName}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink Doris -->
<#if engine.dorisSinks?? && engine.dorisSinks?size != 0>
    <#list engine.dorisSinks as sink>
        Doris {
        fenodes="${sink.fenodes}"
    <#-- 需要加?string("##0")，不然千位会有逗号，例如：9,030 -->
        query-port="${sink.queryPort?string("##0")}"
        username="${sink.username}"
        <#if sink.password??>
            password="${sink.password}"
        <#else>
            password=""
        </#if>
        database="${sink.database}"
        table="${sink.table}"
        <#if sink.tableIdentifier??>
            table.identifier="${sink.tableIdentifier}"
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.sinkLabelPrefix??>
            sink.label-prefix="${sink.sinkLabelPrefix}"
        </#if>
        <#if sink.sinkEnable2pc??>
            sink.enable-2pc="${sink.sinkEnable2pc}"
        </#if>
        <#if sink.sinkEnableDelete??>
            sink.enable-delete="${sink.sinkEnableDelete}"
        </#if>
        <#if sink.sinkCheckInterval != 0>
            sink.check-interval="${sink.sinkCheckInterval?string("##0")}"
        </#if>
        <#if sink.sinkBufferSize != 0>
            sink.buffer-size="${sink.sinkBufferSize?string("##0")}"
        </#if>
        <#if sink.sinkBufferCount != 0>
            sink.buffer-count="${sink.sinkBufferCount?string("##0")}"
        </#if>
        <#if sink.dorisBatchSize != 0>
            doris.batch.size=${sink.dorisBatchSize?string("##0")}
        </#if>
        <#if sink.needsUnsupportedTypeCasting??>
            needs_unsupported_type_casting="${sink.needsUnsupportedTypeCasting}"
        </#if>
        <#if sink.dataSaveMode??>
            data_save_mode="${sink.dataSaveMode}"
        </#if>
        <#if sink.customSql??>
            custom_sql="${sink.customSql}"
        </#if>
        <#if sink.dorisConfig??>
            doris.config={
            <#list sink.dorisConfig?keys as key>
                ${key}=${sink.dorisConfig[key]}
            </#list>
            }
        </#if>
        }
    </#list>
</#if>
<#-- sink ossHuawei -->
<#if engine.ossHuaweiSinks?? && engine.ossHuaweiSinks?size != 0>
    <#list engine.ossHuaweiSinks as sink>
        ObsFile {
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        bucket="${sink.bucket}"
        access_key="${sink.accessKey}"
        security_key="${sink.accessSecret}"
        endpoint="${sink.endpoint}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.fileNameExpression??>
            custom_filename=true
            file_name_expression="${sink.fileNameExpression}"<#-- 有文件名字但是不选择合并的时候，文件后缀是跟的进程号 -->
            is_enable_transaction=false
        </#if>
        <#if sink.filenameTimeFormat??>
            filename_time_format="${sink.filenameTimeFormat}"
        </#if>
        <#if sink.fileFormatType??>
            file_format_type="${sink.fileFormatType}"
        </#if>
        <#if sink.fieldDelimiter??>
            field_delimiter="${sink.fieldDelimiter}"
        </#if>
        <#if sink.rowDelimiter??>
            row_delimiter="${sink.rowDelimiter}"
        </#if>
        <#if sink.dateFormat??>
            date_format="${sink.dateFormat}"
        </#if>
        <#if sink.datetimeFormat??>
            datetime_format="${sink.datetimeFormat}"
        </#if>
        <#if sink.timeFormat??>
            time_format="${sink.timeFormat}"
        </#if>
        <#if sink.havePartition??>
            have_partition="${sink.havePartition}"
        </#if>
        <#if sink.partitionBy??>
            partition_by=${sink.partitionBy}
        </#if>
        <#if sink.partitionDirExpression??>
            partition_dir_expression="${sink.partitionDirExpression}"
        </#if>
        <#if sink.isPartitionFieldWriteInFile??>
            is_partition_field_write_in_file="${sink.isPartitionFieldWriteInFile}"
        </#if>
        <#if sink.sinkColumns??>
            sink_columns=${sink.sinkColumns}
        </#if>
        <#if sink.batchSize??>
            batch_size="${sink.batchSize}"
        </#if>
        <#if sink.compressCodec??>
            compress_codec="${sink.compressCodec}"
        </#if>
        <#if sink.maxRowsInMemory??>
            max_rows_in_memory="${sink.maxRowsInMemory}"
        </#if>
        <#if sink.sheetName??>
            sheet_name="${sink.sheetName}"
        </#if>
        <#if sink.validates??>
            validates="${sink.validates}"
        </#if>
        <#if sink.validateFile??>
            validate_file="${sink.validateFile}"
        </#if>
        <#if sink.validateContent??>
            validate_content="${sink.validateContent}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        <#if sink.encoding??>
            encoding="${sink.encoding}"
        </#if>
        <#if sink.tdsqlPartitionName??>
            tdsql_partition_name="${sink.tdsqlPartitionName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink DataHub -->
<#if engine.dataHubSinks?? && engine.dataHubSinks?size != 0>
    <#list engine.dataHubSinks as sink>
        DataHub {
        endpoint="${sink.endpoint}"
        accessId="${sink.accessId}"
        accessKey="${sink.accessKey}"
        project="${sink.project}"
        topic="${sink.topic}"
        <#if sink.timeout??>
            timeout=${sink.timeout}
        </#if>
        <#if sink.retryTimes??>
            retryTimes=${sink.retryTimes}
        </#if>
        }
    </#list>
</#if>
<#-- sink elasticsearch -->
<#if engine.elasticsearchSinks?? && engine.elasticsearchSinks?size != 0>
    <#list engine.elasticsearchSinks as sink>
        Elasticsearch {
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
    <#-- hosts -->
        <#if sink.hosts??>
            hosts = ["${sink.hosts}"]
        </#if>
    <#-- index -->
        <#if sink.index??>
            index = "${sink.index}"
        </#if>
        <#if sink.indexType??>
            index_type = "${sink.indexType}"
        </#if>
    <#-- username -->
        <#if sink.username??>
            username = "${sink.username}"
        </#if>
    <#-- password -->
        <#if sink.password??>
            password = "${sink.password}"
        </#if>
    <#-- maxRetryCount -->
        <#if sink.maxRetryCount??>
            max_retry_count = "${sink.maxRetryCount}"
        </#if>
    <#-- maxBatchSize -->
        <#if sink.maxBatchSize??>
            max_batch_size = "${sink.maxBatchSize}"
        </#if>
    <#-- tlsVerifyCertificate -->
        <#if sink.tlsVerifyCertificate??>
            tls_verify_certificate = "${sink.tlsVerifyCertificate}"
        </#if>
    <#-- tlsVerifyHostname -->
        <#if sink.tlsVerifyHostname??>
            tls_verify_hostname = "${sink.tlsVerifyHostname}"
        </#if>
    <#-- schemaSaveMode -->
        <#if sink.schemaSaveMode??>
            schema_save_mode = "${sink.schemaSaveMode}"
        </#if>
    <#-- dataSaveMode -->
        <#if sink.dataSaveMode??>
            data_save_mode = "${sink.dataSaveMode}"
        </#if>
        }
    </#list>
</#if>
<#-- sink ArgoHdfsFile -->
<#if engine.argoHdfsFileSinks?? && engine.argoHdfsFileSinks?size != 0>
    <#list engine.argoHdfsFileSinks as sink>
        ArgoHdfsFile {
        <#if sink.defaultFS??>
            fs.defaultFS="${sink.defaultFS}"
        <#else>
            fs.defaultFS=""
        </#if>
        <#if sink.path??>
            path="${sink.path}"
        <#else>
            path=""
        </#if>
        file_format_type = "${sink.fileFormatType}"
        field_delimiter = "${sink.fieldDelimiter}"
        row_delimiter = "${sink.rowDelimiter}"
        is_enable_transaction = true
        argo_url="${sink.argoUrl}"
        argo_user="${sink.argoUser}"
        argo_password="${sink.argoPassword}"
        argo_schema="${sink.argoSchema}"
        argo_table="${sink.argoTable}"
        argo_tmp_table_name="${sink.argoTmpTableName}"
        <#if sink.argoTmpSchema??>
            argo_tmp_schema="${sink.argoTmpSchema}"
        </#if>
        <#if sink.hdfsSitePath??>
            hdfs_site_path="${sink.hdfsSitePath}"
        </#if>
        <#if sink.kerberosPrincipal??>
            kerberos_principal="${sink.kerberosPrincipal}"
        </#if>
        <#if sink.kerberosKeytabPath??>
            kerberos_keytab_path="${sink.kerberosKeytabPath}"
        </#if>
        <#if sink.krb5Path??>
            krb5_path="${sink.krb5Path}"
        </#if>
        <#if sink.emptyDataStrategy??>
            "empty_data_strategy"=${sink.emptyDataStrategy}
        <#else>
        </#if>
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        }
    </#list>
</#if>
<#-- sink hbaseSink -->
<#if engine.hbaseSinks?? && engine.hbaseSinks?size != 0>
    <#list engine.hbaseSinks as sink>
        Hbase {
        source_table_name="${sink.sourceTableName}"
        zookeeper_quorum = "${sink.zookeeperQuorum}"
        table = "${sink.tableName}"
        <#if sink.rowkeyColumn??>
            rowkey_column= ${sink.rowkeyColumn}
        <#else>
        </#if>
        <#if sink.rowkeyDelimiter??>
            rowkey_delimiter= "${sink.rowkeyDelimiter}"
        <#else>
        </#if>
    <#-- familyName -->
        <#if sink.familyName??>
            family_name {
            <#list sink.familyName as key>
                ${key}
            </#list>
            }
        </#if>
        <#if sink.versionColumn??>
            version_column= "${sink.versionColumn}"
        <#else>
        </#if>
        <#if sink.nullMode??>
            null_mode= "${sink.nullMode}"
        <#else>
        </#if>
        <#if sink.walWrite??>
            wal_write= "${sink.walWrite}"
        <#else>
        </#if>
        <#if sink.writeBufferSize??>
            write_buffer_size= "${sink.writeBufferSize}"
        <#else>
        </#if>
        <#if sink.encoding??>
            encoding= "${sink.encoding}"
        <#else>
        </#if>
        <#if sink.hbaseExtraConfig??>
            hbase_extra_config= "${sink.hbaseExtraConfig}"
        <#else>
        </#if>
        <#if sink.filePath??>
            file_path= "${sink.filePath}"
        <#else>
        </#if>
        <#if sink.user??>
            user= "${sink.user}"
        <#else>
        </#if>
        <#if sink.serverPrincipal??>
            server_principal= "${sink.serverPrincipal}"
        <#else>
        </#if>
        }
    </#list>
</#if>
<#-- sink Console -->
<#if engine.consoleHoleSinks?? && engine.consoleHoleSinks?size != 0>
    <#list engine.consoleHoleSinks as sink>
        ConsoleHole {
        source_table_name="${sink.sourceTableName}"
        }
    </#list>
</#if>
<#-- sink adb gpfdist -->
<#if engine.adbGpdistSinks?? && engine.adbGpdistSinks?size != 0>
    <#list engine.adbGpdistSinks as sink>
        ArgoAdbLocalFile{
        path="${sink.path}"
        file_format_type="${sink.fileFormatType}"
        delimiter="${sink.fieldDelimiter}"
        batch_size="10000000000"
        adb_url="${sink.adbUrl}"
        adb_driver="${sink.adbDriver}"
        adb_user="${sink.adbUser}"
        adb_password="${sink.adbPassword}"
        adb_database="${sink.schemaName}"
        adb_table="${sink.adbTable}"
        adb_gpfdist_address="${sink.adbGpfdistDddress}"
        adb_tmp_file_path="${sink.adbTmpFilePath}"
        adb_gpfdist_path="${sink.adbGpfdistPath}"
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.adbExternalTableName??>
            adb_external_table_name="${sink.adbExternalTableName}"
        </#if>
        <#if sink.adbExternalTableSchema??>
            adb_external_table_schema="${sink.adbExternalTableSchema}"
        </#if>
        }
    </#list>
</#if>
<#-- sink dws_gds -->
<#if engine.dwspgSinks?? && engine.dwspgSinks?size != 0>
    <#list engine.dwspgSinks as sink>
        Gds2DwsFile {
        path="${sink.path}"
        tmp_path="${sink.tmpPath}"
        file_format_type="${sink.fileFormatType}"
        field_delimiter="${sink.fieldDelimiter}"
        row_delimiter="${sink.rowDelimiter}"
        final_name="${sink.finalName}"
        batch_size ="${sink.batchSize}"
        dws_url="${sink.dwsUrl}"
        dws_driver="${sink.dwsDriver}"
        dws_user="${sink.dwsUser}"
        dws_password="${sink.dwsPassword}"
        dws_table="${sink.dwsTable}"
        dws_schema="${sink.dwsSchema}"
        dws_gds_address="${sink.dwsGdsAddress}"
        dws_fields=${sink.dwsFields}
        dws_clean_cache="${sink.dwsCleanCache}"
        }
    </#list>
</#if>
<#--  http base sink -->
<#if engine.httpBaseSinks?? && engine.httpBaseSinks?size != 0>
    <#list engine.httpBaseSinks as sink>
        HTTP {
        <#if sink.sourceTableName??>
            source_table_name="${sink.sourceTableName}"
        </#if>
        <#if sink.url??>
            url="${sink.url}"
        </#if>
        method="${sink.method}"
        params= {
        <#list sink.params?keys as key>
            ${key}="${sink.params[key]}"
        </#list>
        }
        headers = {
        <#list sink.headers?keys as key>
            ${key}="${sink.headers[key]}"
        </#list>
        }
        sink_output_columns = [
        <#list sink.sinkOutputColumns as nestedMap>
            {
            <#list nestedMap?keys as nestedKey>
                ${nestedKey}="${nestedMap[nestedKey]}"
            </#list>
            }
        </#list>
        ]
        body_send_type="${sink.bodySendType}"
        connect_timeout_ms = "${sink.connectTimeoutMs}"
        socket_timeout_ms = "${sink.connectTimeoutMs}"
        datetimeFormat = "${sink.datetimeFormat}"
        dateFormat ="${sink.dateFormat}"
        timeFormat ="${sink.timeFormat}"
        }
    </#list>
</#if>
<#-- sink MaxCompute -->
<#if engine.maxComputeSinks?? && engine.maxComputeSinks?size != 0>
    <#list engine.maxComputeSinks as sink>
        Maxcompute {
            accessId="${sink.accessId}"
            accessKey="${sink.accessKey}"
            endpoint="${sink.endpoint}"
            <#if sink.tunnelEndpoint?has_content>
            tunnel_endpoint="${sink.tunnelEndpoint}"
            </#if>
            project="${sink.project}"
            table_name="${sink.tableName}"
            <#if sink.partitionSpec?has_content>
            partition_spec="${sink.partitionSpec}"
            </#if>
        }
    </#list>
</#if>
}
