#!/bin/bash

# 测试脚本：验证 "text file busy" 问题修复
# 模拟海豚调度器创建和执行命令文件的过程

echo "=== Text File Busy 问题修复验证测试 ==="

# 创建测试目录
TEST_DIR="/tmp/dolphinscheduler_test_$(date +%s)"
mkdir -p "$TEST_DIR"
echo "测试目录: $TEST_DIR"

# 模拟海豚调度器创建的命令文件内容
create_test_command_file() {
    local file_path="$1"
    local content="$2"
    
    echo "创建命令文件: $file_path"
    
    # 模拟 ShellCommandExecutor.createCommandFileIfNotExists 的行为
    cat > "$file_path" << EOF
#!/bin/sh
BASEDIR=\$(cd \`dirname \$0\`; pwd)
cd \$BASEDIR
source /dsg/app/public/dolphinscheduler/worker-server/conf/dolphinscheduler_env.sh
$content
EOF
    
    # 设置执行权限
    chmod +x "$file_path"
    
    # 模拟 FileChannel.force(true) - 强制同步到磁盘
    sync
    
    echo "文件创建完成: $file_path"
}

# 模拟 ensureFileWriteComplete 方法
ensure_file_write_complete() {
    local file_path="$1"
    local max_retries=20
    local retry_count=0
    local file_ready=false
    
    echo "开始检查文件写入状态: $file_path"
    
    while [ $retry_count -lt $max_retries ] && [ "$file_ready" = "false" ]; do
        # 尝试以只读方式打开文件
        if [ -r "$file_path" ] && [ -x "$file_path" ]; then
            # 尝试读取文件内容
            if head -n 1 "$file_path" > /dev/null 2>&1; then
                file_ready=true
                echo "✅ 文件 $file_path 读取检查通过"
            else
                echo "⚠️  文件 $file_path 读取失败，重试 $((retry_count + 1))/$max_retries"
                retry_count=$((retry_count + 1))
                sleep 0.1
            fi
        else
            echo "⚠️  文件 $file_path 权限检查失败，重试 $((retry_count + 1))/$max_retries"
            retry_count=$((retry_count + 1))
            sleep 0.1
        fi
    done
    
    if [ "$file_ready" = "true" ]; then
        echo "✅ 文件写入完成检查通过"
        return 0
    else
        echo "❌ 等待文件写入完成超时"
        return 1
    fi
}

# 测试并发创建和执行
test_concurrent_execution() {
    echo ""
    echo "=== 测试并发创建和执行 ==="
    
    local test_count=5
    local pids=()
    
    for i in $(seq 1 $test_count); do
        (
            local cmd_file="$TEST_DIR/test_command_$i.sh"
            local content="/bin/bash /zl/A-999000/a1.sh
sleep 2
echo \"Test $i completed\""
            
            # 创建命令文件
            create_test_command_file "$cmd_file" "$content"
            
            # 确保文件写入完成
            if ensure_file_write_complete "$cmd_file"; then
                # 尝试执行
                echo "执行命令文件: $cmd_file"
                if bash "$cmd_file" > "$TEST_DIR/output_$i.log" 2>&1; then
                    echo "✅ 测试 $i 执行成功"
                else
                    echo "❌ 测试 $i 执行失败"
                    cat "$TEST_DIR/output_$i.log"
                fi
            else
                echo "❌ 测试 $i 文件检查失败"
            fi
        ) &
        pids+=($!)
    done
    
    # 等待所有测试完成
    echo "等待所有测试完成..."
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    echo "并发测试完成"
}

# 测试文件锁定情况
test_file_locking() {
    echo ""
    echo "=== 测试文件锁定情况 ==="
    
    local cmd_file="$TEST_DIR/lock_test.sh"
    local content="echo 'Lock test'"
    
    # 创建文件
    create_test_command_file "$cmd_file" "$content"
    
    # 模拟文件被锁定的情况
    echo "模拟文件锁定..."
    (
        # 在后台持续写入文件，模拟锁定
        while true; do
            echo "# Writing..." >> "$cmd_file"
            sleep 0.01
        done
    ) &
    local lock_pid=$!
    
    sleep 0.5  # 让锁定进程运行一会儿
    
    # 尝试检查文件状态
    echo "在文件锁定状态下检查文件..."
    if ensure_file_write_complete "$cmd_file"; then
        echo "✅ 即使在锁定状态下也能正确处理"
    else
        echo "⚠️  文件锁定状态下检查超时（这是预期的）"
    fi
    
    # 停止锁定进程
    kill $lock_pid 2>/dev/null
    wait $lock_pid 2>/dev/null
    
    # 再次检查
    echo "锁定解除后再次检查..."
    if ensure_file_write_complete "$cmd_file"; then
        echo "✅ 锁定解除后检查成功"
    else
        echo "❌ 锁定解除后检查仍然失败"
    fi
}

# 运行测试
echo "开始测试..."
test_concurrent_execution
test_file_locking

# 清理
echo ""
echo "清理测试文件..."
rm -rf "$TEST_DIR"

echo ""
echo "=== 测试总结 ==="
echo "1. 验证了文件创建和权限设置流程"
echo "2. 测试了 ensureFileWriteComplete 方法的重试机制"
echo "3. 模拟了并发执行场景"
echo "4. 测试了文件锁定情况的处理"
echo ""
echo "如果所有测试都通过，说明修复应该能解决 'text file busy' 问题"
echo "建议在实际环境中部署后观察日志中的详细信息"
