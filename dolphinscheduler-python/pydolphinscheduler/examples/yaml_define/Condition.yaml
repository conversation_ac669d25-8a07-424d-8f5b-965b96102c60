# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Define the workflow
workflow:
  name: "Condition"

# Define the tasks under the workflow
tasks:
  - { "task_type": "Shell", "name": "pre_task_1", "command": "echo pre_task_1" }
  - { "task_type": "Shell", "name": "pre_task_2", "command": "echo pre_task_2" }
  - { "task_type": "Shell", "name": "pre_task_3", "command": "echo pre_task_3" }
  - { "task_type": "Shell", "name": "success_branch", "command": "echo success_branch" }
  - { "task_type": "Shell", "name": "fail_branch", "command": "echo fail_branch" }

  - name: condition
    task_type: Condition
    success_task: success_branch
    failed_task: fail_branch
    op: AND
    groups:
      - op: AND
        groups:
          - task: pre_task_1
            flag: true
          - task: pre_task_2
            flag: true
          - task: pre_task_3
            flag: false
