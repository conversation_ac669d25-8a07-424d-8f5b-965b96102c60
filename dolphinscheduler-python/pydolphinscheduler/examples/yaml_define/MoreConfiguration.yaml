# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Define the workflow
workflow:
  name: "MoreConfiguration"
  param:
    n: 1

# Define the tasks under the workflow
tasks:
  - name: shell_0
    task_type: Shell
    description: "yaml define task"
    flag: "YES"
    command: |
      echo "$ENV{HOME}"
      echo "${n}"
    task_priority: "HIGH"
    delay_time: 20
    fail_retry_times: 30
    fail_retry_interval: 5
    timeout_flag: "CLOSE"
    timeout: 60
    local_params:
      - { "prop": "n", "direct": "IN", "type": "VARCHAR", "value": "${n}" }
