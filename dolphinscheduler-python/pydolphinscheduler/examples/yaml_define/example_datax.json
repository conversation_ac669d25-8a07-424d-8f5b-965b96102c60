{"job": {"content": [{"reader": {"name": "mysqlreader", "parameter": {"username": "usr", "password": "pwd", "column": ["id", "name", "code", "description"], "splitPk": "id", "connection": [{"table": ["source_table"], "jdbcUrl": ["*************************************"]}]}}, "writer": {"name": "mysqlwriter", "parameter": {"writeMode": "insert", "username": "usr", "password": "pwd", "column": ["id", "name"], "connection": [{"jdbcUrl": "*************************************", "table": ["target_table"]}]}}}], "setting": {"errorLimit": {"percentage": 0, "record": 0}, "speed": {"channel": 1, "record": 1000}}}}