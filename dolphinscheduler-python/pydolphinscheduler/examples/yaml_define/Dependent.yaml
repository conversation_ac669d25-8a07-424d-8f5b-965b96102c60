# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

workflow:
  name: "Dependent"

# Define the tasks under the workflow
tasks:
  - name: dependent
    task_type: Dependent
    denpendence:
    op: and
    groups:
      - op: or
        groups:
          - project_name: pydolphin
            process_definition_name: task_dependent_external
            dependent_task_name: task_1

          - project_name: pydolphin
            process_definition_name: task_dependent_external
            dependent_task_name: task_2

      - op: and
        groups:
          - project_name: pydolphin
            process_definition_name: task_dependent_external
            dependent_task_name: task_1
            dependent_date: LAST_WEDNESDAY 

          - project_name: pydolphin
            process_definition_name: task_dependent_external
            dependent_task_name: task_2
            dependent_date: last24Hours 

  - name: dependent_var
    task_type: Dependent
    denpendence:
    op: and
    groups:
      - op: or
        # we can use ${CONFIG.WORKFLOW_PROJECT} to set the value to configuration.WORKFLOW_PROJECT
        # we can use $WORKFLOW{"Dependent_External.yaml"} to create or update a workflow from dependent_external.yaml and set the value to that workflow name
        groups:
          - project_name: ${CONFIG.WORKFLOW_PROJECT} 
            process_definition_name: $WORKFLOW{"Dependent_External.yaml"} 
            dependent_task_name: task_1

          - project_name: ${CONFIG.WORKFLOW_PROJECT} 
            process_definition_name: $WORKFLOW{"Dependent_External.yaml"} 
            dependent_task_name: task_2
      - op: and
        groups:
          - project_name: ${CONFIG.WORKFLOW_PROJECT} 
            process_definition_name: $WORKFLOW{"Dependent_External.yaml"} 
            dependent_task_name: task_1
            dependent_date: LAST_WEDNESDAY 

          - project_name: ${CONFIG.WORKFLOW_PROJECT} 
            process_definition_name: $WORKFLOW{"Dependent_External.yaml"} 
            dependent_task_name: task_2
            dependent_date: last24Hours 
