# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

default_stages: [commit, push]
default_language_version:
  # force all python hooks to run python3
  python: python3
repos:
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
        name: isort (python)
  - repo: https://github.com/psf/black
    rev: 22.1.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        additional_dependencies: [
            'flake8-docstrings>=1.6',
            'flake8-black>=0.2',
        ]
        # pre-commit run in the root, so we have to point out the full path of configuration
        args: [
            --config, 
            dolphinscheduler-python/pydolphinscheduler/.flake8
        ]
  - repo: https://github.com/pycqa/autoflake
    rev: v1.4
    hooks:
      - id: autoflake
        args: [
            --remove-all-unused-imports,
            --ignore-init-module-imports,
            --in-place
        ]
