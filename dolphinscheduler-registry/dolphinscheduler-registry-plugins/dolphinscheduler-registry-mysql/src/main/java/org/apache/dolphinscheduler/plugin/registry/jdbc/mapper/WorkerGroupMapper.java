/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.plugin.registry.jdbc.mapper;

import org.apache.dolphinscheduler.plugin.registry.jdbc.model.WorkerGroup;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Worker Group Mapper
 */
@Mapper
public interface WorkerGroupMapper {

    /**
     * 插入 worker group
     */
    @Insert("INSERT INTO t_ds_worker_group (name, addr_list, create_time, update_time, description, other_params_json) " +
            "VALUES (#{name}, #{addrList}, #{createTime}, #{updateTime}, #{description}, #{otherParamsJson})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(WorkerGroup workerGroup);

    /**
     * 根据名称查询 worker group
     */
    @Select("SELECT * FROM t_ds_worker_group WHERE name = #{name}")
    WorkerGroup selectByName(@Param("name") String name);

    /**
     * 根据ID更新 worker group
     */
    @Update("UPDATE t_ds_worker_group SET addr_list = #{addrList}, update_time = #{updateTime} WHERE id = #{id}")
    int updateById(WorkerGroup workerGroup);

    /**
     * 根据名称删除 worker group
     */
    @Delete("DELETE FROM t_ds_worker_group WHERE name = #{name}")
    int deleteByName(@Param("name") String name);

    /**
     * 查询所有 worker group
     */
    @Select("SELECT * FROM t_ds_worker_group ORDER BY update_time DESC")
    List<WorkerGroup> selectAll();

    /**
     * 根据名称前缀查询
     */
    @Select("SELECT * FROM t_ds_worker_group WHERE name LIKE CONCAT(#{prefix}, '%')")
    List<WorkerGroup> selectByNamePrefix(@Param("prefix") String prefix);
}
