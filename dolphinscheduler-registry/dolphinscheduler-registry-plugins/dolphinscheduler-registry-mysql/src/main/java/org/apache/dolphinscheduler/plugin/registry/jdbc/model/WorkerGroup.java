/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.plugin.registry.jdbc.model;

import lombok.Data;

import java.util.Date;

/**
 * Worker Group 实体类
 */
@Data
public class WorkerGroup {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * worker group 名称
     */
    private String name;

    /**
     * worker 地址列表，用逗号分隔
     */
    private String addrList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 其他参数JSON
     */
    private String otherParamsJson;
}
