/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.plugin.registry.jdbc;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dolphinscheduler.common.utils.JSONUtils;
import org.apache.dolphinscheduler.common.model.WorkerHeartBeat;
import org.apache.dolphinscheduler.plugin.registry.jdbc.model.JdbcRegistryData;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Worker Group 自动管理器
 * 负责监听 worker 的上线/下线事件，自动维护 t_ds_worker_group 表
 */
@Slf4j
public class WorkerGroupAutoManager implements AutoCloseable {

    private final JdbcOperator jdbcOperator;
    private final JdbcRegistryProperties registryProperties;
    private final ScheduledExecutorService scheduledExecutorService;
    
    // 缓存当前活跃的 worker 信息：key=workerAddress, value=workerGroupName
    private final Map<String, String> activeWorkers = new ConcurrentHashMap<>();
    
    // 缓存当前的 worker group 信息：key=groupName, value=addressList
    private final Map<String, Set<String>> currentWorkerGroups = new ConcurrentHashMap<>();

    public WorkerGroupAutoManager(JdbcRegistryProperties registryProperties, JdbcOperator jdbcOperator) {
        this.registryProperties = registryProperties;
        this.jdbcOperator = jdbcOperator;
        this.scheduledExecutorService = Executors.newScheduledThreadPool(
                1,
                new ThreadFactoryBuilder().setNameFormat("WorkerGroupAutoManager").setDaemon(true).build());
    }

    public void start() {
        log.info("Starting WorkerGroupAutoManager...");
        
        // 启动定期检查任务，每30秒检查一次 worker 状态变化
        this.scheduledExecutorService.scheduleWithFixedDelay(
                this::checkWorkerStatusChange,
                10, // 初始延迟10秒
                30, // 每30秒执行一次
                TimeUnit.SECONDS);
        
        log.info("WorkerGroupAutoManager started");
    }

    /**
     * 检查 worker 状态变化
     */
    private void checkWorkerStatusChange() {
        try {
            log.debug("开始检查 worker 状态变化...");
            
            // 1. 获取当前注册中心中的所有 worker 信息
            Map<String, String> currentActiveWorkers = getCurrentActiveWorkers();
            
            // 2. 检查新上线的 worker
            for (Map.Entry<String, String> entry : currentActiveWorkers.entrySet()) {
                String workerAddress = entry.getKey();
                String workerGroupName = entry.getValue();
                
                if (!activeWorkers.containsKey(workerAddress)) {
                    // 新上线的 worker
                    handleWorkerOnline(workerAddress, workerGroupName);
                }
            }
            
            // 3. 检查下线的 worker
            Set<String> offlineWorkers = new HashSet<>(activeWorkers.keySet());
            offlineWorkers.removeAll(currentActiveWorkers.keySet());
            
            for (String workerAddress : offlineWorkers) {
                String workerGroupName = activeWorkers.get(workerAddress);
                handleWorkerOffline(workerAddress, workerGroupName);
            }
            
            // 4. 更新缓存
            activeWorkers.clear();
            activeWorkers.putAll(currentActiveWorkers);
            
            log.debug("worker 状态检查完成，当前活跃 worker 数量: {}", activeWorkers.size());
            
        } catch (Exception e) {
            log.error("检查 worker 状态变化时发生错误", e);
        }
    }

    /**
     * 获取当前注册中心中的所有活跃 worker
     */
    private Map<String, String> getCurrentActiveWorkers() throws SQLException {
        Map<String, String> workers = new HashMap<>();
        
        // 查询注册中心中所有 worker 相关的数据
        List<JdbcRegistryData> workerData = jdbcOperator.getWorkerRegistryData();
        
        for (JdbcRegistryData data : workerData) {
            try {
                // 解析 worker 心跳数据
                WorkerHeartBeat heartBeat = JSONUtils.parseObject(data.getDataValue(), WorkerHeartBeat.class);
                if (heartBeat != null) {
                    String workerAddress = heartBeat.getHost() + ":" + heartBeat.getPort();
                    String workerGroupName = extractWorkerGroupFromPath(data.getDataKey());
                    
                    if (workerGroupName != null) {
                        workers.put(workerAddress, workerGroupName);
                    }
                }
            } catch (Exception e) {
                log.warn("解析 worker 数据失败: {}", data.getDataKey(), e);
            }
        }
        
        return workers;
    }

    /**
     * 从注册路径中提取 worker group 名称
     * 路径格式: /dolphinscheduler/workers/groupName/host:port
     */
    private String extractWorkerGroupFromPath(String path) {
        if (path == null || !path.contains("/workers/")) {
            return null;
        }
        
        String[] parts = path.split("/");
        for (int i = 0; i < parts.length - 1; i++) {
            if ("workers".equals(parts[i]) && i + 1 < parts.length) {
                return parts[i + 1];
            }
        }
        return null;
    }

    /**
     * 处理 worker 上线事件
     */
    private void handleWorkerOnline(String workerAddress, String workerGroupName) {
        try {
            log.info("检测到 worker 上线: {} -> {}", workerAddress, workerGroupName);
            
            // 更新 worker group 的地址列表
            currentWorkerGroups.computeIfAbsent(workerGroupName, k -> new HashSet<>()).add(workerAddress);
            
            // 更新或插入 t_ds_worker_group 表
            updateWorkerGroupTable(workerGroupName, currentWorkerGroups.get(workerGroupName));
            
        } catch (Exception e) {
            log.error("处理 worker 上线事件失败: {} -> {}", workerAddress, workerGroupName, e);
        }
    }

    /**
     * 处理 worker 下线事件
     */
    private void handleWorkerOffline(String workerAddress, String workerGroupName) {
        try {
            log.info("检测到 worker 下线: {} -> {}", workerAddress, workerGroupName);
            
            // 从 worker group 的地址列表中移除
            Set<String> addresses = currentWorkerGroups.get(workerGroupName);
            if (addresses != null) {
                addresses.remove(workerAddress);
                
                if (addresses.isEmpty()) {
                    // 如果该 worker group 没有活跃的 worker，删除记录
                    deleteWorkerGroupTable(workerGroupName);
                    currentWorkerGroups.remove(workerGroupName);
                } else {
                    // 更新 worker group 的地址列表
                    updateWorkerGroupTable(workerGroupName, addresses);
                }
            }
            
        } catch (Exception e) {
            log.error("处理 worker 下线事件失败: {} -> {}", workerAddress, workerGroupName, e);
        }
    }

    /**
     * 更新 t_ds_worker_group 表
     */
    private void updateWorkerGroupTable(String groupName, Set<String> addresses) throws SQLException {
        String addrList = String.join(",", addresses);
        
        if (jdbcOperator.workerGroupExists(groupName)) {
            // 更新现有记录
            jdbcOperator.updateWorkerGroup(groupName, addrList);
            log.info("更新 worker group: {} -> {}", groupName, addrList);
        } else {
            // 插入新记录
            jdbcOperator.insertWorkerGroup(groupName, addrList, "Auto-generated by worker registry");
            log.info("创建 worker group: {} -> {}", groupName, addrList);
        }
    }

    /**
     * 删除 t_ds_worker_group 表记录
     */
    private void deleteWorkerGroupTable(String groupName) throws SQLException {
        jdbcOperator.deleteWorkerGroup(groupName);
        log.info("删除 worker group: {}", groupName);
    }

    @Override
    public void close() throws Exception {
        log.info("Closing WorkerGroupAutoManager...");
        scheduledExecutorService.shutdownNow();
        activeWorkers.clear();
        currentWorkerGroups.clear();
        log.info("WorkerGroupAutoManager closed");
    }
}
