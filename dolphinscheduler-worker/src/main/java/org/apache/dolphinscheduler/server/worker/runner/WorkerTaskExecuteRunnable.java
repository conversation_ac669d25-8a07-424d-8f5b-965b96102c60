/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.dolphinscheduler.server.worker.runner;

import com.dsg.database.datasource.utils.DatasourceUtils;
import com.google.common.base.Strings;
import com.joyadata.kafka.core.KafkaTemplate;
import groovy.lang.Tuple2;
import lombok.NonNull;
import org.apache.dolphinscheduler.common.Constants;
import org.apache.dolphinscheduler.common.enums.WarningType;
import org.apache.dolphinscheduler.common.storage.StorageOperate;
import org.apache.dolphinscheduler.common.utils.CommonUtils;
import org.apache.dolphinscheduler.common.utils.JSONUtils;
import org.apache.dolphinscheduler.common.utils.LoggerUtils;
import org.apache.dolphinscheduler.plugin.task.api.AbstractTask;
import org.apache.dolphinscheduler.plugin.task.api.TaskChannel;
import org.apache.dolphinscheduler.plugin.task.api.TaskConstants;
import org.apache.dolphinscheduler.plugin.task.api.TaskException;
import org.apache.dolphinscheduler.plugin.task.api.TaskExecutionContext;
import org.apache.dolphinscheduler.plugin.task.api.TaskExecutionContextCacheManager;
import org.apache.dolphinscheduler.plugin.task.api.TaskPluginException;
import org.apache.dolphinscheduler.plugin.task.api.enums.TaskExecutionStatus;
import org.apache.dolphinscheduler.plugin.task.api.model.TaskAlertInfo;
import org.apache.dolphinscheduler.remote.command.CommandType;
import org.apache.dolphinscheduler.server.utils.ProcessUtils;
import org.apache.dolphinscheduler.server.worker.config.WorkerConfig;
import org.apache.dolphinscheduler.server.worker.rpc.WorkerMessageSender;
import org.apache.dolphinscheduler.server.worker.utils.TaskExecutionCheckerUtils;
import org.apache.dolphinscheduler.service.alert.AlertClientService;
import org.apache.dolphinscheduler.service.task.TaskPluginManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.nio.file.NoSuchFileException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.dolphinscheduler.common.Constants.SINGLE_SLASH;

public abstract class WorkerTaskExecuteRunnable implements Runnable {

    protected final Logger logger = LoggerFactory
            .getLogger(String.format(TaskConstants.TASK_LOG_LOGGER_NAME_FORMAT, WorkerTaskExecuteRunnable.class));

    protected final TaskExecutionContext taskExecutionContext;
    protected final WorkerConfig workerConfig;
    protected final String masterAddress;
    protected final WorkerMessageSender workerMessageSender;
    protected final AlertClientService alertClientService;
    protected final TaskPluginManager taskPluginManager;
    protected final @Nullable
    StorageOperate storageOperate;
    protected final @Nullable
    KafkaTemplate kafkaTemplate;
    protected final @Nullable
    StringRedisTemplate redisTemplate;
    DataSource dataSource;
    RestTemplate restTemplate;
    protected @Nullable
    AbstractTask task;

    protected WorkerTaskExecuteRunnable(
            @NonNull TaskExecutionContext taskExecutionContext,
            @NonNull WorkerConfig workerConfig,
            @NonNull String masterAddress,
            @NonNull WorkerMessageSender workerMessageSender,
            @NonNull AlertClientService alertClientService,
            @NonNull TaskPluginManager taskPluginManager,
            @Nullable StorageOperate storageOperate,
            @Nullable KafkaTemplate kafkaTemplate,
            @Nullable StringRedisTemplate redisTemplate,
            @Nullable DataSource dataSource,
            @Nullable RestTemplate restTemplate) {
        this.taskExecutionContext = taskExecutionContext;
        this.workerConfig = workerConfig;
        this.masterAddress = masterAddress;
        this.workerMessageSender = workerMessageSender;
        this.alertClientService = alertClientService;
        this.taskPluginManager = taskPluginManager;
        this.storageOperate = storageOperate;
        this.kafkaTemplate = kafkaTemplate;
        this.redisTemplate = redisTemplate;
        this.dataSource = dataSource;
        this.restTemplate = restTemplate;
        String taskLogName = LoggerUtils.buildTaskId(taskExecutionContext.getFirstSubmitTime(),
                taskExecutionContext.getProcessDefineCode(),
                taskExecutionContext.getProcessDefineVersion(),
                taskExecutionContext.getProcessInstanceId(),
                taskExecutionContext.getTaskInstanceId());
        taskExecutionContext.setTaskLogName(taskLogName);
        logger.info("Set task logger name: {}", taskLogName);
    }

    //执行任务前发送消息
    protected void executeTask() {
        logger.info("开始执行任务..");
        if (task == null) {
            throw new TaskException("The current task instance is null");
        }
        String tenantCode = task.getTaskRequest().getTenantCode();
        new Thread(() -> {
            Map<String, String> data = new HashMap<>();
            data.put("taskName", taskExecutionContext.getTaskName());
            data.put("taskType", taskExecutionContext.getTaskType());
            data.put("taskId", String.valueOf(taskExecutionContext.getTaskCode()));
            data.put("processInstanceId", taskExecutionContext.getProcessInstanceId());
            data.put("processDefinitionId", String.valueOf(taskExecutionContext.getProcessDefineCode()));
            data.put("startTime", String.valueOf(System.currentTimeMillis()));
            Map<String, Object> msg = new HashMap<>();
            msg.put("eventCode", "B301006");//运行
            msg.put("tenantCode", tenantCode);
            msg.put("projectId", task.getTaskRequest().getProjectCode());
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            msg.put("date", df.format(new Date()));
            msg.put("businessId", taskExecutionContext.getTaskCode());
            msg.put("businessName", taskExecutionContext.getTaskName());
            msg.put("data", data);
            kafkaTemplate.send("APP_EVENTS_V1_R2P1", JSONUtils.toJsonString(msg));
        }).start();
        logger.info("开始执行任务...");
    }

    protected void afterExecute() throws TaskException {
        if (task == null) {
            throw new TaskException("The current task instance is null");
        }
        String tenantCode = task.getTaskRequest().getTenantCode();
        //执行后置sql
        Map<String, List<Tuple2<String, List<String>>>> postSqlMap = taskExecutionContext.getPostSqlMap();
        if (null != postSqlMap && !postSqlMap.isEmpty()) {
            postSqlMap.keySet().forEach(datasourceId -> {
                List<Tuple2<String, List<String>>> postSqlTupleList = postSqlMap.get(datasourceId);
                postSqlTupleList.forEach(postSqlTuple -> {
                    String configName = postSqlTuple.getV1();
                    List<String> postSqlList = postSqlTuple.getV2();
                    postSqlList.forEach(sql -> {
                        try {
                            DatasourceUtils.execSQL(datasourceId, sql, null, tenantCode);
                        } catch (SQLException e) {
                            logger.error("后置sql:" + configName + "组件执行后置sql失败，sql语句是{},错误信息是{}", sql, e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
                    logger.info("后置sql:" + configName + "组件后置sql执行成功");
                });
            });
        } else {
            logger.info("后置sql:无后置sql");
        }
        logger.info("任务执行完毕，开始后续发送邮件等操作");
        //2024-08-15，任务执行结束删除任务执行时记录的超时redis key
        try {
            redisTemplate.delete("dedp:scc:task:alarm:alarm:" + taskExecutionContext.getTaskInstanceId());
            redisTemplate.opsForZSet().remove("dedp:scc:task:alarm:alarm", taskExecutionContext.getTaskInstanceId());
            logger.info("删除任务执行时记录的超时完毕taskInstanceId={}", taskExecutionContext.getTaskInstanceId());
        } catch (Exception e) {
            logger.info("redis中超时redis key未删除掉，会发送告警消息taskInstanceId={},{}", taskExecutionContext.getTaskInstanceId(), e);
        }
        sendAlertIfNeeded();
        sendTaskResult();

        // 这里发送kafka消息，将任务状态发送出去
        int strategy = task.getExitStatus() == TaskExecutionStatus.SUCCESS ? WarningType.SUCCESS.getCode()
                : WarningType.FAILURE.getCode();
        if (strategy == 1) {
            logger.info("stx任务结束:任务执行成功");
        } else if (TaskExecutionStatus.KILL.equals(task.getExitStatus())){
            logger.error("stx任务结束:KILL");
        }else {
            logger.error("stx任务结束:任务执行失败");
        }

        new Thread(() -> {
            int code = 200;
            Map<String, String> data = new HashMap<>();
            String eventCode = "B301001";
            data.put("taskName", taskExecutionContext.getTaskName());
            data.put("taskType", taskExecutionContext.getTaskType());
            data.put("taskInstanceId", taskExecutionContext.getTaskInstanceId());
            if (strategy != 1) {
                code = 500;
                eventCode = "B301002";
                data.put("errorMsg", JSONUtils.toJsonString(task.getExitStatus()));
            } else {
                //记录位点
                if ("STX".equalsIgnoreCase(taskExecutionContext.getTaskType())) {
                    task.doSavePoint(redisTemplate, dataSource);
                }
            }
            Map<String, Object> msg = new HashMap<>();
            msg.put("eventCode", eventCode);
            msg.put("tenantCode", tenantCode);
            msg.put("projectId", task.getTaskRequest().getProjectCode());
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            msg.put("date", df.format(new Date()));
            msg.put("businessId", taskExecutionContext.getTaskCode());
            msg.put("businessName", taskExecutionContext.getTaskName());
            msg.put("data", data);
            kafkaTemplate.send("APP_EVENTS_V1_R2P1", JSONUtils.toJsonString(msg));
            logger.info("发送kafka消息内容是{}", JSONUtils.toJsonString(msg));
        }).start();
        TaskExecutionContextCacheManager.removeByTaskInstanceId(taskExecutionContext.getTaskInstanceId());
        logger.info("Remove the current task execute context from worker cache");
        //根据参数判断是否要清理执行后的文件
        clearTaskExecPathIfNeeded(workerConfig.getCleanTaskExecFile());
    }

    protected void afterThrowing(Throwable throwable) throws TaskException {
        cancelTask();
        TaskExecutionContextCacheManager.removeByTaskInstanceId(taskExecutionContext.getTaskInstanceId());
        taskExecutionContext.setCurrentExecutionStatus(TaskExecutionStatus.FAILURE);
        taskExecutionContext.setEndTime(new Date());
        workerMessageSender.sendMessageWithRetry(taskExecutionContext, masterAddress, CommandType.TASK_EXECUTE_RESULT);
        logger.info(
                "Get a exception when execute the task, will send the task execute result to master, the current task execute result is {}",
                TaskExecutionStatus.FAILURE);
        logger.info("FINALIZE_SESSION");
        // 这里发送kafka消息，将任务状态发送出去
        new Thread(() -> {
            Map<String, String> data = new HashMap<>();
            data.put("taskName", taskExecutionContext.getTaskName());
            data.put("taskType", taskExecutionContext.getTaskType());
            data.put("errorMsg", JSONUtils.toJsonString(task.getExitStatus()));
            Map<String, Object> msg = new HashMap<>();
            msg.put("eventCode", "B301002");
            msg.put("tenantCode", task.getTaskRequest().getTenantCode());
            msg.put("projectId", task.getTaskRequest().getProjectCode());
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            msg.put("date", df.format(new Date()));
            msg.put("businessId", taskExecutionContext.getTaskCode());
            msg.put("businessName", taskExecutionContext.getTaskName());
            msg.put("data", data);
            kafkaTemplate.send("APP_EVENTS_V1_R2P1", JSONUtils.toJsonString(msg));
            logger.info("发送kafka消息内容是{}", JSONUtils.toJsonString(msg));
        }).start();
    }

    public void cancelTask() {
        // cancel the task
        if (task != null) {
            try {
                task.cancelApplication(true);
                ProcessUtils.killYarnJob(taskExecutionContext);
            } catch (Exception e) {
                logger.error(
                        "Task execute failed and cancel the application failed, this will not affect the taskInstance status, but you need to check manual",
                        e);
            }
        }
    }

    @Override
    public void run() {
        try {
            // set the thread name to make sure the log be written to the task log file
            Thread.currentThread().setName(taskExecutionContext.getTaskLogName());
            /*if(taskExecutionContext.getTaskName().equals("sql任务")){
                System.out.println("0000000000");
            }*/
            LoggerUtils.setWorkflowAndTaskInstanceIDMDC(taskExecutionContext.getProcessInstanceId(),
                    taskExecutionContext.getTaskInstanceId());
            logger.info("Begin to pulling task");

            initializeTask();

            if (Constants.DRY_RUN_FLAG_YES == taskExecutionContext.getDryRun()) {
                taskExecutionContext.setCurrentExecutionStatus(TaskExecutionStatus.SUCCESS);
                taskExecutionContext.setEndTime(new Date());
                TaskExecutionContextCacheManager.removeByTaskInstanceId(taskExecutionContext.getTaskInstanceId());
                workerMessageSender.sendMessageWithRetry(taskExecutionContext, masterAddress,
                        CommandType.TASK_EXECUTE_RESULT);
                logger.info(
                        "The current execute mode is dry run, will stop the subsequent process and set the taskInstance status to success");
                return;
            }

            // 执行任务前 lihj
            beforeExecute();
            // 执行任务 lihj
            executeTask();
            // 执行任务后 lihj
            afterExecute();

        } catch (Throwable ex) {
            logger.error("Task execute failed, due to meet an exception", ex);
            afterThrowing(ex);
        } finally {
            LoggerUtils.removeWorkflowAndTaskInstanceIdMDC();
        }
    }

    protected void initializeTask() {
        logger.info("Begin to initialize task");

        Date taskStartTime = new Date();
        taskExecutionContext.setStartTime(taskStartTime);
        logger.info("Set task startTime: {}", taskStartTime);

        String systemEnvPath = CommonUtils.getSystemEnvPath();
        taskExecutionContext.setEnvFile(systemEnvPath);
        logger.info("Set task envFile: {}", systemEnvPath);

        String taskAppId = String.format("%s_%s", taskExecutionContext.getProcessInstanceId(),
                taskExecutionContext.getTaskInstanceId());
        taskExecutionContext.setTaskAppId(taskAppId);
        logger.info("Set task appId: {}", taskAppId);

        logger.info("End initialize task");
    }

    protected void beforeExecute() {
        taskExecutionContext.setCurrentExecutionStatus(TaskExecutionStatus.RUNNING_EXECUTION);
        workerMessageSender.sendMessageWithRetry(taskExecutionContext, masterAddress, CommandType.TASK_EXECUTE_RUNNING);
        logger.info("Set task status to {}", TaskExecutionStatus.RUNNING_EXECUTION);

        TaskExecutionCheckerUtils.checkTenantExist(workerConfig, taskExecutionContext);
        logger.info("TenantCode:{} check success", taskExecutionContext.getTenantCode());

        TaskExecutionCheckerUtils.createProcessLocalPathIfAbsent(taskExecutionContext);
        logger.info("ProcessExecDir:{} check success", taskExecutionContext.getExecutePath());

        TaskExecutionCheckerUtils.downloadResourcesIfNeeded(storageOperate, taskExecutionContext, logger);
        logger.info("Resources:{} check success", taskExecutionContext.getResources());

        TaskChannel taskChannel = taskPluginManager.getTaskChannelMap().get(taskExecutionContext.getTaskType());
        if (null == taskChannel) {
            throw new TaskPluginException(String.format("%s task plugin not found, please check config file.",
                    taskExecutionContext.getTaskType()));
        }
        task = taskChannel.createTask(taskExecutionContext);
        if (task == null) {
            throw new TaskPluginException(String.format("%s task is null, please check the task plugin is correct",
                    taskExecutionContext.getTaskType()));
        }
        logger.info("Task plugin: {} create success", taskExecutionContext.getTaskType());

        task.init();
        logger.info("Success initialized task plugin instance success");
        //seatunnel任务，组装参数，然后查询位点等内容，将位点存入redis
        String gatewayHttp = workerConfig.getGatewayHttp();
        String datasourcexHome = workerConfig.getDataSourcePluginPath();
        if ("STX".equalsIgnoreCase(taskExecutionContext.getTaskType())) {
            String home = workerConfig.getJoyadataEngineHome();
            String stxHome = workerConfig.getJoyadataStxHome();
            //Boolean createAlterTable = workerConfig.getCreateAlterTable();
            task.initCustomParam(redisTemplate, home, stxHome, gatewayHttp, dataSource, null, datasourcexHome);
            //这里可以获取到redis的信息
            logger.info("seatunnel组装参数");
        } else if ("SQL".equalsIgnoreCase(taskExecutionContext.getTaskType()) || "PROCEDURE".equalsIgnoreCase(taskExecutionContext.getTaskType())) {
            task.initCustomParam(redisTemplate, null, null, gatewayHttp, null, restTemplate, datasourcexHome);
        } else {
            task.initCustomParam(redisTemplate, null, null, null, null, null, null);
        }
        task.getParameters().setVarPool(taskExecutionContext.getVarPool());
        logger.info("localparam是{}", task.getParameters().getLocalParams());
        logger.info("Success set taskVarPool: {}", taskExecutionContext.getVarPool());

    }

    protected void sendAlertIfNeeded() {
        logger.info("开始判断是否需要告警邮件{}", task.getNeedAlert());
        if (!task.getNeedAlert()) {
            return;
        }
        logger.info("The current task need to send alert, begin to send alert");
        TaskExecutionStatus status = task.getExitStatus();
        TaskAlertInfo taskAlertInfo = task.getTaskAlertInfo();
        int strategy =
                status == TaskExecutionStatus.SUCCESS ? WarningType.SUCCESS.getCode() : WarningType.FAILURE.getCode();
        alertClientService.sendAlert(taskAlertInfo.getAlertGroupId(), taskAlertInfo.getTitle(),
                taskAlertInfo.getContent(), strategy);
        logger.info("Success send alert");
    }

    protected void sendTaskResult() {
        taskExecutionContext.setCurrentExecutionStatus(task.getExitStatus());
        taskExecutionContext.setEndTime(new Date());
        taskExecutionContext.setProcessId(task.getProcessId());
        taskExecutionContext.setAppIds(task.getAppIds());
        taskExecutionContext.setVarPool(JSONUtils.toJsonString(task.getParameters().getVarPool()));
        workerMessageSender.sendMessageWithRetry(taskExecutionContext, masterAddress, CommandType.TASK_EXECUTE_RESULT);

        logger.info("发送任务执行结果给master，当前任务状态是 {}", taskExecutionContext.getCurrentExecutionStatus());
    }

    protected void clearTaskExecPathIfNeeded(boolean cleanTaskExecFile) {

        String execLocalPath = taskExecutionContext.getExecutePath();
        if (!CommonUtils.isDevelopMode()) {
            logger.info("The current execute mode isn't develop mode, will clear the task execute file: {}",
                    execLocalPath);
            // get exec dir
            if (Strings.isNullOrEmpty(execLocalPath)) {
                logger.warn("The task execute file is {} no need to clear", taskExecutionContext.getTaskName());
                return;
            }

            if (SINGLE_SLASH.equals(execLocalPath)) {
                logger.warn("The task execute file is '/', direct deletion is not allowed");
                return;
            }
            // 屏蔽删除的文件
            if (cleanTaskExecFile) {
                try {
                    org.apache.commons.io.FileUtils.deleteDirectory(new File(execLocalPath));
                    logger.info("Success clear the task execute file: {}", execLocalPath);
                } catch (IOException e) {
                    if (e instanceof NoSuchFileException) {
                    } else {
                        logger.error("Delete task execute file: {} failed, this will not affect the task status, but you need to clear this manually", execLocalPath, e);
                    }
                }
            }

        } else {
            logger.info("The current execute mode is develop mode, will not clear the task execute file: {}",
                    execLocalPath);
        }
        logger.info("FINALIZE_SESSION");
    }

    public @NonNull TaskExecutionContext getTaskExecutionContext() {
        return taskExecutionContext;
    }

    public @Nullable
    AbstractTask getTask() {
        return task;
    }

}
