#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  banner:
    charset: UTF-8
  jackson:
    time-zone: UTC
    date-format: "yyyy-MM-dd HH:mm:ss"
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************
    username: root
    password: ENC(1CgAJmlG8HVz7ZQPOYLFnv4nEGvmauLzA0tVXmG0Bt+sbqLssbnc7Ovnc5uo4lOt)
    hikari:
      connection-test-query: select 1
      minimum-idle: 5
      auto-commit: true
      validation-timeout: 3000
      pool-name: DolphinScheduler
      maximum-pool-size: 50
      connection-timeout: 30000
      idle-timeout: 600000
      leak-detection-threshold: 0
      initialization-fail-timeout: 1
  kafka:
    bootstrap-servers: localhost:9092
    reconnect-backoff-ms: 5000
    reconnect-backoff-max-ms: 1000
    producer:
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      enable-jaas: false
      user-jaas: admin
      pwd-jaas: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-jaas: false
      user-jaas: admin
      pwd-jaas: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
  redis:
    cluster:
      nodes: **************:7361,**************:7362,**************:7363,**************:7364,**************:7365,**************:7366
    password: ENC(cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w)
    timeout: 6000
    lettuce:
      pool:
        max-idle: 10
        min-idle: 5
        max-active: 10
        max-wait: -1

application:
  yaml:
registry:
  type: jdbc
  term-refresh-interval: 1s
  term-expire-times: 10
  hikari-config:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************
    username: root
    password: ENC(1CgAJmlG8HVz7ZQPOYLFnv4nEGvmauLzA0tVXmG0Bt+sbqLssbnc7Ovnc5uo4lOt)
    maximum-pool-size: 5
    connection-timeout: 9000
    idle-timeout: 600000

worker:
  # worker listener port
  listen-port: 1234
  # worker execute thread number to limit task instances in parallel
  exec-threads: 100
  # worker heartbeat interval
  heartbeat-interval: 10s
  # worker host weight to dispatch tasks, default value 100
  host-weight: 100
  # tenant corresponds to the user of the system, which is used by the worker to submit the job. If system does not have this user, it will be automatically created after the parameter worker.tenant.auto.create is true.
  tenant-auto-create: true
  #Scenes to be used for distributed users.For example,users created by FreeIpa are stored in LDAP.This parameter only applies to Linux, When this parameter is true, worker.tenant.auto.create has no effect and will not automatically create tenants.
  tenant-distributed-user: false
  # worker max cpuload avg, only higher than the system cpu load average, worker server can be dispatched tasks. default value -1: the number of cpu cores * 2
  max-cpu-load-avg: -1
  # worker reserved memory, only lower than system available memory, worker server can be dispatched tasks. default value 0.3, the unit is G
  reserved-memory: 0.3
  # for multiple worker groups, use hyphen before group name, e.g.
  # groups:
  #   - default
  #   - group1
  #   - group2
  groups:
    - default
  # alert server listen host
  alert-listen-host: localhost
  alert-listen-port: 50052
  registry-disconnect-strategy:
    # The disconnect strategy: stop, waiting
    strategy: waiting
    # The max waiting time to reconnect to registry if you set the strategy to waiting
    max-waiting-time: 100s
  joyadata-engine-home: /dsg/app/public/dolphinscheduler
  joyadata-stx-home: D:\\DSG\\git_repo\\cindasc\\dolphinscheduler
  create-alter-table: true
  clean-task-exec-file: false
  data-source-plugin-path: d://datasourceX//pluginLibs
  gateway-http: http://nginx.dsg.com:8000

server:
  port: 1235

management:
  endpoints:
    web:
      exposure:
        include: 'none'
    enabled-by-default: false


metrics:
  enabled: true

jasypt:
  encryptor:
    # 加密算法
    algorithm: PBEWITHHMACSHA512ANDAES_256
    # 加密使用的盐
    password: NkVCQUQxMjBFQTI4QjY5NzVFQkYxRUNBRjEzMjc1Nzc=
