# Script

If you need to use `Shell script` for alerting, create an alert instance in the alert instance management and select the `Script` plugin.
The following shows the `Script` configuration example:

![dingtalk-plugin](../../../../img/alert/script-plugin.png)

## Parameter Configuration

| **Parameter** |                 **Description**                  |
|---------------|--------------------------------------------------|
| User Params   | User defined parameters will pass to the script. |
| Script Path   | The file location path in the server.            |
| Type          | Support `Shell` script.                          |

### Note

Consider the script file access privileges with the executing tenant.
