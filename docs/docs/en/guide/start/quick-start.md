# Quick Start

This is a Quick Start guide to help you get a basic idea of working with Apache DolphinScheduler. Once you've gone through the tutorial video, we encourage you to find out more about Apache DolphinScheduler functions and examples in .

<figure class="video_container"> 
  <iframe src="https://www.youtube.com/embed/nrF20hpCkug" frameborder="0" allowfullscreen="true"></iframe>
</figure>

## Administrator User Login

* Address: http://localhost:12345/dolphinscheduler
* Username and password: admin/dolphinscheduler123

![login](../../../../img/new_ui/dev/quick-start/login.png)

## Create a queue

![create-queue](../../../../img/new_ui/dev/quick-start/create-queue.png)

## Create a tenant

![create-tenant](../../../../img/new_ui/dev/quick-start/create-tenant.png)

## Create Ordinary Users

![create-user](../../../../img/new_ui/dev/quick-start/create-user.png)

## Create an alarm instance

![create-alarmInstance](../../../../img/new_ui/dev/quick-start/create-alarmInstance.png)

## Create an alarm group

![create-alarmGroup](../../../../img/new_ui/dev/quick-start/create-alarmGroup.png)

## Create a worker group

![create-workerGroup](../../../../img/new_ui/dev/quick-start/create-workerGroup.png)

## Create environment

![create-environment](../../../../img/new_ui/dev/quick-start/create-environment.png)

## Create a token

![create-token](../../../../img/new_ui/dev/quick-start/create-token.png)

## Login with regular users

Click on the user name in the upper right corner to "exit" and re-use the normal user login.

* `Project Management -> Create Project -> Project Name`

![project](../../../../img/new_ui/dev/quick-start/project.png)

* `Click Workflow Definition -> Create Workflow Definition-> Online Workflow Definition`

![workflow-definition](../../../../img/new_ui/dev/quick-start/workflow-definition.png)
* `Running Workflow Definition -> Click Workflow Instance -> Click Workflow Instance Name -> Double-click Task Node -> View Task Execution Log`

![log](../../../../img/new_ui/dev/quick-start/log.png)
