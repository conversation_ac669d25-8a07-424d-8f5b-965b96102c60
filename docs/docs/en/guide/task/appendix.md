# DolphinScheduler Task Parameters Appendix

DolphinScheduler task plugins share some common default parameters. Each type of task contains all or **some** default parameters as follows:

## Default Task Parameters

|      **Parameter**       |                                                                                                                                        **Description**                                                                                                                                         |
|--------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Node Name                | The name of the task. Node names within the same workflow must be unique.                                                                                                                                                                                                                      |
| Run Flag                 | Indicating whether to schedule the task. If you do not need to execute the task, you can turn on the `Prohibition execution` switch.                                                                                                                                                           |
| Description              | Describing the function of this node.                                                                                                                                                                                                                                                          |
| Task Priority            | When the number of the worker threads is insufficient, the worker executes task according to the priority. When two tasks have the same priority, the worker will execute them in `first come first served` fashion.                                                                           |
| Worker Group             | Machines which execute the tasks. If you choose `default`, scheduler will send the task to a random worker.                                                                                                                                                                                    |
| Task Group Name          | Resource group of tasks. It will not take effect if not configured.                                                                                                                                                                                                                            |
| Environment Name         | Environment to execute the task.                                                                                                                                                                                                                                                               |
| Number of Failed Retries | The number of task retries for failures. You could select it by drop-down menu or fill it manually.                                                                                                                                                                                            |
| Failure Retry Interval   | Interval of task retries for failures. You could select it by drop-down menu or fill it manually.                                                                                                                                                                                              |
| CPU Quota                | Assign the specified CPU time quota to the task executed. Takes a percentage value. Default -1 means unlimited. For example, the full CPU load of one core is 100%, and that of 16 cores is 1600%. You could configure it by [task.resource.limit.state](../../architecture/configuration.md). |
| Max Memory               | Assign the specified max memory to the task executed. Exceeding this limit will trigger oom to be killed and will not automatically retry. Takes an MB value. Default -1 means unlimited. You could configure it by [task.resource.limit.state](../../architecture/configuration.md).          |
| Timeout Alarm            | Alarm for task timeout. When the task exceeds the "timeout threshold", an alarm email will send.                                                                                                                                                                                               |
| Delayed Execution Time   | The time that a task delays for execution in minutes.                                                                                                                                                                                                                                          |
| Resources                | Resources which your task node uses.                                                                                                                                                                                                                                                           |
| Predecessor Task         | The upstream task of the current task node.                                                                                                                                                                                                                                                    |

