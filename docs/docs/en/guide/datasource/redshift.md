# Amazon Redshift

![Redshift datasource](../../../../img/new_ui/dev/datasource/redshift.png)

## Datasource Parameters

|     **Datasource**      |                       **Description**                       |
|-------------------------|-------------------------------------------------------------|
| Datasource              | Select Redshift.                                            |
| Datasource Name         | Enter the name of the datasource.                           |
| Description             | Enter a description of the datasource.                      |
| IP/Host Name            | Enter the Redshift service IP.                              |
| Port                    | Enter the Redshift service port.                            |
| Username                | Set the username for Redshift connection.                   |
| Password                | Set the password for Redshift connection.                   |
| Database Name           | Enter the database name of the Redshift connection.         |
| jdbc connect parameters | Parameter settings for Redshift connection, in JSON format. |

## Native Supported

Yes, could use this datasource by default.
