# MySQL

![mysql](../../../../img/new_ui/dev/datasource/mysql.png)

## Datasource Parameters

|       **Datasource**       |                     **Description**                      |
|----------------------------|----------------------------------------------------------|
| Datasource                 | Select MYSQL.                                            |
| Datasource name            | Enter the name of the DataSource.                        |
| Description                | Enter a description of the DataSource.                   |
| IP/Host Name               | Enter the MYSQL service IP.                              |
| Port                       | Enter the MYSQL service port.                            |
| Username                   | Set the username for MYSQL connection.                   |
| Password                   | Set the password for MYSQL connection.                   |
| Database name              | Enter the database name of the MYSQL connection.         |
| Jdbc connection parameters | Parameter settings for MYSQL connection, in JSON format. |

## Native Supported

No, read section example in [datasource-setting](../howto/datasource-setting.md) `DataSource Center` section to activate this datasource.

