# Features

## Simple to Use

- **Visual DAG**: User-friendly drag-and-drop workflow definition and facility for run-time control.
- **Modular Operation**: Modularity facilitates easy customization and maintenance.

## Rich Scenarios

- **Multiple Task Type Support**: Supports more than 10 task types, like Shell, MR, Spark, SQL, etc., with cross-language support making it easy to extend
- **Workflow Ops**: Workflow can be timed, paused, resumed and stopped, enabling easy maintenance and control of global and local parameters.

## High Reliability

- **Reliability**: Decentralized designs ensure stability. Self-supporting HA task queue to avoid overload fault tolerant capability. DolphinScheduler facilitates a highly robust environment.

## High Scalability

- **Scalability**: Supports multitenancy and online resource management. Stable operation of 100,000 data tasks per day is supported.

